2025-07-29 14:52:54,586 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:52:54,587 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:52:54,587 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 14:52:54,587 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:52:54,588 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 14:52:54,589 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:52:54,589 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:52:54,590 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:52:54,590 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:52:54,590 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:52:54,591 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:52:54,593 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:52:54,600 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_145254.log
2025-07-29 14:52:54,601 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:52:54,601 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:52:54,601 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:52:54,602 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 14:52:54,602 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 14:52:54,603 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 22:52:54
2025-07-29 14:52:54,603 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 14:52:54,603 - __main__ - INFO - 📅 启动时间: 2025-07-29 22:52:54
2025-07-29 14:52:54,603 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 14:52:54,604 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:52:55,144 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:52:55,144 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:52:55,675 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:52:55,676 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:52:55,678 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 14:52:55,678 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 14:52:55,679 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 14:52:55,679 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 14:52:55,679 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 14:52:56,512 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 14:52:56,512 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 14:52:56,513 - __main__ - INFO - 📋 待处理联系人数: 2942
2025-07-29 14:52:56,513 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 14:52:56,514 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2942
2025-07-29 14:52:56,514 - __main__ - INFO - 
============================================================
2025-07-29 14:52:56,514 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-29 14:52:56,515 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:52:56,516 - __main__ - INFO - 📊 全局进度：已处理 0/2942 个联系人
2025-07-29 14:52:56,516 - __main__ - INFO - ============================================================
2025-07-29 14:52:56,517 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 14:52:56,517 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:52:56,518 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:52:56,519 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 14:52:56,519 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:52:56,823 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:52:56,824 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:52:56,824 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:52:56,824 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:52:56,825 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:52:56,825 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:52:56,825 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:52:56,826 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:52:56,826 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:52:56,826 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:52:57,028 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:52:57,029 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:52:57,029 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:52:57,029 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:52:57,333 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:52:57,333 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:52:57,334 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:52:57,334 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:52:57,334 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:52:57,335 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:52:57,335 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:52:57,335 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:52:57,336 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:52:57,336 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:52:57,537 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:52:57,538 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:52:57,539 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:52:57,840 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:52:57,841 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:52:57,841 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:52:57,842 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:52:57,842 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:52:57,844 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:52:57,845 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:52:57,845 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:52:58,846 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:52:58,846 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 14:52:58,846 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:52:58,847 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:52:58,847 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:52:58,848 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:52:58,848 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:52:58,848 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:52:59,049 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:52:59,050 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:53:01,433 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:53:01,434 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:53:01,434 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 14:53:03,770 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:53:03,971 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:53:03,972 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:53:06,350 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:53:06,350 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:53:06,350 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-29 14:53:08,992 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:53:09,193 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:53:09,193 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:53:11,562 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:53:11,563 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:53:11,563 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-29 14:53:13,459 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:53:13,660 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:53:13,660 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:53:16,032 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:53:16,032 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:53:16,033 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-29 14:53:18,433 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:53:18,634 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:53:18,635 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:53:21,016 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:53:21,016 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:53:21,016 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:53:21,017 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:53:21,017 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:53:21,018 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:53:21,019 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:53:21,020 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:53:21,020 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:53:21,021 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4786682, 进程: Weixin.exe)
2025-07-29 14:53:21,023 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:53:21,024 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4786682)
2025-07-29 14:53:21,024 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4786682) - 增强版
2025-07-29 14:53:21,327 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:53:21,327 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:53:21,328 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:53:21,328 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:53:21,328 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:53:21,329 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:53:21,532 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:53:21,533 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:53:21,735 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:53:21,735 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:53:21,735 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:53:21,736 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:53:21,736 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:53:21,736 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:53:21,736 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:53:22,737 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:53:22,738 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:53:22,739 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:53:22,739 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:53:22,740 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:53:22,740 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:53:22,741 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4786682, 进程: Weixin.exe)
2025-07-29 14:53:22,743 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:53:22,744 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:53:22,744 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:53:22,744 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:53:22,745 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:53:22,746 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:53:23,052 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:53:23,053 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:53:23,053 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:53:23,053 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:53:23,054 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:53:23,054 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:53:23,054 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:53:23,055 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:53:23,055 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:53:23,055 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:53:23,257 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:53:23,257 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:53:23,259 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:53:23,560 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:53:23,560 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:53:23,560 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:53:24,561 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:53:24,562 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 14:53:24,562 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:53:24,565 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_145324.log
2025-07-29 14:53:24,565 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:53:24,566 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:53:24,566 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:53:24,566 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-29 14:53:24,567 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:53:24,567 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:53:24,569 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 14:53:24,569 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 14:53:24,570 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 14:53:24,570 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:53:24,570 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 14:53:24,571 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 14:53:24,571 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:53:24,572 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:53:24,572 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4786682
2025-07-29 14:53:24,572 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4786682) - 增强版
2025-07-29 14:53:24,880 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:53:24,881 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:53:24,881 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:53:24,881 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:53:24,882 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:53:24,882 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:53:24,882 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:53:24,883 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:53:25,085 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:53:25,085 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:53:25,089 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4786682 (API返回: None)
2025-07-29 14:53:25,390 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:53:25,390 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:53:25,390 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:53:25,391 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:53:25,392 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:53:25,392 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:53:25,393 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:53:25,397 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:53:25,397 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:53:25,876 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:53:25,876 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:53:26,115 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2942 个
2025-07-29 14:53:26,116 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2942 个 (总计: 3135 个)
2025-07-29 14:53:26,117 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:53:26,117 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:53:26,118 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:26,118 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:53:26,119 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2942
2025-07-29 14:53:26,119 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18827001037 (曹静)
2025-07-29 14:53:26,119 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:32,691 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18827001037
2025-07-29 14:53:32,692 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:53:32,692 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18827001037 执行添加朋友操作...
2025-07-29 14:53:32,692 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:53:32,692 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:53:32,693 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:53:32,694 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:53:32,699 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:53:32,700 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:53:32,700 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:53:32,701 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:53:32,701 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:53:32,701 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:53:32,701 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:53:32,702 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:53:32,705 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:53:32,706 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:53:32,707 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:53:32,710 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-29 14:53:32,711 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:53:33,213 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:53:33,214 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:53:33,280 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差45.65, 边缘比例0.0524
2025-07-29 14:53:33,288 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_145333.png
2025-07-29 14:53:33,289 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:53:33,290 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:53:33,291 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:53:33,293 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:53:33,294 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:53:33,299 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_145333.png
2025-07-29 14:53:33,300 - WeChatAutoAdd - INFO - 底部区域原始检测到 48 个轮廓
2025-07-29 14:53:33,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,453), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:33,301 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-29 14:53:33,303 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-29 14:53:33,304 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 14:53:33,305 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-29 14:53:33,306 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-29 14:53:33,307 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,279), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:53:33,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,277), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,276), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(166,275), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:53:33,310 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,273), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,311 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,273), 尺寸1x3, 长宽比0.33, 面积3
2025-07-29 14:53:33,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,271), 尺寸7x8, 长宽比0.88, 面积56
2025-07-29 14:53:33,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(166,269), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,269), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,315 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,266), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(258,265), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:33,317 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,264), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:53:33,318 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,263), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:33,319 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,260), 尺寸35x24, 长宽比1.46, 面积840
2025-07-29 14:53:33,320 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=72.1 (阈值:60)
2025-07-29 14:53:33,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-29 14:53:33,322 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:33,323 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(259,257), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:53:33,324 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-29 14:53:33,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,326 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,256), 尺寸1x3, 长宽比0.33, 面积3
2025-07-29 14:53:33,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,256), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:53:33,328 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:53:33,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:53:33,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,331 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(256,253), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 14:53:33,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,253), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:53:33,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:33,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,252), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,335 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,252), 尺寸2x7, 长宽比0.29, 面积14
2025-07-29 14:53:33,336 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 14:53:33,338 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=65.9 (阈值:60)
2025-07-29 14:53:33,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 14:53:33,341 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=65.8 (阈值:60)
2025-07-29 14:53:33,342 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸34x15, 长宽比2.27, 面积510
2025-07-29 14:53:33,343 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=110.7 (阈值:60)
2025-07-29 14:53:33,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 14:53:33,345 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=68.3 (阈值:60)
2025-07-29 14:53:33,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-29 14:53:33,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-29 14:53:33,349 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-29 14:53:33,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,241), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:53:33,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(140,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:53:33,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,236), 尺寸5x4, 长宽比1.25, 面积20
2025-07-29 14:53:33,355 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(178,231), 尺寸13x4, 长宽比3.25, 面积52
2025-07-29 14:53:33,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,231), 尺寸15x13, 长宽比1.15, 面积195
2025-07-29 14:53:33,362 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=105.0 (阈值:60)
2025-07-29 14:53:33,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,230), 尺寸56x14, 长宽比4.00, 面积784
2025-07-29 14:53:33,365 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=96.5 (阈值:60)
2025-07-29 14:53:33,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:53:33,367 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-07-29 14:53:33,368 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-29 14:53:33,369 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-29 14:53:33,370 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-29 14:53:33,379 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_145333.png
2025-07-29 14:53:33,381 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-29 14:53:33,382 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:53:33,684 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-29 14:53:34,451 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:53:34,452 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:53:34,453 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:34,453 - modules.wechat_auto_add_simple - INFO - ✅ 18827001037 添加朋友操作执行成功
2025-07-29 14:53:34,454 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:34,454 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:53:36,456 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:53:36,456 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:53:36,456 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:53:36,457 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:53:36,457 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:53:36,457 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:53:36,457 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:53:36,458 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:53:36,458 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:53:36,458 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18827001037
2025-07-29 14:53:36,462 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:53:36,463 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:53:36,463 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:53:36,464 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:53:36,464 - modules.friend_request_window - INFO -    📱 phone: '18827001037'
2025-07-29 14:53:36,465 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:53:36,465 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:53:36,943 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:53:36,943 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:53:36,943 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:53:36,944 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:53:36,945 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18827001037
2025-07-29 14:53:36,946 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:53:36,946 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:53:36,947 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:53:36,948 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:53:36,948 - modules.friend_request_window - INFO -    📱 手机号码: 18827001037
2025-07-29 14:53:36,948 - modules.friend_request_window - INFO -    🆔 准考证: 014325110099
2025-07-29 14:53:36,949 - modules.friend_request_window - INFO -    👤 姓名: 曹静
2025-07-29 14:53:36,949 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:53:36,949 - modules.friend_request_window - INFO -    📝 备注格式: '014325110099-曹静-2025-07-29 22:53:36'
2025-07-29 14:53:36,950 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:53:36,950 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110099-曹静-2025-07-29 22:53:36'
2025-07-29 14:53:36,950 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:53:36,954 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:53:36,954 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 14:53:36,955 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:36,956 - modules.wechat_auto_add_simple - INFO - ✅ 18827001037 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 14:53:36,956 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18827001037
2025-07-29 14:53:36,957 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:40,519 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2942
2025-07-29 14:53:40,520 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15207129351 (黄诚斌)
2025-07-29 14:53:40,520 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:47,088 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15207129351
2025-07-29 14:53:47,088 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:53:47,088 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15207129351 执行添加朋友操作...
2025-07-29 14:53:47,089 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:53:47,089 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:53:47,090 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:53:47,091 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:53:47,095 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-29 14:53:47,096 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:53:47,096 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:53:47,096 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:53:47,097 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:53:47,097 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:53:47,097 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:53:47,098 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:53:47,102 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:53:47,104 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:53:47,107 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:53:47,108 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-29 14:53:47,109 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:53:47,611 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:53:47,612 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:53:47,674 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差31.64, 边缘比例0.0462
2025-07-29 14:53:47,682 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_145347.png
2025-07-29 14:53:47,684 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:53:47,685 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:53:47,687 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:53:47,688 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:53:47,689 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:53:47,694 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_145347.png
2025-07-29 14:53:47,695 - WeChatAutoAdd - INFO - 底部区域原始检测到 33 个轮廓
2025-07-29 14:53:47,696 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,453), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,697 - WeChatAutoAdd - INFO - 重要轮廓: 位置(117,236), 尺寸93x31, 长宽比3.00, 已知特征:False
2025-07-29 14:53:47,699 - WeChatAutoAdd - INFO - 发现目标候选: 位置(117,236), 尺寸93x31
2025-07-29 14:53:47,700 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(306,209), 尺寸1x86, 长宽比0.01, 面积86
2025-07-29 14:53:47,701 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,702 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,703 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,704 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,706 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-29 14:53:47,707 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,708 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,709 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,710 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,209), 尺寸7x1, 长宽比7.00, 面积7
2025-07-29 14:53:47,711 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(190,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,712 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,713 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-29 14:53:47,715 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,717 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,209), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:53:47,718 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,719 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,209), 尺寸10x1, 长宽比10.00, 面积10
2025-07-29 14:53:47,721 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,722 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,723 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(118,209), 尺寸5x1, 长宽比5.00, 面积5
2025-07-29 14:53:47,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,209), 尺寸11x1, 长宽比11.00, 面积11
2025-07-29 14:53:47,725 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(100,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,726 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(94,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:53:47,727 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,728 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:53:47,731 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,209), 尺寸6x1, 长宽比6.00, 面积6
2025-07-29 14:53:47,732 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(23,209), 尺寸282x168, 长宽比1.68, 面积47376
2025-07-29 14:53:47,733 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,209), 尺寸2x88, 长宽比0.02, 面积176
2025-07-29 14:53:47,734 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:53:47,736 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:53:47,737 - WeChatAutoAdd - INFO - 选择目标候选按钮: Y=236, 符合'添加到通讯录'特征
2025-07-29 14:53:47,738 - WeChatAutoAdd - INFO - 在底部找到按钮: (163, 251), 尺寸: 93x31, 位置得分: 1.0, 目标候选: True, 绿色按钮: False, 特殊位置: False
2025-07-29 14:53:47,739 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-29 14:53:47,748 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_145347.png
2025-07-29 14:53:47,749 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-29 14:53:47,750 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:53:48,051 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(163, 251) -> 屏幕坐标(1363, 251)
2025-07-29 14:53:48,832 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:53:48,833 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:53:48,834 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:48,834 - modules.wechat_auto_add_simple - INFO - ✅ 15207129351 添加朋友操作执行成功
2025-07-29 14:53:48,834 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:48,834 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:53:50,836 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:53:50,836 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:53:50,836 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:53:50,837 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:53:50,837 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:53:50,837 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:53:50,837 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:53:50,838 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:53:50,838 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:53:50,838 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15207129351
2025-07-29 14:53:50,839 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:53:50,839 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:53:50,839 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:53:50,840 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:53:50,840 - modules.friend_request_window - INFO -    📱 phone: '15207129351'
2025-07-29 14:53:50,840 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:53:50,841 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:53:51,368 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:53:51,368 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:53:51,369 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:53:51,369 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:53:51,370 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15207129351
2025-07-29 14:53:51,371 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:53:51,371 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:53:51,372 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:53:51,372 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:53:51,372 - modules.friend_request_window - INFO -    📱 手机号码: 15207129351
2025-07-29 14:53:51,372 - modules.friend_request_window - INFO -    🆔 准考证: 014325110100
2025-07-29 14:53:51,373 - modules.friend_request_window - INFO -    👤 姓名: 黄诚斌
2025-07-29 14:53:51,373 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:53:51,374 - modules.friend_request_window - INFO -    📝 备注格式: '014325110100-黄诚斌-2025-07-29 22:53:51'
2025-07-29 14:53:51,374 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:53:51,374 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110100-黄诚斌-2025-07-29 22:53:51'
2025-07-29 14:53:51,375 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:53:51,377 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:53:51,377 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 14:53:51,378 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:51,378 - modules.wechat_auto_add_simple - INFO - ✅ 15207129351 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 14:53:51,379 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15207129351
2025-07-29 14:53:51,380 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:53:52,819 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:53:52,820 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:53:52,820 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:53:52,821 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:53:52,822 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:53:52,822 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:53:52,822 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:53:52,823 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:53:52,823 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:53:52,823 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 14:53:52,824 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:53:52,824 - __main__ - INFO - � 更新全局进度：已处理 2/2942 个联系人
2025-07-29 14:53:52,825 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:53:55,825 - __main__ - INFO - 
============================================================
2025-07-29 14:53:55,826 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-29 14:53:55,826 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:53:55,826 - __main__ - INFO - 📊 全局进度：已处理 2/2942 个联系人
2025-07-29 14:53:55,827 - __main__ - INFO - ============================================================
2025-07-29 14:53:55,827 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 14:53:55,827 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:53:55,828 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:53:55,828 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 14:53:55,828 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:53:56,149 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:53:56,149 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:53:56,150 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:53:56,150 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:53:56,150 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:53:56,151 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:53:56,151 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:53:56,151 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:53:56,152 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:53:56,152 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:53:56,353 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:53:56,354 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:53:56,354 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:53:56,354 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:53:56,658 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:53:56,659 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:53:56,659 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:53:56,660 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:53:56,660 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:53:56,660 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:53:56,661 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:53:56,661 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:53:56,661 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:53:56,662 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:53:56,864 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:53:56,864 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:53:56,866 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:53:57,166 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:53:57,167 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:53:57,167 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:53:57,168 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:53:57,168 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:53:57,168 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:53:57,168 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:53:57,169 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:53:58,169 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:53:58,170 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 14:53:58,170 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:53:58,170 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:53:58,171 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:53:58,171 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:53:58,171 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:53:58,171 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:53:58,372 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:53:58,373 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:54:00,746 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:54:00,747 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:54:00,747 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-29 14:54:02,921 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:54:03,121 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:54:03,122 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:54:05,490 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:54:05,491 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:54:05,491 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 14:54:07,313 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:54:07,514 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:54:07,514 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:54:09,895 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:54:09,895 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:54:09,896 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-29 14:54:12,704 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:54:12,904 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:54:12,905 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:54:15,282 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:54:15,282 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:54:15,283 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-29 14:54:17,805 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:54:18,006 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:54:18,006 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:54:20,378 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:54:20,378 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:54:20,379 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:54:20,379 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:54:20,379 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:54:20,381 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:54:20,381 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:54:20,382 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4786682, 进程: Weixin.exe)
2025-07-29 14:54:20,383 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:54:20,383 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:54:20,384 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4066504, 进程: Weixin.exe)
2025-07-29 14:54:20,385 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 14:54:20,386 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4066504)
2025-07-29 14:54:20,386 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4066504) - 增强版
2025-07-29 14:54:20,689 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:54:20,689 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:54:20,690 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:54:20,690 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:54:20,690 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:54:20,691 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:54:20,893 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:54:20,894 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:54:21,095 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:54:21,096 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:54:21,096 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:54:21,096 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:54:21,096 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:54:21,097 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:54:21,097 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:54:22,097 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:54:22,098 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:54:22,099 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:54:22,099 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:54:22,100 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4786682, 进程: Weixin.exe)
2025-07-29 14:54:22,101 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:54:22,101 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:54:22,102 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4066504, 进程: Weixin.exe)
2025-07-29 14:54:22,105 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 14:54:22,106 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:54:22,107 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:54:22,108 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:54:22,109 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:54:22,113 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:54:22,422 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:54:22,423 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:54:22,423 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:54:22,424 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:54:22,424 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:54:22,424 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:54:22,424 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:54:22,425 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:54:22,425 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:54:22,425 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:54:22,627 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:54:22,628 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:54:22,629 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:54:22,930 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:54:22,930 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:54:22,930 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:54:23,931 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:54:23,931 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-29 14:54:23,932 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:54:23,934 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_145423.log
2025-07-29 14:54:23,935 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:54:23,936 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:54:23,936 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:54:23,936 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-29 14:54:23,936 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:54:23,937 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:54:23,939 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-29 14:54:23,939 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-29 14:54:23,940 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 14:54:23,940 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:54:23,940 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 14:54:23,941 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-29 14:54:23,942 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-29 14:54:23,942 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:54:23,945 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:54:23,945 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4066504
2025-07-29 14:54:23,946 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4066504) - 增强版
2025-07-29 14:54:24,256 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:54:24,256 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:54:24,257 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:54:24,257 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:54:24,257 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:54:24,258 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:54:24,258 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:54:24,258 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:54:24,460 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:54:24,461 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:54:24,467 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4066504 (API返回: None)
2025-07-29 14:54:24,767 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:54:24,768 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:54:24,768 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:54:24,768 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:54:24,769 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:54:24,770 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:54:24,770 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:54:24,774 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:54:24,774 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:54:25,261 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:54:25,262 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:54:25,506 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2940 个
2025-07-29 14:54:25,507 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-29 14:54:25,507 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2938 个
2025-07-29 14:54:25,507 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2938 个 (总计: 3135 个)
2025-07-29 14:54:25,508 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:54:25,508 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:54:25,508 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:54:25,509 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:54:25,509 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2938
2025-07-29 14:54:25,509 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15019004476 (王硕)
2025-07-29 14:54:25,510 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:54:32,092 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15019004476
2025-07-29 14:54:32,093 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:54:32,094 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15019004476 执行添加朋友操作...
2025-07-29 14:54:32,094 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:54:32,094 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:54:32,095 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:54:32,096 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:54:32,102 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-29 14:54:32,103 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:54:32,104 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:54:32,104 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:54:32,104 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:54:32,105 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:54:32,105 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:54:32,105 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:54:32,110 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:54:32,113 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:54:32,115 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:54:32,120 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:54:32,121 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:54:32,125 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 14:54:32,129 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:54:32,633 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:54:32,634 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:54:32,695 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.56, 边缘比例0.0379
2025-07-29 14:54:32,702 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_145432.png
2025-07-29 14:54:32,705 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:54:32,706 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:54:32,708 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:54:32,712 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:54:32,713 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:54:32,723 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_145432.png
2025-07-29 14:54:32,725 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:54:32,729 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:54:32,731 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:54:32,737 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:54:32,738 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:54:32,740 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:54:32,743 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:54:32,745 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:54:32,755 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_145432.png
2025-07-29 14:54:32,761 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:54:32,763 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:54:32,769 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_145432.png
2025-07-29 14:54:32,832 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:54:32,834 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:54:32,836 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:54:32,837 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:54:33,139 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:54:33,909 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:54:33,910 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:54:33,912 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:54:33,912 - modules.wechat_auto_add_simple - INFO - ✅ 15019004476 添加朋友操作执行成功
2025-07-29 14:54:33,912 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:54:33,913 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:54:35,914 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:54:35,915 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:54:35,915 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:54:35,915 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:54:35,916 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:54:35,916 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:54:35,916 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:54:35,916 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:54:35,917 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:54:35,917 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15019004476
2025-07-29 14:54:35,918 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:54:35,918 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:54:35,918 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:54:35,919 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:54:35,919 - modules.friend_request_window - INFO -    📱 phone: '15019004476'
2025-07-29 14:54:35,920 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:54:35,922 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:54:36,665 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:54:36,666 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:54:36,666 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:54:36,667 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:54:36,668 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15019004476
2025-07-29 14:54:36,672 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:54:36,672 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:54:36,673 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:54:36,673 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:54:36,678 - modules.friend_request_window - INFO -    📱 手机号码: 15019004476
2025-07-29 14:54:36,679 - modules.friend_request_window - INFO -    🆔 准考证: 014325110113
2025-07-29 14:54:36,680 - modules.friend_request_window - INFO -    👤 姓名: 王硕
2025-07-29 14:54:36,681 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:54:36,682 - modules.friend_request_window - INFO -    📝 备注格式: '014325110113-王硕-2025-07-29 22:54:36'
2025-07-29 14:54:36,682 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:54:36,683 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110113-王硕-2025-07-29 22:54:36'
2025-07-29 14:54:36,687 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:54:36,695 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5114052, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:54:36,697 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5114052)
2025-07-29 14:54:36,796 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:54:36,828 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:54:36,900 - modules.friend_request_window - INFO - 🔄 激活窗口: 5114052
2025-07-29 14:54:37,607 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:54:37,609 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:54:37,610 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:54:37,610 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:54:37,611 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:54:37,611 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:54:37,611 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:54:37,611 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:54:37,612 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:54:37,612 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:54:37,612 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:54:37,612 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:54:37,613 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:54:37,613 - modules.friend_request_window - INFO -    📝 remark参数: '014325110113-王硕-2025-07-29 22:54:36' (类型: <class 'str'>, 长度: 35)
2025-07-29 14:54:37,613 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:54:37,613 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110113-王硕-2025-07-29 22:54:36'
2025-07-29 14:54:37,614 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:54:37,614 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:54:37,615 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:54:37,615 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:54:37,616 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:54:37,616 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:54:37,616 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:54:38,528 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
