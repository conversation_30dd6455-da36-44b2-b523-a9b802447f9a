2025-07-29 14:30:44,134 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:30:44,135 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:30:44,135 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 14:30:44,136 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:30:44,137 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 14:30:44,137 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:30:44,137 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:30:44,138 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:30:44,138 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:30:44,139 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:30:44,139 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:30:44,142 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:30:44,145 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_143044.log
2025-07-29 14:30:44,148 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:30:44,149 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:30:44,150 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:30:44,150 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 14:30:44,151 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 14:30:44,151 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 22:30:44
2025-07-29 14:30:44,152 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 14:30:44,152 - __main__ - INFO - 📅 启动时间: 2025-07-29 22:30:44
2025-07-29 14:30:44,152 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 14:30:44,154 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:30:44,685 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:30:44,686 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:30:45,210 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:30:45,210 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:30:45,213 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 14:30:45,213 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 14:30:45,214 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 14:30:45,214 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 14:30:45,215 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 14:30:45,891 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 14:30:45,892 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 14:30:45,892 - __main__ - INFO - 📋 待处理联系人数: 2946
2025-07-29 14:30:45,892 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 14:30:45,893 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2946
2025-07-29 14:30:45,893 - __main__ - INFO - 
============================================================
2025-07-29 14:30:45,893 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-29 14:30:45,893 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:30:45,894 - __main__ - INFO - 📊 全局进度：已处理 0/2946 个联系人
2025-07-29 14:30:45,894 - __main__ - INFO - ============================================================
2025-07-29 14:30:45,894 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 14:30:45,895 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:30:45,895 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:30:45,895 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 14:30:45,897 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:30:46,217 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:30:46,218 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:30:46,218 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:30:46,219 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:30:46,219 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:30:46,219 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:30:46,220 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:30:46,220 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:30:46,220 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:30:46,220 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:30:46,422 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:30:46,422 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:30:46,423 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:30:46,423 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:30:46,726 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:30:46,727 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:30:46,727 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:30:46,728 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:30:46,729 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:30:46,730 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:30:46,730 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:30:46,730 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:30:46,731 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:30:46,731 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:30:46,933 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:30:46,933 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:30:46,935 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:30:47,236 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:30:47,236 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:30:47,236 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:30:47,237 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:30:47,237 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:30:47,237 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:30:47,238 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:30:47,238 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:30:48,239 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:30:48,240 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 14:30:48,240 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:30:48,241 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:30:48,241 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:30:48,241 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:30:48,241 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:30:48,242 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:30:48,443 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:30:48,444 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:30:50,816 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:30:50,816 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:30:50,816 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-29 14:30:53,422 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:30:53,623 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:30:53,623 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:30:55,992 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:30:55,992 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:30:55,992 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 14:30:57,746 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:30:57,947 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:30:57,947 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:31:00,333 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:31:00,334 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:31:00,334 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-29 14:31:02,453 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:31:02,653 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:31:02,654 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:31:05,032 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:31:05,032 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:31:05,033 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-29 14:31:07,508 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:31:07,709 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:31:07,710 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:31:10,081 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:31:10,082 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:31:10,082 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:31:10,082 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:31:10,082 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:31:10,085 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:31:10,086 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:31:10,086 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:31:10,087 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:31:10,088 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5375160, 进程: Weixin.exe)
2025-07-29 14:31:10,091 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:31:10,092 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 5375160)
2025-07-29 14:31:10,093 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5375160) - 增强版
2025-07-29 14:31:10,397 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:31:10,397 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:31:10,398 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:31:10,398 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:31:10,398 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:31:10,399 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:31:10,602 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:31:10,602 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:31:10,805 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:31:10,805 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:31:10,805 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:31:10,806 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:31:10,806 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:31:10,806 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:31:10,807 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:31:11,807 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:31:11,808 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:31:11,809 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:31:11,810 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:31:11,811 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:31:11,811 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:31:11,813 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5375160, 进程: Weixin.exe)
2025-07-29 14:31:11,815 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:31:11,816 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:31:11,816 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:31:11,816 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:31:11,817 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:31:11,817 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:31:12,124 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:31:12,124 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:31:12,124 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:31:12,125 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:31:12,125 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:31:12,125 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:31:12,126 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:31:12,126 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:31:12,126 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:31:12,126 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:31:12,328 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:31:12,328 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:31:12,330 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:31:12,630 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:31:12,631 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:31:12,631 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:31:13,632 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:31:13,632 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 14:31:13,633 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:31:13,636 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_143113.log
2025-07-29 14:31:13,637 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:31:13,637 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:31:13,638 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:31:13,638 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-29 14:31:13,638 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:31:13,639 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:31:13,641 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 14:31:13,641 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 14:31:13,642 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 14:31:13,642 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:31:13,642 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 14:31:13,642 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 14:31:13,643 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:31:13,644 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:31:13,645 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 5375160
2025-07-29 14:31:13,645 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5375160) - 增强版
2025-07-29 14:31:13,953 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:31:13,954 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:31:13,954 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:31:13,955 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:31:13,955 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:31:13,955 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:31:13,956 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:31:13,956 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:31:14,158 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:31:14,159 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:31:14,163 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 5375160 (API返回: None)
2025-07-29 14:31:14,463 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:31:14,464 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:31:14,464 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:31:14,465 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:31:14,465 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:31:14,466 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:31:14,466 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:31:14,470 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:31:14,470 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:31:14,762 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:31:14,762 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:31:15,017 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2946 个
2025-07-29 14:31:15,018 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2946 个 (总计: 3135 个)
2025-07-29 14:31:15,019 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:31:15,019 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:31:15,020 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:31:15,021 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:31:15,021 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2946
2025-07-29 14:31:15,021 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18319407261 (朱惠登)
2025-07-29 14:31:15,022 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:31:21,591 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18319407261
2025-07-29 14:31:21,591 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:31:21,592 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18319407261 执行添加朋友操作...
2025-07-29 14:31:21,592 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:31:21,592 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:31:21,593 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:31:21,594 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:31:21,599 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:31:21,600 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:31:21,600 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:31:21,601 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:31:21,601 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:31:21,602 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:31:21,602 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:31:21,603 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:31:21,615 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:31:21,618 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:31:21,619 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:31:21,621 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:31:21,626 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 14:31:21,629 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:31:22,131 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:31:22,132 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:31:22,207 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.94, 边缘比例0.0373
2025-07-29 14:31:22,215 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_143122.png
2025-07-29 14:31:22,218 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:31:22,219 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:31:22,220 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:31:22,221 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:31:22,228 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:31:22,234 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_143122.png
2025-07-29 14:31:22,236 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:31:22,239 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:31:22,241 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:31:22,242 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:31:22,243 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:31:22,245 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:31:22,247 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:31:22,249 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:31:22,257 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_143122.png
2025-07-29 14:31:22,261 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:31:22,263 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:31:22,267 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_143122.png
2025-07-29 14:31:22,326 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:31:22,327 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:31:22,329 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:31:22,330 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:31:22,632 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:31:23,399 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:31:23,400 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:31:23,402 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:31:23,402 - modules.wechat_auto_add_simple - INFO - ✅ 18319407261 添加朋友操作执行成功
2025-07-29 14:31:23,402 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:31:23,403 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:31:25,405 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:31:25,405 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:31:25,405 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:31:25,406 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:31:25,406 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:31:25,406 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:31:25,407 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:31:25,407 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:31:25,407 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:31:25,408 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18319407261
2025-07-29 14:31:25,413 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:31:25,413 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:31:25,414 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:31:25,414 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:31:25,414 - modules.friend_request_window - INFO -    📱 phone: '18319407261'
2025-07-29 14:31:25,415 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:31:25,415 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:31:25,736 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:31:25,737 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:31:25,737 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:31:25,738 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:31:25,739 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18319407261
2025-07-29 14:31:25,740 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:31:25,741 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:31:25,742 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:31:25,742 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:31:25,742 - modules.friend_request_window - INFO -    📱 手机号码: 18319407261
2025-07-29 14:31:25,744 - modules.friend_request_window - INFO -    🆔 准考证: 014325110095
2025-07-29 14:31:25,744 - modules.friend_request_window - INFO -    👤 姓名: 朱惠登
2025-07-29 14:31:25,745 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:31:25,745 - modules.friend_request_window - INFO -    📝 备注格式: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:25,745 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:31:25,746 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:25,746 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:31:25,748 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 524414, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:31:25,750 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 524414)
2025-07-29 14:31:25,750 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:31:25,752 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:31:25,754 - modules.friend_request_window - INFO - 🔄 激活窗口: 524414
2025-07-29 14:31:26,460 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:31:26,460 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:31:26,461 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:31:26,461 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:31:26,462 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:31:26,462 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:31:26,463 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:31:26,463 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:31:26,463 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:31:26,464 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:31:26,464 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:31:26,464 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:31:26,464 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:31:26,464 - modules.friend_request_window - INFO -    📝 remark参数: '014325110095-朱惠登-2025-07-29 22:31:25' (类型: <class 'str'>, 长度: 36)
2025-07-29 14:31:26,465 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:31:26,465 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:26,466 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:31:26,466 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:31:26,466 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:31:26,467 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:31:26,467 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:31:26,467 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:31:26,468 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:31:27,381 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:31:32,624 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:31:32,624 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:31:32,625 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:31:32,626 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:31:32,628 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '            return True

...' (前50字符)
2025-07-29 14:31:32,938 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:31:32,939 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:31:33,842 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:31:33,854 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:31:33,854 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:31:33,855 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:31:33,855 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:31:33,856 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:31:34,357 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:31:34,357 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:31:34,357 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:31:34,358 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:31:34,358 - modules.friend_request_window - INFO -    📝 内容: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:34,358 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 14:31:34,359 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110095-\xe6\x9c\xb1\xe6\x83\xa0\xe7\x99\xbb-2025-07-29 22:31:25'
2025-07-29 14:31:34,359 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:31:35,280 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:31:40,524 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:31:40,525 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:31:40,525 - modules.friend_request_window - INFO -    📝 原始文本: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:40,525 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 14:31:40,526 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '            return True

...' (前50字符)
2025-07-29 14:31:40,839 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:31:40,839 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:31:41,743 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:31:41,753 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:31:41,753 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:41,754 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:31:41,754 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:41,755 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 14:31:42,255 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:42,256 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:31:42,256 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:31:42,256 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:31:42,257 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:31:42,257 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:31:42,257 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:31:43,058 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:31:43,058 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:31:43,059 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:31:43,680 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:31:43,681 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:31:43,681 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:31:43,681 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:31:44,183 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 14:31:44,185 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 14:31:44,186 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 14:31:44,186 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 14:31:44,186 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 14:31:44,186 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 14:31:44,187 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 14:31:44,187 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 14:31:44,187 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 14:31:44,188 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:31:44,188 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 14:31:44,188 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 14:31:44,189 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 14:31:44,189 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 14:31:44,190 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 14:31:44,190 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 14:31:44,191 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:31:44,191 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-29 14:31:44,192 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-29 14:31:44,694 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 14:31:44,694 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 14:31:44,694 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 14:31:44,695 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 14:31:44,695 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 14:31:44,695 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 14:31:44,695 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 14:31:44,696 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 14:31:45,615 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 14:31:45,615 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 14:31:45,616 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 14:31:45,616 - modules.friend_request_window - INFO - 🔄 步骤2: 强制关闭所有微信相关窗口...
2025-07-29 14:31:45,616 - modules.friend_request_window - INFO - 🚨 强制关闭所有微信相关窗口...
2025-07-29 14:31:45,617 - modules.friend_request_window - INFO - 🔄 1. 关闭添加朋友窗口...
2025-07-29 14:31:45,617 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 14:31:46,118 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 14:31:47,229 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 14:31:47,229 - modules.friend_request_window - INFO - 🔄 2. 关闭当前微信窗口...
2025-07-29 14:31:47,230 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-29 14:31:47,731 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-29 14:31:48,846 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-29 14:31:48,847 - modules.friend_request_window - INFO - 🔄 3. 查找并强制关闭所有微信窗口...
2025-07-29 14:31:48,847 - modules.friend_request_window - INFO - 🔍 搜索所有微信窗口进行强制关闭...
2025-07-29 14:31:48,897 - modules.friend_request_window - INFO - 🎯 找到 1 个微信相关窗口需要关闭
2025-07-29 14:31:48,898 - modules.friend_request_window - INFO - 🔄 强制关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-29 14:31:48,898 - modules.friend_request_window - INFO - 🚨 强制关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-29 14:31:48,898 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-29 14:31:49,399 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-29 14:31:49,399 - modules.friend_request_window - INFO - ✅ 成功关闭窗口: 微信
2025-07-29 14:31:49,400 - modules.friend_request_window - INFO - 🔄 4. 关闭错误对话框...
2025-07-29 14:31:49,400 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 14:31:49,416 - modules.friend_request_window - INFO - 🔄 步骤3: 执行额外清理操作...
2025-07-29 14:31:49,416 - modules.friend_request_window - INFO - 🧹 执行额外清理操作...
2025-07-29 14:31:51,457 - modules.friend_request_window - INFO - ✅ 所有微信窗口已成功关闭
2025-07-29 14:31:51,458 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-29 14:31:51,459 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-29 14:31:51,459 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-29 14:31:51,459 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-29 14:31:51,459 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-29 14:31:51,460 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:31:51,460 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:31:51,460 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:31:51,460 - modules.friend_request_window - INFO -    📝 备注信息: '014325110095-朱惠登-2025-07-29 22:31:25'
2025-07-29 14:31:51,961 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:31:51,962 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:31:51,962 - modules.wechat_auto_add_simple - INFO - ✅ 18319407261 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:31:51,962 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18319407261
2025-07-29 14:31:51,963 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:31:55,407 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2946
2025-07-29 14:31:55,407 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15871569448 (王维)
2025-07-29 14:31:55,408 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:32:02,072 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15871569448
2025-07-29 14:32:02,073 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:32:02,073 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15871569448 执行添加朋友操作...
2025-07-29 14:32:02,073 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:32:02,074 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:32:02,075 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:32:02,077 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:32:02,082 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:32:02,084 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:32:02,084 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:32:02,085 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:32:02,085 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:32:02,086 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:32:02,086 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:32:02,086 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:32:02,089 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:32:02,091 - WeChatAutoAdd - INFO - 共找到 1 个微信窗口
2025-07-29 14:32:02,092 - WeChatAutoAdd - WARNING - 未找到微信添加朋友窗口
2025-07-29 14:32:02,094 - WeChatAutoAdd - ERROR - 未找到微信添加朋友窗口
2025-07-29 14:32:02,096 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:32:02,097 - modules.wechat_auto_add_simple - ERROR - ❌ 15871569448 添加朋友操作执行失败
2025-07-29 14:32:02,097 - modules.wechat_auto_add_simple - WARNING - ⚠️ 添加朋友失败: 15871569448 - 添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮
2025-07-29 14:32:02,098 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:32:03,797 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:32:03,798 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:32:03,798 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:32:03,799 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:32:03,799 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:32:03,800 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:32:03,800 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:32:03,800 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:32:03,800 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:32:03,801 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 14:32:03,801 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:32:03,802 - __main__ - INFO - � 更新全局进度：已处理 2/2946 个联系人
2025-07-29 14:32:03,802 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:32:06,802 - __main__ - INFO - 
============================================================
2025-07-29 14:32:06,803 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-29 14:32:06,803 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:32:06,803 - __main__ - INFO - 📊 全局进度：已处理 2/2946 个联系人
2025-07-29 14:32:06,804 - __main__ - INFO - ============================================================
2025-07-29 14:32:06,804 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 14:32:06,804 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:32:06,804 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:32:06,805 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 14:32:06,805 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:32:06,805 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-29 14:32:07,340 - modules.window_manager - INFO - ✅ 激活方法 1 部分成功（窗口可见）
2025-07-29 14:32:07,340 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:32:07,343 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:32:07,344 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:32:07,344 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:32:07,344 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:32:07,345 - modules.window_manager - INFO - 📏 当前窗口位置: (88, 48), 大小: 726x650
2025-07-29 14:32:07,345 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:32:07,346 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-29 14:32:07,649 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-29 14:32:07,653 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:32:07,654 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-29 14:32:07,654 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-29 14:32:07,655 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-29 14:32:07,656 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:32:07,857 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-29 14:32:07,858 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:32:07,858 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:32:07,858 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:32:08,179 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:32:08,179 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:32:08,180 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:32:08,180 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:32:08,180 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:32:08,181 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:32:08,181 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:32:08,182 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:32:08,182 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:32:08,183 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:32:08,385 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:32:08,386 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:32:08,387 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:32:08,689 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:32:08,689 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:32:08,689 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:32:08,690 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:32:08,690 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:32:08,690 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:32:08,691 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:32:08,691 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:32:09,691 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:32:09,692 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 14:32:09,692 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:32:09,693 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:32:09,693 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:32:09,694 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:32:09,694 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:32:09,694 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:32:09,895 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:32:09,896 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:32:12,277 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:32:12,277 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:32:12,278 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 14:32:13,858 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:32:14,059 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:32:14,060 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:32:16,428 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:32:16,429 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:32:16,429 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-29 14:32:18,141 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:32:18,342 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:32:18,343 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:32:20,726 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:32:20,726 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:32:20,727 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-29 14:32:23,088 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:32:23,290 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:32:23,290 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:32:25,659 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:32:25,660 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:32:25,661 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 14:32:27,432 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:32:27,633 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:32:27,633 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:32:30,009 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:32:30,009 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:32:30,009 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:32:30,010 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:32:30,010 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:32:30,012 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:32:30,012 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:32:30,013 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4983176, 进程: Weixin.exe)
2025-07-29 14:32:30,015 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 14:32:30,015 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4983176)
2025-07-29 14:32:30,015 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4983176) - 增强版
2025-07-29 14:32:30,318 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:32:30,319 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:32:30,319 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:32:30,320 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:32:30,320 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:32:30,320 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:32:30,523 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:32:30,524 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:32:30,725 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:32:30,726 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:32:30,726 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:32:30,726 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:32:30,727 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:32:30,727 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:32:30,727 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:32:31,728 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:32:31,729 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:32:31,730 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:32:31,731 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:32:31,731 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4983176, 进程: Weixin.exe)
2025-07-29 14:32:31,733 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 14:32:31,734 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:32:31,734 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:32:31,735 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:32:31,735 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:32:31,735 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:32:32,044 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:32:32,045 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:32:32,045 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:32:32,045 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:32:32,046 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:32:32,046 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:32:32,047 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:32:32,047 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:32:32,047 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:32:32,048 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:32:32,250 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:32:32,251 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:32:32,252 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:32:32,553 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:32:32,554 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:32:32,554 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:32:33,555 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:32:33,555 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-29 14:32:33,556 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:32:33,558 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_143233.log
2025-07-29 14:32:33,559 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:32:33,559 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:32:33,560 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:32:33,560 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-29 14:32:33,561 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:32:33,561 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:32:33,563 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-29 14:32:33,563 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 14:32:33,563 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-29 14:32:33,564 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:32:33,564 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 14:32:33,564 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:32:33,565 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:32:33,566 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4983176
2025-07-29 14:32:33,566 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4983176) - 增强版
2025-07-29 14:32:33,873 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:32:33,874 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:32:33,874 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:32:33,875 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:32:33,875 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:32:33,876 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:32:33,876 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:32:33,876 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:32:34,078 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:32:34,078 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:32:34,080 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4983176 (API返回: None)
2025-07-29 14:32:34,381 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:32:34,381 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:32:34,382 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:32:34,382 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:32:34,383 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:32:34,383 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:32:34,383 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:32:34,387 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:32:34,388 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:32:34,860 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:32:34,861 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:32:35,108 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2944 个
2025-07-29 14:32:35,109 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-29 14:32:35,109 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2942 个
2025-07-29 14:32:35,110 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2942 个 (总计: 3135 个)
2025-07-29 14:32:35,110 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:32:35,110 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:32:35,110 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:32:35,111 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:32:35,111 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2942
2025-07-29 14:32:35,111 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15697188631 (李佳兴)
2025-07-29 14:32:35,112 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:32:41,685 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15697188631
2025-07-29 14:32:41,685 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:32:41,685 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15697188631 执行添加朋友操作...
2025-07-29 14:32:41,686 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:32:41,686 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:32:41,687 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:32:41,688 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:32:41,688 - WeChatAutoAdd - INFO - screenshots目录已经是干净的
2025-07-29 14:32:41,689 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:32:41,689 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:32:41,690 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:32:41,690 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:32:41,690 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:32:41,691 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:32:41,691 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:32:41,694 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:32:41,696 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:32:41,698 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-29 14:32:41,699 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:32:42,201 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:32:42,202 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:32:42,268 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-29 14:32:42,269 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-29 14:32:42,277 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_143242.png
2025-07-29 14:32:42,279 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:32:42,280 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:32:42,281 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:32:42,283 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:32:42,284 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:32:42,290 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_143242.png
2025-07-29 14:32:42,292 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-07-29 14:32:42,293 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,453), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:32:42,294 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:32:42,295 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:32:42,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:32:42,298 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:32:42,299 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:32:42,300 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:32:42,301 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:32:42,309 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_143242.png
2025-07-29 14:32:42,311 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:32:42,312 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:32:42,316 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_143242.png
2025-07-29 14:32:42,333 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:32:42,335 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:32:42,336 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:32:42,337 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:32:42,639 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:32:43,408 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:32:43,409 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:32:43,411 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:32:43,411 - modules.wechat_auto_add_simple - INFO - ✅ 15697188631 添加朋友操作执行成功
2025-07-29 14:32:43,412 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:32:43,412 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:32:45,413 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:32:45,414 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:32:45,414 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:32:45,415 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:32:45,415 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:32:45,416 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:32:45,416 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:32:45,416 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:32:45,417 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:32:45,417 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15697188631
2025-07-29 14:32:45,417 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:32:45,418 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:32:45,418 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:32:45,418 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:32:45,419 - modules.friend_request_window - INFO -    📱 phone: '15697188631'
2025-07-29 14:32:45,419 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:32:45,419 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:32:45,909 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:32:45,909 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:32:45,909 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:32:45,910 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:32:45,911 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15697188631
2025-07-29 14:32:45,911 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:32:45,912 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:32:45,912 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:32:45,912 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:32:45,913 - modules.friend_request_window - INFO -    📱 手机号码: 15697188631
2025-07-29 14:32:45,913 - modules.friend_request_window - INFO -    🆔 准考证: 014325110103
2025-07-29 14:32:45,913 - modules.friend_request_window - INFO -    👤 姓名: 李佳兴
2025-07-29 14:32:45,913 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:32:45,914 - modules.friend_request_window - INFO -    📝 备注格式: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:32:45,914 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:32:45,915 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:32:45,915 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:32:45,916 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 6097654, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:32:45,918 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 6097654)
2025-07-29 14:32:45,918 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:32:45,918 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:32:45,919 - modules.friend_request_window - INFO - 🔄 激活窗口: 6097654
2025-07-29 14:32:46,622 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:32:46,622 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:32:46,623 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:32:46,623 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:32:46,623 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:32:46,623 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:32:46,624 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:32:46,624 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:32:46,624 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:32:46,624 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:32:46,625 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:32:46,625 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:32:46,625 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:32:46,625 - modules.friend_request_window - INFO -    📝 remark参数: '014325110103-李佳兴-2025-07-29 22:32:45' (类型: <class 'str'>, 长度: 36)
2025-07-29 14:32:46,626 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:32:46,626 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:32:46,626 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:32:46,627 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:32:46,627 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:32:46,628 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:32:46,628 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:32:46,629 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:32:46,629 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:32:47,542 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:32:52,786 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:32:52,787 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:32:52,787 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:32:52,787 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:32:52,789 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-29 14:31:48,898 - modules.friend_request_wi...' (前50字符)
2025-07-29 14:32:53,099 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:32:53,099 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:32:54,002 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:32:54,011 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:32:54,011 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:32:54,012 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:32:54,012 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:32:54,013 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:32:54,513 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:32:54,514 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:32:54,514 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:32:54,514 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:32:54,514 - modules.friend_request_window - INFO -    📝 内容: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:32:54,515 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 14:32:54,515 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110103-\xe6\x9d\x8e\xe4\xbd\xb3\xe5\x85\xb4-2025-07-29 22:32:45'
2025-07-29 14:32:54,515 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:32:55,424 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:33:00,667 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:33:00,667 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:33:00,668 - modules.friend_request_window - INFO -    📝 原始文本: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:33:00,668 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 14:33:00,669 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-29 14:31:48,898 - modules.friend_request_wi...' (前50字符)
2025-07-29 14:33:00,978 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:33:00,978 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:33:01,881 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:33:01,889 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:33:01,891 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:33:01,892 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:33:01,892 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:33:01,893 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 14:33:02,394 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:33:02,394 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:33:02,395 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:33:02,395 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:33:02,395 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:33:02,396 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:33:02,396 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:33:03,197 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:33:03,197 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:33:03,198 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:33:03,807 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:03,807 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:33:03,808 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:33:03,808 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:33:04,324 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:04,553 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:04,782 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:05,012 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:05,241 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:05,471 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:05,701 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:05,930 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:06,160 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:06,391 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:06,620 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:06,849 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:07,080 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:07,309 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:07,538 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:07,766 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:07,995 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:08,227 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:08,456 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:08,686 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:08,900 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 14:33:08,900 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 14:33:09,901 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:33:09,904 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:33:09,904 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 14:33:09,904 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:33:09,905 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:33:09,905 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:33:09,905 - modules.friend_request_window - INFO -    📝 备注信息: '014325110103-李佳兴-2025-07-29 22:32:45'
2025-07-29 14:33:10,406 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:33:10,407 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:10,407 - modules.wechat_auto_add_simple - INFO - ✅ 15697188631 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:33:10,407 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15697188631
2025-07-29 14:33:10,408 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:14,010 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2942
2025-07-29 14:33:14,011 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13310583166 (黎智)
2025-07-29 14:33:14,011 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:20,663 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13310583166
2025-07-29 14:33:20,696 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:33:20,717 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13310583166 执行添加朋友操作...
2025-07-29 14:33:20,718 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:33:20,718 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:33:20,723 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:33:20,732 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:33:20,748 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:33:20,758 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:33:20,759 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:33:20,760 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:33:20,760 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:33:20,761 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:33:20,761 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:33:20,761 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:33:20,774 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:33:20,778 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:33:20,781 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:33:20,797 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-29 14:33:20,802 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:33:21,314 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:33:21,316 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:33:21,398 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.01, 边缘比例0.0369
2025-07-29 14:33:21,408 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_143321.png
2025-07-29 14:33:21,416 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:33:21,420 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:33:21,432 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:33:21,434 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:33:21,437 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:33:21,444 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_143321.png
2025-07-29 14:33:21,446 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:33:21,449 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:33:21,451 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:33:21,454 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:33:21,459 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:33:21,461 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:33:21,463 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:33:21,465 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:33:21,476 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_143321.png
2025-07-29 14:33:21,479 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:33:21,481 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:33:21,485 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_143321.png
2025-07-29 14:33:21,513 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:33:21,517 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:33:21,518 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:33:21,521 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:33:21,827 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:33:22,606 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:33:22,607 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:33:22,610 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:22,610 - modules.wechat_auto_add_simple - INFO - ✅ 13310583166 添加朋友操作执行成功
2025-07-29 14:33:22,611 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:22,611 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:33:24,613 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:33:24,613 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:33:24,614 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:33:24,614 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:33:24,614 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:33:24,615 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:33:24,615 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:33:24,615 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:33:24,616 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:33:24,616 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13310583166
2025-07-29 14:33:24,617 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:33:24,617 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:33:24,617 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:33:24,617 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:33:24,618 - modules.friend_request_window - INFO -    📱 phone: '13310583166'
2025-07-29 14:33:24,618 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:33:24,618 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:33:25,106 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:33:25,106 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:33:25,107 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:33:25,107 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:33:25,108 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13310583166
2025-07-29 14:33:25,108 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:33:25,109 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:33:25,109 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:33:25,110 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:33:25,110 - modules.friend_request_window - INFO -    📱 手机号码: 13310583166
2025-07-29 14:33:25,110 - modules.friend_request_window - INFO -    🆔 准考证: 014325110104
2025-07-29 14:33:25,111 - modules.friend_request_window - INFO -    👤 姓名: 黎智
2025-07-29 14:33:25,111 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:33:25,112 - modules.friend_request_window - INFO -    📝 备注格式: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:25,113 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:33:25,115 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:25,116 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:33:25,117 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3147552, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:33:25,120 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3147552)
2025-07-29 14:33:25,120 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:33:25,121 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:33:25,122 - modules.friend_request_window - INFO - 🔄 激活窗口: 3147552
2025-07-29 14:33:25,825 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:33:25,826 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:33:25,826 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:33:25,827 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:33:25,827 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:33:25,827 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:33:25,827 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:33:25,828 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:33:25,828 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:33:25,828 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:33:25,828 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:33:25,829 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:33:25,829 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:33:25,829 - modules.friend_request_window - INFO -    📝 remark参数: '014325110104-黎智-2025-07-29 22:33:25' (类型: <class 'str'>, 长度: 35)
2025-07-29 14:33:25,829 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:33:25,830 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:25,830 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:33:25,830 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:33:25,831 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:33:25,831 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:33:25,831 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:33:25,832 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:33:25,833 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:33:26,739 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:33:31,988 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:33:31,989 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:33:31,989 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:33:31,989 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:33:31,990 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-29 14:31:48,898 - modules.friend_request_wi...' (前50字符)
2025-07-29 14:33:32,299 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:33:32,300 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:33:33,202 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:33:33,211 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:33:33,211 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:33:33,212 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:33:33,213 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:33:33,214 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:33:33,716 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:33:33,717 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:33:33,717 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:33:33,717 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:33:33,717 - modules.friend_request_window - INFO -    📝 内容: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:33,718 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 14:33:33,718 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110104-\xe9\xbb\x8e\xe6\x99\xba-2025-07-29 22:33:25'
2025-07-29 14:33:33,718 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:33:34,639 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:33:39,882 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:33:39,882 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:33:39,883 - modules.friend_request_window - INFO -    📝 原始文本: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:39,883 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 14:33:39,884 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-29 14:31:48,898 - modules.friend_request_wi...' (前50字符)
2025-07-29 14:33:40,193 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:33:40,193 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:33:41,095 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:33:41,104 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:33:41,105 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:41,106 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:33:41,107 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:41,107 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 14:33:41,609 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:41,609 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:33:41,610 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:33:41,610 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:33:41,610 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:33:41,610 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:33:41,611 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:33:42,411 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:33:42,412 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:33:42,412 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:33:43,021 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:43,022 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:33:43,022 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:33:43,022 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:33:43,539 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:43,770 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:44,001 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:44,231 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:44,462 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:44,694 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:44,929 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:45,161 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:45,392 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:45,621 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:45,856 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:46,089 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:46,321 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:46,553 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:46,783 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:47,011 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:47,244 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:47,476 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:47,709 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:47,942 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:33:48,155 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 14:33:48,156 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 14:33:49,157 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:33:49,159 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:33:49,160 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 14:33:49,160 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:33:49,160 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:33:49,161 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:33:49,161 - modules.friend_request_window - INFO -    📝 备注信息: '014325110104-黎智-2025-07-29 22:33:25'
2025-07-29 14:33:49,661 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:33:49,662 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:49,663 - modules.wechat_auto_add_simple - INFO - ✅ 13310583166 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:33:49,663 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13310583166
2025-07-29 14:33:49,664 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:33:51,029 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:33:51,030 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:33:51,030 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:33:51,031 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:33:51,032 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:33:51,032 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:33:51,032 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:33:51,032 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:33:51,033 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:33:51,033 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-29 14:33:51,033 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:33:51,034 - __main__ - INFO - � 更新全局进度：已处理 4/2946 个联系人
2025-07-29 14:33:51,034 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:33:54,035 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-29 14:33:54,036 - __main__ - INFO - 📊 当前进度：已处理 4/2946 个联系人
2025-07-29 14:33:54,037 - __main__ - INFO - 
============================================================
2025-07-29 14:33:54,038 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 2 轮)
2025-07-29 14:33:54,038 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:33:54,038 - __main__ - INFO - 📊 全局进度：已处理 4/2946 个联系人
2025-07-29 14:33:54,038 - __main__ - INFO - ============================================================
2025-07-29 14:33:54,039 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 14:33:54,039 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:33:54,039 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:33:54,039 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 14:33:54,040 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:33:54,040 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-29 14:33:54,568 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:33:54,569 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:33:54,570 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:33:54,571 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:33:54,572 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:33:54,572 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:33:54,572 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:33:54,573 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:33:54,573 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:33:54,574 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:33:54,776 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:33:54,776 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:33:54,777 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:33:54,777 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
