#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信新闻提示框阻止器
用于检测和关闭微信的新闻弹窗提示框

功能：
1. 检测微信新闻弹窗
2. 自动关闭新闻提示框
3. 防止新闻弹窗干扰自动化流程

作者：AI助手
创建时间：2025-01-28
版本：1.0.0
"""

import time
import logging
import win32gui
import win32con
from typing import List, Dict, Optional
from dataclasses import dataclass


@dataclass
class PopupInfo:
    """弹窗信息"""
    hwnd: int
    title: str
    class_name: str
    rect: tuple
    is_visible: bool


class WeChatNewsPopupBlocker:
    """微信新闻提示框阻止器"""
    
    def __init__(self, config: Dict = None):
        """初始化阻止器"""
        self.config = config or {}
        self.logger = self._setup_logger()
        
        # 新闻弹窗识别配置
        self.news_popup_patterns = {
            "title_keywords": [
                "新闻", "资讯", "热点", "推荐", "头条", 
                "通知", "消息", "提醒", "更新"
            ],
            "class_names": [
                "Qt51514QWindowIcon", 
                "#32770", 
                "Dialog",
                "PopupWindow"
            ],
            "size_ranges": {
                "min_width": 200,
                "max_width": 600,
                "min_height": 100,
                "max_height": 400
            }
        }
        
        self.logger.info("✅ 微信新闻提示框阻止器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.WeChatNewsPopupBlocker")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def detect_and_block_popups(self) -> List[PopupInfo]:
        """检测并阻止新闻弹窗"""
        try:
            self.logger.debug("🔍 开始检测微信新闻弹窗...")
            
            # 查找所有可能的新闻弹窗
            popup_windows = self._find_news_popups()
            
            if not popup_windows:
                self.logger.debug("🔍 未发现微信新闻弹窗")
                return []
            
            blocked_popups = []
            
            # 关闭找到的弹窗
            for popup in popup_windows:
                if self._close_popup(popup):
                    blocked_popups.append(popup)
                    self.logger.info(f"✅ 成功关闭新闻弹窗: {popup.title}")
                else:
                    self.logger.warning(f"⚠️ 关闭新闻弹窗失败: {popup.title}")
            
            if blocked_popups:
                self.logger.info(f"🎯 总共关闭了 {len(blocked_popups)} 个新闻弹窗")
            
            return blocked_popups
            
        except Exception as e:
            self.logger.error(f"❌ 检测和阻止新闻弹窗异常: {e}")
            return []
    
    def _find_news_popups(self) -> List[PopupInfo]:
        """查找新闻弹窗窗口"""
        try:
            popup_windows = []
            
            def enum_windows_callback(hwnd, _):
                try:
                    if not win32gui.IsWindowVisible(hwnd):
                        return True
                    
                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]
                    
                    # 检查是否是新闻弹窗
                    if self._is_news_popup(title, class_name, width, height):
                        popup_info = PopupInfo(
                            hwnd=hwnd,
                            title=title or "",
                            class_name=class_name,
                            rect=rect,
                            is_visible=True
                        )
                        popup_windows.append(popup_info)
                        self.logger.debug(f"🔍 发现新闻弹窗: {title} ({class_name}) {width}x{height}")
                
                except:
                    pass
                return True
            
            win32gui.EnumWindows(enum_windows_callback, None)
            return popup_windows
            
        except Exception as e:
            self.logger.error(f"❌ 查找新闻弹窗异常: {e}")
            return []
    
    def _is_news_popup(self, title: str, class_name: str, width: int, height: int) -> bool:
        """判断是否是新闻弹窗"""
        try:
            # 检查标题关键词
            title_match = any(
                keyword in title 
                for keyword in self.news_popup_patterns["title_keywords"]
            )
            
            # 检查类名
            class_match = class_name in self.news_popup_patterns["class_names"]
            
            # 检查窗口大小
            size_ranges = self.news_popup_patterns["size_ranges"]
            size_match = (
                size_ranges["min_width"] <= width <= size_ranges["max_width"] and
                size_ranges["min_height"] <= height <= size_ranges["max_height"]
            )
            
            # 需要满足至少两个条件
            conditions_met = sum([title_match, class_match, size_match])
            return conditions_met >= 2
            
        except Exception as e:
            self.logger.debug(f"⚠️ 判断新闻弹窗异常: {e}")
            return False
    
    def _close_popup(self, popup: PopupInfo) -> bool:
        """关闭弹窗"""
        try:
            # 方法1: 发送关闭消息
            win32gui.SendMessage(popup.hwnd, win32con.WM_CLOSE, 0, 0)
            time.sleep(0.2)
            
            # 验证是否关闭
            if not win32gui.IsWindow(popup.hwnd) or not win32gui.IsWindowVisible(popup.hwnd):
                return True
            
            # 方法2: 强制关闭
            try:
                win32gui.DestroyWindow(popup.hwnd)
                time.sleep(0.2)
                return True
            except:
                pass
            
            # 方法3: 发送ESC键
            try:
                win32gui.SetForegroundWindow(popup.hwnd)
                time.sleep(0.1)
                import pyautogui
                pyautogui.press('escape')
                time.sleep(0.2)
                return not (win32gui.IsWindow(popup.hwnd) and win32gui.IsWindowVisible(popup.hwnd))
            except:
                pass
            
            return False
            
        except Exception as e:
            self.logger.warning(f"⚠️ 关闭弹窗异常: {e}")
            return False
    
    def is_enabled(self) -> bool:
        """检查阻止器是否启用"""
        return self.config.get("enable_news_popup_blocker", True)
    
    def get_blocked_count(self) -> int:
        """获取已阻止的弹窗数量（简单实现）"""
        return getattr(self, '_blocked_count', 0)


# 兼容性函数
def create_news_popup_blocker(config: Dict = None) -> WeChatNewsPopupBlocker:
    """创建新闻弹窗阻止器实例"""
    return WeChatNewsPopupBlocker(config)


if __name__ == "__main__":
    # 测试代码
    blocker = WeChatNewsPopupBlocker()
    blocked = blocker.detect_and_block_popups()
    print(f"测试完成，阻止了 {len(blocked)} 个弹窗")
