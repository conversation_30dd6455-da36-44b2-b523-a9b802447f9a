2025-07-30 11:16:07,151 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:16:07,152 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:16:07,152 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:16:07,153 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:16:07,153 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 11:16:07,154 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:16:07,154 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:16:07,155 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:16:07,156 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:16:07,157 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:16:07,158 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:16:07,160 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:16:07,163 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_111607.log
2025-07-30 11:16:07,164 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:16:07,165 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:16:07,165 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:16:07,165 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 11:16:07,166 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 11:16:07,166 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 19:16:07
2025-07-30 11:16:07,166 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 11:16:07,166 - __main__ - INFO - 📅 启动时间: 2025-07-30 19:16:07
2025-07-30 11:16:07,166 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 11:16:07,167 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:16:07,697 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:16:07,697 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:16:08,229 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:16:08,230 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:16:08,233 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:16:08,233 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 11:16:08,233 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 11:16:08,233 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 11:16:08,235 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 11:16:09,894 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 11:16:09,931 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 11:16:10,020 - __main__ - INFO - 📋 待处理联系人数: 2905
2025-07-30 11:16:10,071 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 11:16:10,168 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2905
2025-07-30 11:16:10,232 - __main__ - INFO - 
============================================================
2025-07-30 11:16:10,299 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 11:16:10,397 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:16:10,499 - __main__ - INFO - 📊 全局进度：已处理 0/2905 个联系人
2025-07-30 11:16:10,577 - __main__ - INFO - ============================================================
2025-07-30 11:16:10,648 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:16:10,740 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:16:10,824 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:16:10,895 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:16:10,944 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:16:11,336 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:16:11,434 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:16:11,538 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:16:11,594 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:16:11,665 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:16:11,757 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:16:11,835 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:16:11,939 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:16:12,054 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:16:12,178 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:16:12,486 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:16:12,507 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:16:12,581 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:16:12,662 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:16:13,063 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:16:13,130 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:16:13,402 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:16:13,545 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:16:13,634 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:16:13,716 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:16:13,825 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:16:13,895 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:16:13,970 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:16:14,043 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:16:14,282 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:16:14,331 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:16:14,370 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:16:14,718 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:16:14,767 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:16:14,821 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:16:14,850 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:16:14,912 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:16:14,990 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:16:15,092 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:16:15,161 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:16:16,211 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:16:16,224 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:16:16,236 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:16:16,260 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:16:16,291 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:16:16,320 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:16:16,368 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:16:16,387 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:16:16,617 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:16:16,641 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:16:19,644 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:16:19,687 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:16:19,713 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 11:16:22,151 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:16:22,352 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:16:22,353 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:16:24,726 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:16:24,726 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:16:24,727 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 11:16:26,910 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:16:27,111 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:16:27,111 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:16:29,493 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:16:29,494 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:16:29,494 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 11:16:31,375 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:16:31,577 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:16:31,577 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:16:33,958 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:16:33,958 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:16:33,959 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 11:16:36,662 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:16:36,862 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:16:36,863 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:16:39,241 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:16:39,242 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:16:39,242 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:16:39,242 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:16:39,243 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:16:39,244 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:16:39,245 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:16:39,245 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:16:39,246 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:16:39,247 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1245242, 进程: Weixin.exe)
2025-07-30 11:16:39,250 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:16:39,251 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1245242)
2025-07-30 11:16:39,252 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1245242) - 增强版
2025-07-30 11:16:39,556 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:16:39,556 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:16:39,556 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:16:39,557 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:16:39,557 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:16:39,557 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:16:39,761 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:16:39,761 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:16:39,964 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:16:39,964 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:16:39,964 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:16:39,964 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:16:39,965 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:16:39,965 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:16:39,965 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:16:40,966 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:16:40,967 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:16:40,968 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:16:40,969 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:16:40,969 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:16:40,972 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:16:40,974 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1245242, 进程: Weixin.exe)
2025-07-30 11:16:40,976 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:16:40,977 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:16:40,977 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:16:40,977 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:16:40,978 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:16:40,978 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:16:41,285 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:16:41,286 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:16:41,286 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:16:41,287 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:16:41,288 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:16:41,288 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:16:41,289 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:16:41,289 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:16:41,289 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:16:41,290 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:16:41,491 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:16:41,492 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:16:41,493 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:16:41,794 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:16:41,795 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:16:41,795 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:16:42,796 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:16:42,796 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 11:16:42,796 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:16:42,799 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_111642.log
2025-07-30 11:16:42,800 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:16:42,801 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:16:42,801 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:16:42,802 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 11:16:42,804 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:16:42,807 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:16:42,810 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 11:16:42,810 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 11:16:42,811 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:16:42,811 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:16:42,812 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 11:16:42,812 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 11:16:42,813 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:16:42,814 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:16:42,818 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1245242
2025-07-30 11:16:42,818 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1245242) - 增强版
2025-07-30 11:16:43,127 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:16:43,127 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:16:43,128 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:16:43,128 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:16:43,128 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:16:43,129 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:16:43,129 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:16:43,129 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:16:43,332 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:16:43,333 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:16:43,337 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1245242 (API返回: None)
2025-07-30 11:16:43,639 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:16:43,639 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:16:43,639 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:16:43,640 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:16:43,641 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:16:43,641 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:16:43,641 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:16:43,644 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:16:43,645 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:16:44,248 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:16:44,248 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:16:44,533 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2912 个
2025-07-30 11:16:44,534 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2912 个 (总计: 3135 个)
2025-07-30 11:16:44,534 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:16:44,535 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:16:44,535 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:16:44,535 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:16:44,536 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2912
2025-07-30 11:16:44,536 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18263399923 (成宇鑫)
2025-07-30 11:16:44,537 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
