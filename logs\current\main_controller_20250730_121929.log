2025-07-30 12:19:29,349 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:19:29,350 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:19:29,350 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:29,351 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:29,351 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:19:29,352 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:19:29,353 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:19:29,355 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:19:29,355 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:19:29,356 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:19:29,356 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:19:29,356 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:19:29,359 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:29,363 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_121929.log
2025-07-30 12:19:29,364 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:29,364 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:19:29,364 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:19:29,365 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:19:29,365 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:19:29,366 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:19:29,366 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 20:19:29
2025-07-30 12:19:29,366 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:19:29,367 - __main__ - INFO - 📅 启动时间: 2025-07-30 20:19:29
2025-07-30 12:19:29,367 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:19:29,367 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:19:29,909 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:19:29,909 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:19:30,437 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:19:30,438 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:19:30,439 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:19:30,439 - __main__ - INFO - ✅ 找到 2 个可用微信窗口
2025-07-30 12:19:30,440 - __main__ - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 12:19:30,440 - __main__ - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:19:30,440 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 12:19:31,327 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 12:19:31,327 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 12:19:31,328 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:19:31,329 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:19:31,330 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 12:19:31,330 - __main__ - INFO - 
============================================================
2025-07-30 12:19:31,331 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 12:19:31,332 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:19:31,333 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:19:31,336 - __main__ - INFO - ============================================================
2025-07-30 12:19:31,339 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:19:31,341 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:19:31,342 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:19:31,343 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:19:31,344 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:19:31,657 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:19:31,658 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:19:31,658 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:19:31,658 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:19:31,659 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:19:31,659 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:19:31,659 - modules.window_manager - INFO - 📏 当前窗口位置: (686, 279), 大小: 726x650
2025-07-30 12:19:31,660 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:19:31,660 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 12:19:31,964 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-30 12:19:31,964 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:19:31,965 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-30 12:19:31,965 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-30 12:19:31,965 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-30 12:19:31,966 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:19:32,167 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:19:32,168 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:19:32,168 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:19:32,168 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:19:32,472 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:19:32,473 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:19:32,473 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:19:32,474 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:19:32,474 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:19:32,474 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:19:32,475 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:19:32,475 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:19:32,475 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:19:32,476 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:19:32,677 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:19:32,677 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:19:32,681 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:19:32,982 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:19:32,983 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:19:32,983 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:19:32,983 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:19:32,984 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:19:32,984 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:19:32,984 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 12:19:32,984 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:19:33,985 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:19:33,986 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 12:19:33,986 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:19:33,986 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:19:33,987 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:19:33,988 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:19:33,988 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:19:33,989 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:19:34,190 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:19:34,191 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:19:36,575 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:19:36,575 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:19:36,576 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 12:19:39,384 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:19:39,585 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:19:39,585 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:19:41,983 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:19:41,984 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:19:41,984 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
