# 窗口状态管理逻辑错误修复说明

## 问题描述

在微信自动化系统中存在一个严重的窗口状态管理逻辑错误：

1. **问题现象**：微信窗口1被检测出频率错误并被正确关闭后，系统切换到微信窗口2并正常执行流程。但是在微信窗口2添加2个人之后，系统错误地重新激活了之前被关闭的微信窗口1，导致又恢复成了2个微信窗口的状态。

2. **根本原因**：
   - 缺乏持久化的窗口黑名单机制
   - 窗口重新发现时没有检查历史错误状态
   - 窗口切换逻辑中没有过滤已关闭的问题窗口
   - 状态管理不一致，临时状态容易丢失

## 修复方案

### 1. 新增窗口黑名单管理系统

#### 1.1 WindowBlacklist类 (modules/frequency_error_handler.py)

```python
class WindowBlacklist:
    """窗口黑名单管理器
    
    功能：
    1. 管理被检测出频率错误的窗口黑名单
    2. 提供持久化存储，确保重启后黑名单仍然有效
    3. 生成窗口唯一标识符，防止问题窗口重新被使用
    4. 提供黑名单查询、添加、清理功能
    """
```

**核心功能**：
- **持久化存储**：使用JSON文件保存黑名单，确保程序重启后仍然有效
- **窗口唯一标识**：基于进程ID+进程名+窗口标题+进程路径生成稳定的窗口标识符
- **自动清理**：自动清理7天前的过期黑名单条目
- **线程安全**：使用锁机制确保多线程环境下的数据一致性

#### 1.2 关键方法

- `generate_window_id()`: 生成窗口唯一标识符
- `add_to_blacklist()`: 将窗口添加到黑名单
- `is_blacklisted()`: 检查窗口是否在黑名单中
- `get_blacklist_info()`: 获取窗口的黑名单详细信息
- `clear_blacklist()`: 清空黑名单

### 2. 修改频率错误处理逻辑

#### 2.1 在handle_frequency_error方法中添加黑名单标记

```python
# 🆕 步骤0: 将当前窗口添加到黑名单（关键修复）
if detection_result.window_info:
    self.logger.info("🚫 将检测到频率错误的窗口添加到黑名单...")
    self.window_blacklist.add_to_blacklist(
        detection_result.window_info.hwnd,
        detection_result.window_info.title,
        detection_result.error_type,
        detection_result.error_message
    )
    self.logger.info("✅ 窗口已永久标记为不可用，防止重新激活")
```

#### 2.2 新增黑名单管理方法

- `is_window_blacklisted()`: 检查窗口是否在黑名单中
- `get_window_blacklist_info()`: 获取窗口黑名单信息
- `add_window_to_blacklist()`: 手动添加窗口到黑名单
- `clear_window_blacklist()`: 清空窗口黑名单

### 3. 修改窗口管理器逻辑

#### 3.1 在find_all_wechat_windows中添加黑名单过滤

```python
# 🆕 检查窗口是否在黑名单中
if self._is_window_blacklisted(hwnd, window_text):
    self.logger.warning(f"🚫 跳过黑名单窗口: {window_text} (句柄: {hwnd})")
    return True  # 继续枚举其他窗口
```

#### 3.2 在switch_to_next_window中添加黑名单检查

```python
# 🆕 寻找下一个可用的窗口（跳过黑名单窗口）
while attempts < max_attempts:
    # 切换到下一个窗口
    self.current_window_index = (self.current_window_index + 1) % len(self.wechat_windows)
    next_window = self.wechat_windows[self.current_window_index]
    
    # 🆕 检查窗口是否在黑名单中
    if self._is_window_blacklisted(next_window['hwnd'], next_window['title']):
        self.logger.warning(f"🚫 跳过黑名单窗口: {next_window['title']}")
        attempts += 1
        continue
```

#### 3.3 新增黑名单检查方法

- `_is_window_blacklisted()`: 检查窗口是否在黑名单中
- `set_frequency_handler()`: 设置频率错误处理器引用

### 4. 修改主控制器逻辑

#### 4.1 建立组件间引用关系

```python
# 🆕 先初始化频率错误处理器
self.frequency_handler = FrequencyErrorHandler()
# 🆕 传入频率错误处理器引用
self.window_manager = WeChatWindowManager(frequency_handler=self.frequency_handler)
# 🆕 建立双向引用关系
self.window_manager.set_frequency_handler(self.frequency_handler)
```

#### 4.2 在get_wechat_windows中添加黑名单过滤

```python
# 🆕 过滤黑名单窗口
for window in all_windows:
    hwnd = window.get('hwnd')
    title = window.get('title', 'Unknown')
    
    # 🆕 验证hwnd有效性
    if not hwnd or not isinstance(hwnd, int):
        continue
    
    if self.frequency_handler.is_window_blacklisted(hwnd, title):
        blacklisted_count += 1
        self.logger.warning(f"🚫 跳过黑名单窗口: {title}")
    else:
        valid_windows.append(window)
```

## 修复效果

### 1. 问题解决

- ✅ **永久标记**：被检测出频率错误的窗口会被永久标记为不可用
- ✅ **防止重新激活**：黑名单中的窗口不会被重新发现或激活
- ✅ **状态持久化**：黑名单信息保存在文件中，程序重启后仍然有效
- ✅ **智能过滤**：窗口发现和切换逻辑会自动过滤黑名单窗口

### 2. 系统行为

- **正确行为**：微信窗口1一旦被检测出频率错误并关闭后，会被永久标记为"不可用"状态
- **持续运行**：系统只在未被检测出频率错误的微信窗口2中继续运行
- **防止回退**：被关闭的微信窗口1不会在任何情况下被重新激活或打开

### 3. 安全机制

- **自动清理**：7天后自动清理过期的黑名单条目
- **手动管理**：提供手动添加、查询、清理黑名单的接口
- **错误容错**：黑名单检查失败时不会阻止正常窗口的使用
- **日志记录**：详细记录黑名单操作和状态变化

## 测试验证

### 测试脚本：test_window_blacklist.py

测试内容：
1. ✅ 基本黑名单功能（添加、查询、删除）
2. ✅ 窗口管理器的黑名单过滤
3. ✅ 窗口切换时的黑名单检查
4. ✅ 黑名单数据的持久化存储

### 测试结果

```
📊 测试结果汇总:
  基本功能测试: ✅ 通过
  持久化测试: ✅ 通过
🎉 所有测试通过！窗口黑名单功能正常工作
```

## 文件修改清单

### 修改的文件

1. **modules/frequency_error_handler.py**
   - 新增：WindowBlacklist类
   - 修改：FrequencyErrorHandler.__init__()
   - 修改：handle_frequency_error()
   - 新增：黑名单管理相关方法

2. **modules/window_manager.py**
   - 修改：WeChatWindowManager.__init__()
   - 修改：find_all_wechat_windows()
   - 修改：switch_to_next_window()
   - 新增：_is_window_blacklisted()
   - 新增：set_frequency_handler()

3. **main_controller.py**
   - 修改：MainController.__init__()
   - 修改：get_wechat_windows()

### 新增的文件

1. **test_window_blacklist.py** - 黑名单功能测试脚本
2. **窗口黑名单修复说明.md** - 本文档
3. **window_blacklist.json** - 黑名单数据文件（运行时自动创建）

## 总结

通过实施窗口黑名单管理系统，成功解决了微信窗口状态管理的逻辑错误。现在系统能够：

1. **正确标记**：将检测出频率错误的窗口永久标记为不可用
2. **智能过滤**：在窗口发现和切换时自动过滤黑名单窗口
3. **状态持久**：黑名单信息持久化保存，确保重启后仍然有效
4. **安全可靠**：提供完整的错误处理和恢复机制

这个修复确保了频率错误处理的一致性和持久性，防止了问题窗口的重新激活，从而解决了原始问题。
