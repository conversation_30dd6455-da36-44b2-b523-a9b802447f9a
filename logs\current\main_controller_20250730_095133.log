2025-07-30 09:51:33,153 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:51:33,154 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:51:33,154 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 09:51:33,155 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:51:33,157 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 09:51:33,157 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:51:33,158 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:51:33,160 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:51:33,162 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:51:33,163 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:51:33,164 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:51:33,174 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:51:33,187 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_095133.log
2025-07-30 09:51:33,189 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:51:33,191 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 09:51:33,192 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 09:51:33,193 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 09:51:33,193 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 09:51:33,194 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 17:51:33
2025-07-30 09:51:33,200 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 09:51:33,207 - __main__ - INFO - 📅 启动时间: 2025-07-30 17:51:33
2025-07-30 09:51:33,209 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 09:51:33,213 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:51:33,750 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:51:33,754 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 09:51:34,280 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:51:34,280 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:51:34,283 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 09:51:34,283 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 09:51:34,284 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 09:51:34,284 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 09:51:34,285 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 09:51:35,331 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 09:51:35,331 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 09:51:35,331 - __main__ - INFO - 📋 待处理联系人数: 2935
2025-07-30 09:51:35,332 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 09:51:35,332 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2935
2025-07-30 09:51:35,333 - __main__ - INFO - 
============================================================
2025-07-30 09:51:35,333 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 09:51:35,333 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 09:51:35,334 - __main__ - INFO - 📊 全局进度：已处理 0/2935 个联系人
2025-07-30 09:51:35,334 - __main__ - INFO - ============================================================
2025-07-30 09:51:35,335 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 09:51:35,336 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 09:51:35,337 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 09:51:35,339 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 09:51:35,339 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:51:35,643 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:51:35,644 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:51:35,644 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:51:35,644 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:51:35,645 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:51:35,645 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:51:35,645 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:51:35,645 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:51:35,646 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:51:35,646 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:51:35,848 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:51:35,849 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:51:35,849 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 09:51:35,850 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:51:36,154 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:51:36,155 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:51:36,155 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:51:36,155 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:51:36,156 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:51:36,156 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:51:36,156 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:51:36,156 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:51:36,157 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:51:36,157 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:51:36,359 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:51:36,359 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:51:36,361 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 09:51:36,662 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:51:36,662 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:51:36,663 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:51:36,663 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:51:36,664 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:51:36,664 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:51:36,664 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 09:51:36,665 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 09:51:37,665 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 09:51:37,665 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 09:51:37,666 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 09:51:37,666 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 09:51:37,666 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 09:51:37,667 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 09:51:37,667 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 09:51:37,668 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 09:51:37,869 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 09:51:37,878 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 09:51:40,255 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 09:51:40,255 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 09:51:40,256 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 09:51:42,621 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 09:51:42,822 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 09:51:42,823 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 09:51:45,204 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 09:51:45,204 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 09:51:45,205 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 09:51:48,134 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 09:51:48,335 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 09:51:48,335 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 09:51:50,703 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 09:51:50,703 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 09:51:50,704 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 09:51:53,364 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 09:51:53,565 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 09:51:53,566 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 09:51:55,933 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 09:51:55,934 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 09:51:55,934 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 09:51:57,540 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 09:51:57,741 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 09:51:57,742 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 09:52:00,120 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 09:52:00,121 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 09:52:00,121 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:52:00,121 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:52:00,121 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:52:00,123 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:52:00,123 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:52:00,124 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:52:00,125 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 09:52:00,127 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:52:00,130 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 09:52:00,130 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3148976)
2025-07-30 09:52:00,131 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148976) - 增强版
2025-07-30 09:52:00,435 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:52:00,435 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:52:00,435 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:52:00,436 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:52:00,437 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 09:52:00,437 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:52:00,640 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 09:52:00,641 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:52:00,843 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:52:00,843 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:52:00,843 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 09:52:00,844 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 09:52:00,844 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 09:52:00,844 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 09:52:00,844 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 09:52:01,845 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 09:52:01,845 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:52:01,847 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:52:01,847 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:52:01,848 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:52:01,848 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 09:52:01,850 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:52:01,853 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 09:52:01,854 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 09:52:01,854 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 09:52:01,855 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 09:52:01,855 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 09:52:01,856 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:52:02,164 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:52:02,164 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:52:02,164 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:52:02,165 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:52:02,165 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:52:02,166 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:52:02,166 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:52:02,166 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:52:02,167 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:52:02,167 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:52:02,369 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:52:02,370 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:52:02,371 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 09:52:02,672 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:52:02,673 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 09:52:02,673 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 09:52:03,673 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 09:52:03,674 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 09:52:03,674 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 09:52:03,677 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_095203.log
2025-07-30 09:52:03,677 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:52:03,678 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 09:52:03,678 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 09:52:03,679 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 09:52:03,679 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 09:52:03,680 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 09:52:03,682 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 09:52:03,682 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 09:52:03,683 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 09:52:03,684 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 09:52:03,684 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 09:52:03,685 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 09:52:03,688 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 09:52:03,689 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:52:03,689 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3148976
2025-07-30 09:52:03,690 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148976) - 增强版
2025-07-30 09:52:03,998 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:52:03,998 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:52:03,998 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:52:03,999 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:52:03,999 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 09:52:03,999 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:52:04,000 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 09:52:04,000 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:52:04,202 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:52:04,202 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:52:04,206 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3148976 (API返回: None)
2025-07-30 09:52:04,507 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:52:04,507 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 09:52:04,507 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 09:52:04,508 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 09:52:04,509 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:52:04,509 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 09:52:04,509 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 09:52:04,513 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 09:52:04,513 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 09:52:04,996 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 09:52:04,996 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:52:05,260 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2935 个
2025-07-30 09:52:05,261 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2935 个 (总计: 3135 个)
2025-07-30 09:52:05,261 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 09:52:05,262 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 09:52:05,262 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:05,263 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 09:52:05,264 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2935
2025-07-30 09:52:05,264 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13621013400 (昌晟)
2025-07-30 09:52:05,264 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:11,840 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13621013400
2025-07-30 09:52:11,841 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:52:11,841 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13621013400 执行添加朋友操作...
2025-07-30 09:52:11,841 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:52:11,841 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:52:11,842 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:52:11,843 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:52:11,848 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 09:52:11,849 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:52:11,850 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:52:11,851 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:52:11,852 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:52:11,852 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:52:11,853 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:52:11,854 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:52:11,860 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:52:11,862 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:52:11,863 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:52:11,864 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 09:52:11,872 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 09:52:11,873 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:52:12,375 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:52:12,376 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 09:52:12,525 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差41.72, 边缘比例0.0386
2025-07-30 09:52:12,572 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095212.png
2025-07-30 09:52:12,573 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:52:12,574 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:52:12,575 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:52:12,576 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:52:12,577 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:52:12,596 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095212.png
2025-07-30 09:52:12,598 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-07-30 09:52:12,604 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,450), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 09:52:12,608 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 09:52:12,620 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 09:52:12,622 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,209), 尺寸2x240, 长宽比0.01, 面积480
2025-07-30 09:52:12,622 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸326x245, 长宽比1.33, 面积79870
2025-07-30 09:52:12,623 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 09:52:12,626 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 09:52:12,628 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 09:52:12,629 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 09:52:12,646 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095212.png
2025-07-30 09:52:12,649 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 09:52:12,651 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 09:52:12,657 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_095212.png
2025-07-30 09:52:12,789 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 09:52:12,790 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 09:52:12,791 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 09:52:12,792 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:52:13,093 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 09:52:13,870 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:52:13,871 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:52:13,872 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:13,872 - modules.wechat_auto_add_simple - INFO - ✅ 13621013400 添加朋友操作执行成功
2025-07-30 09:52:13,872 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:13,876 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:52:15,878 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:52:15,879 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:52:15,879 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:52:15,879 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:52:15,880 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:52:15,880 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:52:15,881 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:52:15,881 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:52:15,881 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:52:15,882 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13621013400
2025-07-30 09:52:15,886 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:52:15,886 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:52:15,887 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:52:15,887 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:52:15,889 - modules.friend_request_window - INFO -    📱 phone: '13621013400'
2025-07-30 09:52:15,889 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:52:15,889 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:52:16,380 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:52:16,381 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:52:16,381 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:52:16,381 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:52:16,393 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13621013400
2025-07-30 09:52:16,394 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:52:16,396 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:52:16,398 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:52:16,398 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:52:16,398 - modules.friend_request_window - INFO -    📱 手机号码: 13621013400
2025-07-30 09:52:16,402 - modules.friend_request_window - INFO -    🆔 准考证: 014325110115
2025-07-30 09:52:16,403 - modules.friend_request_window - INFO -    👤 姓名: 昌晟
2025-07-30 09:52:16,403 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:52:16,404 - modules.friend_request_window - INFO -    📝 备注格式: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:16,404 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:52:16,405 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:16,405 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:52:16,407 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4852542, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 09:52:16,410 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4852542)
2025-07-30 09:52:16,433 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 09:52:16,469 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 09:52:16,486 - modules.friend_request_window - INFO - 🔄 激活窗口: 4852542
2025-07-30 09:52:17,191 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 09:52:17,191 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 09:52:17,192 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 09:52:17,192 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 09:52:17,193 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 09:52:17,193 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:52:17,193 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 09:52:17,194 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:52:17,194 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 09:52:17,194 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 09:52:17,195 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 09:52:17,195 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 09:52:17,195 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 09:52:17,196 - modules.friend_request_window - INFO -    📝 remark参数: '014325110115-昌晟-2025-07-30 17:52:16' (类型: <class 'str'>, 长度: 35)
2025-07-30 09:52:17,196 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 09:52:17,197 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:17,197 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 09:52:17,198 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 09:52:17,198 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 09:52:17,199 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 09:52:17,199 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 09:52:17,200 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 09:52:17,200 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 09:52:18,119 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 09:52:23,361 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 09:52:23,362 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 09:52:23,362 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 09:52:23,362 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 09:52:23,364 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:52:23,674 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:52:23,675 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:52:24,577 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:52:24,587 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:52:24,587 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 09:52:24,588 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 09:52:24,589 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 09:52:24,589 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 09:52:25,091 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 09:52:25,091 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 09:52:25,092 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 09:52:25,093 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 09:52:25,094 - modules.friend_request_window - INFO -    📝 内容: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:25,094 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 09:52:25,094 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110115-\xe6\x98\x8c\xe6\x99\x9f-2025-07-30 17:52:16'
2025-07-30 09:52:25,095 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 09:52:26,002 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 09:52:31,244 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 09:52:31,245 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 09:52:31,245 - modules.friend_request_window - INFO -    📝 原始文本: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:31,245 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 09:52:31,246 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:52:31,559 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:52:31,559 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:52:32,462 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:52:32,472 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:52:32,473 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:32,474 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 09:52:32,474 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:32,475 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 09:52:32,975 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:32,976 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 09:52:32,976 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 09:52:32,976 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 09:52:32,977 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 09:52:32,977 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 09:52:32,977 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 09:52:33,778 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 09:52:33,779 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 09:52:33,779 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 09:52:34,387 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:34,387 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 09:52:34,387 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 09:52:34,388 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 09:52:34,903 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:35,137 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:35,368 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:35,603 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:35,833 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:36,067 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:36,305 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:36,540 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:36,770 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:37,003 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:37,238 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:37,467 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:37,698 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:37,928 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:38,158 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:38,390 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:38,621 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:38,851 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:39,082 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:39,314 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:52:39,535 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 09:52:39,535 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 09:52:40,536 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:52:40,539 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 09:52:40,539 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 09:52:40,539 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 09:52:40,540 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 09:52:40,540 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:52:40,540 - modules.friend_request_window - INFO -    📝 备注信息: '014325110115-昌晟-2025-07-30 17:52:16'
2025-07-30 09:52:41,041 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 09:52:41,042 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:41,042 - modules.wechat_auto_add_simple - INFO - ✅ 13621013400 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 09:52:41,043 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13621013400
2025-07-30 09:52:41,044 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:44,603 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2935
2025-07-30 09:52:44,603 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15653484755 (马帅琦)
2025-07-30 09:52:44,603 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:51,161 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15653484755
2025-07-30 09:52:51,161 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:52:51,161 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15653484755 执行添加朋友操作...
2025-07-30 09:52:51,162 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:52:51,162 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:52:51,163 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:52:51,164 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:52:51,168 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 09:52:51,170 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:52:51,170 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:52:51,171 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:52:51,171 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:52:51,172 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:52:51,172 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:52:51,173 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:52:51,181 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:52:51,184 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:52:51,186 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:52:51,192 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 09:52:51,194 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 09:52:51,198 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:52:51,703 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:52:51,704 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 09:52:51,764 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差46.50, 边缘比例0.0400
2025-07-30 09:52:51,773 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095251.png
2025-07-30 09:52:51,775 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:52:51,776 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:52:51,777 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:52:51,778 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:52:51,778 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:52:51,786 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095251.png
2025-07-30 09:52:51,787 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-07-30 09:52:51,788 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,450), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 09:52:51,788 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 09:52:51,790 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 09:52:51,791 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,209), 尺寸2x240, 长宽比0.01, 面积480
2025-07-30 09:52:51,793 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸326x245, 长宽比1.33, 面积79870
2025-07-30 09:52:51,794 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 09:52:51,795 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 09:52:51,796 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 09:52:51,800 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 09:52:51,811 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095251.png
2025-07-30 09:52:51,812 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 09:52:51,815 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 09:52:51,821 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_095251.png
2025-07-30 09:52:51,843 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 09:52:51,845 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 09:52:51,845 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 09:52:51,856 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:52:52,159 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 09:52:52,927 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:52:52,928 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:52:52,931 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:52,932 - modules.wechat_auto_add_simple - INFO - ✅ 15653484755 添加朋友操作执行成功
2025-07-30 09:52:52,932 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:52:52,932 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:52:54,934 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:52:54,934 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:52:54,935 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:52:54,935 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:52:54,935 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:52:54,936 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:52:54,936 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:52:54,936 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:52:54,936 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:52:54,937 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15653484755
2025-07-30 09:52:54,937 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:52:54,938 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:52:54,938 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:52:54,938 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:52:54,938 - modules.friend_request_window - INFO -    📱 phone: '15653484755'
2025-07-30 09:52:54,939 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:52:54,939 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:52:55,475 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:52:55,475 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:52:55,476 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:52:55,476 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:52:55,477 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15653484755
2025-07-30 09:52:55,478 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:52:55,478 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:52:55,479 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:52:55,479 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:52:55,480 - modules.friend_request_window - INFO -    📱 手机号码: 15653484755
2025-07-30 09:52:55,483 - modules.friend_request_window - INFO -    🆔 准考证: 014325110116
2025-07-30 09:52:55,483 - modules.friend_request_window - INFO -    👤 姓名: 马帅琦
2025-07-30 09:52:55,484 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:52:55,485 - modules.friend_request_window - INFO -    📝 备注格式: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:52:55,485 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:52:55,485 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:52:55,486 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:52:55,488 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 6357770, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 09:52:55,491 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 6357770)
2025-07-30 09:52:55,491 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 09:52:55,491 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 09:52:55,492 - modules.friend_request_window - INFO - 🔄 激活窗口: 6357770
2025-07-30 09:52:56,195 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 09:52:56,195 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 09:52:56,196 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 09:52:56,196 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 09:52:56,197 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 09:52:56,198 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:52:56,198 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 09:52:56,198 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:52:56,199 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 09:52:56,199 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 09:52:56,200 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 09:52:56,200 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 09:52:56,200 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 09:52:56,201 - modules.friend_request_window - INFO -    📝 remark参数: '014325110116-马帅琦-2025-07-30 17:52:55' (类型: <class 'str'>, 长度: 36)
2025-07-30 09:52:56,202 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 09:52:56,202 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:52:56,202 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 09:52:56,202 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 09:52:56,203 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 09:52:56,203 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 09:52:56,203 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 09:52:56,203 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 09:52:56,204 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 09:52:57,119 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 09:53:02,361 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 09:53:02,362 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 09:53:02,362 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 09:53:02,362 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 09:53:02,363 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:53:02,676 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:53:02,676 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:53:03,578 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:53:03,589 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:53:03,590 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 09:53:03,591 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 09:53:03,592 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 09:53:03,592 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 09:53:04,093 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 09:53:04,093 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 09:53:04,093 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 09:53:04,094 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 09:53:04,094 - modules.friend_request_window - INFO -    📝 内容: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:53:04,094 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 09:53:04,095 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110116-\xe9\xa9\xac\xe5\xb8\x85\xe7\x90\xa6-2025-07-30 17:52:55'
2025-07-30 09:53:04,095 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 09:53:05,017 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 09:53:10,260 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 09:53:10,260 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 09:53:10,261 - modules.friend_request_window - INFO -    📝 原始文本: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:53:10,261 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 09:53:10,262 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:53:10,571 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:53:10,571 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:53:11,474 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:53:11,483 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:53:11,483 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:53:11,484 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 09:53:11,485 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:53:11,485 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 09:53:11,986 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:53:11,986 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 09:53:11,986 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 09:53:11,987 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 09:53:11,987 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 09:53:11,987 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 09:53:11,988 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 09:53:12,788 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 09:53:12,789 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 09:53:12,789 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 09:53:13,399 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:53:13,399 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 09:53:13,400 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 09:53:13,400 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 09:53:13,916 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:14,145 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:14,375 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:14,606 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:14,837 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:15,069 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:15,298 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:15,532 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:15,762 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:16,009 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:16,246 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:16,477 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:16,708 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:16,938 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:17,169 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:17,403 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:17,637 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:17,868 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:18,097 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:18,331 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:53:18,550 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 09:53:18,550 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 09:53:19,551 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:53:19,553 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 09:53:19,553 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 09:53:19,553 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 09:53:19,553 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 09:53:19,554 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:53:19,554 - modules.friend_request_window - INFO -    📝 备注信息: '014325110116-马帅琦-2025-07-30 17:52:55'
2025-07-30 09:53:20,054 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 09:53:20,055 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:53:20,056 - modules.wechat_auto_add_simple - INFO - ✅ 15653484755 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 09:53:20,056 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15653484755
2025-07-30 09:53:20,057 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:53:21,390 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 09:53:21,391 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 09:53:21,391 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 09:53:21,392 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 09:53:21,392 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 09:53:21,393 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 09:53:21,393 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 09:53:21,393 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 09:53:21,393 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 09:53:21,394 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 09:53:21,394 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 09:53:21,395 - __main__ - INFO - � 更新全局进度：已处理 2/2935 个联系人
2025-07-30 09:53:21,396 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 09:53:24,397 - __main__ - INFO - 
============================================================
2025-07-30 09:53:24,398 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 09:53:24,398 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 09:53:24,398 - __main__ - INFO - 📊 全局进度：已处理 2/2935 个联系人
2025-07-30 09:53:24,399 - __main__ - INFO - ============================================================
2025-07-30 09:53:24,399 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 09:53:24,399 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 09:53:24,399 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 09:53:24,400 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 09:53:24,400 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 09:53:24,721 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:53:24,721 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:53:24,721 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:53:24,722 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:53:24,723 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:53:24,723 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:53:24,723 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:53:24,724 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:53:24,724 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:53:24,724 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:53:24,926 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:53:24,927 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:53:24,927 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 09:53:24,927 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 09:53:25,230 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:53:25,231 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:53:25,231 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:53:25,231 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:53:25,232 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:53:25,232 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:53:25,232 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:53:25,233 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:53:25,233 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:53:25,233 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:53:25,435 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:53:25,436 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:53:25,439 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 09:53:25,740 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:53:25,740 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:53:25,741 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:53:25,741 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:53:25,741 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:53:25,741 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:53:25,742 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 09:53:25,742 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 09:53:26,742 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 09:53:26,743 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 09:53:26,743 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 09:53:26,744 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 09:53:26,744 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 09:53:26,745 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 09:53:26,746 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 09:53:26,746 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 09:53:26,947 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 09:53:26,948 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 09:53:29,331 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 09:53:29,331 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 09:53:29,332 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 09:53:31,801 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 09:53:32,003 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 09:53:32,003 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 09:53:34,372 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 09:53:34,372 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 09:53:34,372 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 09:53:35,965 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 09:53:36,166 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 09:53:36,166 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 09:53:38,548 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 09:53:38,548 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 09:53:38,549 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 09:53:41,071 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 09:53:41,272 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 09:53:41,273 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 09:53:43,647 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 09:53:43,648 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 09:53:43,648 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 09:53:46,262 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 09:53:46,463 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 09:53:46,463 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 09:53:48,848 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 09:53:48,849 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 09:53:48,849 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:53:48,849 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:53:48,850 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:53:48,851 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:53:48,852 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 09:53:48,853 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:53:48,853 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:53:48,854 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:53:48,856 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4852670, 进程: Weixin.exe)
2025-07-30 09:53:48,859 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 09:53:48,863 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3148976)
2025-07-30 09:53:48,864 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148976) - 增强版
2025-07-30 09:53:49,184 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:53:49,185 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:53:49,185 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:53:49,185 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:53:49,186 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 09:53:49,186 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:53:49,391 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 09:53:49,391 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:53:49,593 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:53:49,594 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:53:49,594 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 09:53:49,594 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 09:53:49,594 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 09:53:49,595 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 09:53:49,595 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 09:53:50,596 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 09:53:50,596 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:53:50,598 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:53:50,599 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:53:50,599 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 09:53:50,600 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:53:50,601 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:53:50,602 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4852670, 进程: Weixin.exe)
2025-07-30 09:53:50,606 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 09:53:50,607 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 09:53:50,609 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 09:53:50,609 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 09:53:50,610 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 09:53:50,610 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 09:53:50,929 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:53:50,930 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:53:50,930 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:53:50,930 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:53:50,931 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:53:50,931 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:53:50,931 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:53:50,931 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:53:50,932 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:53:50,932 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:53:51,134 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:53:51,134 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:53:51,135 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 09:53:51,436 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:53:51,436 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 09:53:51,437 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 09:53:52,437 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 09:53:52,438 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 09:53:52,438 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 09:53:52,441 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_095352.log
2025-07-30 09:53:52,442 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:53:52,443 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 09:53:52,443 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 09:53:52,444 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 09:53:52,444 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 09:53:52,445 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 09:53:52,448 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 09:53:52,449 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 09:53:52,450 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 09:53:52,450 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 09:53:52,451 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 09:53:52,451 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 09:53:52,451 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 09:53:52,451 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 09:53:52,452 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:53:52,453 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4852670
2025-07-30 09:53:52,453 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4852670) - 增强版
2025-07-30 09:53:52,761 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:53:52,762 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:53:52,762 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:53:52,763 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:53:52,763 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 09:53:52,763 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:53:52,966 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 09:53:52,966 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:53:53,168 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:53:53,169 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:53:53,172 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4852670 (API返回: None)
2025-07-30 09:53:53,473 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:53:53,473 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 09:53:53,474 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 09:53:53,474 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 09:53:53,475 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:53:53,475 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 09:53:53,475 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 09:53:53,481 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 09:53:53,481 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 09:53:53,967 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 09:53:53,967 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:53:54,222 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2933 个
2025-07-30 09:53:54,223 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 09:53:54,224 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2931 个
2025-07-30 09:53:54,224 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2931 个 (总计: 3135 个)
2025-07-30 09:53:54,224 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 09:53:54,224 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 09:53:54,225 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:53:54,225 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 09:53:54,225 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2931
2025-07-30 09:53:54,225 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18627098009 (张曼)
2025-07-30 09:53:54,226 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:00,808 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18627098009
2025-07-30 09:54:00,808 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:54:00,809 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18627098009 执行添加朋友操作...
2025-07-30 09:54:00,809 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:54:00,809 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:54:00,810 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:54:00,811 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:54:00,816 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 09:54:00,818 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:54:00,819 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:54:00,819 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:54:00,821 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:54:00,821 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:54:00,822 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:54:00,823 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:54:00,829 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:54:00,832 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:54:00,834 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:54:00,836 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:54:00,838 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 09:54:00,840 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 09:54:00,841 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:54:01,346 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:54:01,347 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 09:54:01,413 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 09:54:01,414 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 09:54:01,421 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095401.png
2025-07-30 09:54:01,423 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:54:01,424 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:54:01,426 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:54:01,428 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:54:01,432 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:54:01,439 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095401.png
2025-07-30 09:54:01,440 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-07-30 09:54:01,441 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,450), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 09:54:01,443 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 09:54:01,446 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 09:54:01,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,209), 尺寸2x240, 长宽比0.01, 面积480
2025-07-30 09:54:01,449 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸326x245, 长宽比1.33, 面积79870
2025-07-30 09:54:01,450 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 09:54:01,452 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 09:54:01,453 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 09:54:01,454 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 09:54:01,462 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095401.png
2025-07-30 09:54:01,465 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 09:54:01,466 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 09:54:01,470 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_095401.png
2025-07-30 09:54:01,491 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 09:54:01,495 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 09:54:01,498 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 09:54:01,500 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:54:01,801 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 09:54:02,569 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:54:02,570 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:54:02,572 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:02,573 - modules.wechat_auto_add_simple - INFO - ✅ 18627098009 添加朋友操作执行成功
2025-07-30 09:54:02,573 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:02,574 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:54:04,576 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:54:04,576 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:54:04,577 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:54:04,577 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:54:04,577 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:54:04,578 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:54:04,578 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:54:04,578 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:54:04,578 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:54:04,579 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18627098009
2025-07-30 09:54:04,579 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:54:04,580 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:54:04,580 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:54:04,580 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:54:04,581 - modules.friend_request_window - INFO -    📱 phone: '18627098009'
2025-07-30 09:54:04,581 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:54:04,581 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:54:05,060 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:54:05,061 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:54:05,061 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:54:05,061 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:54:05,062 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18627098009
2025-07-30 09:54:05,063 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:54:05,064 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:54:05,064 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:54:05,064 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:54:05,065 - modules.friend_request_window - INFO -    📱 手机号码: 18627098009
2025-07-30 09:54:05,065 - modules.friend_request_window - INFO -    🆔 准考证: 014325110120
2025-07-30 09:54:05,065 - modules.friend_request_window - INFO -    👤 姓名: 张曼
2025-07-30 09:54:05,065 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:54:05,066 - modules.friend_request_window - INFO -    📝 备注格式: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:05,066 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:54:05,067 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:05,068 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:54:05,070 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8194806, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 09:54:05,074 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8194806)
2025-07-30 09:54:05,075 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 09:54:05,075 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 09:54:05,076 - modules.friend_request_window - INFO - 🔄 激活窗口: 8194806
2025-07-30 09:54:05,779 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 09:54:05,780 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 09:54:05,780 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 09:54:05,780 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 09:54:05,781 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 09:54:05,781 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:54:05,781 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 09:54:05,781 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:54:05,782 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 09:54:05,782 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 09:54:05,782 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 09:54:05,782 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 09:54:05,783 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 09:54:05,783 - modules.friend_request_window - INFO -    📝 remark参数: '014325110120-张曼-2025-07-30 17:54:05' (类型: <class 'str'>, 长度: 35)
2025-07-30 09:54:05,784 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 09:54:05,784 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:05,784 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 09:54:05,784 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 09:54:05,785 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 09:54:05,785 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 09:54:05,785 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 09:54:05,787 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 09:54:05,787 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 09:54:06,697 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 09:54:11,940 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 09:54:11,941 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 09:54:11,941 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 09:54:11,941 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 09:54:11,942 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:54:12,251 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:54:12,252 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:54:13,155 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:54:13,165 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:54:13,166 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 09:54:13,166 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 09:54:13,168 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 09:54:13,168 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 09:54:13,669 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 09:54:13,669 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 09:54:13,669 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 09:54:13,670 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 09:54:13,670 - modules.friend_request_window - INFO -    📝 内容: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:13,670 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 09:54:13,671 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110120-\xe5\xbc\xa0\xe6\x9b\xbc-2025-07-30 17:54:05'
2025-07-30 09:54:13,671 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 09:54:14,577 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 09:54:19,819 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 09:54:19,819 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 09:54:19,819 - modules.friend_request_window - INFO -    📝 原始文本: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:19,820 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 09:54:19,820 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:54:20,130 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:54:20,130 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:54:21,032 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:54:21,041 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:54:21,041 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:21,042 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 09:54:21,043 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:21,044 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 09:54:21,545 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:21,546 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 09:54:21,546 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 09:54:21,546 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 09:54:21,546 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 09:54:21,547 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 09:54:21,547 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 09:54:22,348 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 09:54:22,348 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 09:54:22,349 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 09:54:22,963 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:22,963 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 09:54:22,963 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 09:54:22,963 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 09:54:23,465 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 09:54:23,467 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 09:54:23,468 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 09:54:23,468 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 09:54:23,468 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 09:54:23,469 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 09:54:23,469 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 09:54:23,469 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 09:54:23,470 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 09:54:23,470 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 09:54:23,470 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 09:54:23,470 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 09:54:23,471 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 09:54:23,471 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 09:54:23,472 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 09:54:23,474 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 09:54:23,476 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 09:54:23,477 - modules.friend_request_window - INFO - 📊 窗口 Weixin 错误次数: 1/3
2025-07-30 09:54:23,504 - modules.friend_request_window - INFO - 📊 窗口状态统计: 0/5 个窗口已失败
2025-07-30 09:54:23,504 - modules.friend_request_window - INFO - 🔄 步骤1: 点击错误提示窗口的'确定'按钮...
2025-07-30 09:54:23,505 - modules.friend_request_window - INFO - 🎯 开始使用坐标定位点击错误对话框的确定按钮...
2025-07-30 09:54:23,505 - modules.friend_request_window - INFO - 🔍 智能查找确定按钮位置...
2025-07-30 09:54:23,507 - modules.friend_request_window - INFO - 🔍 发现错误对话框: Weixin (Qt51514QWindowIcon) 330x194
2025-07-30 09:54:23,513 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 09:54:23,514 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 09:54:23,515 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 09:54:23,515 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 09:54:23,516 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1854x978
2025-07-30 09:54:23,517 - modules.friend_request_window - INFO - 📊 共发现 6 个可能的错误对话框
2025-07-30 09:54:23,517 - modules.friend_request_window - INFO - 🎯 尝试点击对话框: Weixin (大小: 330x194)
2025-07-30 09:54:23,517 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 09:54:23,518 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 09:54:23,518 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 09:54:23,518 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 09:54:23,519 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 09:54:23,519 - modules.friend_request_window - INFO - 🎯 开始坐标点击操作，目标坐标: (1364, 252)
2025-07-30 09:54:23,519 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 09:54:23,519 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 09:54:24,428 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 09:54:24,429 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 09:54:24,429 - modules.friend_request_window - INFO - ✅ 成功点击错误对话框确定按钮: Weixin
2025-07-30 09:54:24,429 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 09:54:25,430 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭出现频繁错误的微信窗口...
2025-07-30 09:54:25,431 - modules.friend_request_window - INFO - 🎯 使用坐标点击关闭微信窗口: Weixin
2025-07-30 09:54:25,431 - modules.friend_request_window - INFO - 📋 目标窗口句柄: 5245668
2025-07-30 09:54:25,432 - modules.friend_request_window - INFO - 📋 执行顺序: 1.关闭添加朋友窗口 → 2.关闭微信主窗口
2025-07-30 09:54:25,432 - modules.friend_request_window - INFO - 🔄 步骤1: 使用坐标关闭添加朋友窗口...
2025-07-30 09:54:25,433 - modules.friend_request_window - INFO - 🎯 使用坐标点击关闭添加朋友窗口...
2025-07-30 09:54:25,433 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-30 09:54:25,934 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-30 09:54:27,046 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-30 09:54:27,046 - modules.friend_request_window - INFO - 🔄 步骤2: 使用坐标关闭微信主窗口...
2025-07-30 09:54:27,047 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-30 09:54:27,548 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-30 09:54:28,661 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-30 09:54:28,661 - modules.friend_request_window - INFO - ✅ 坐标关闭完成
2025-07-30 09:54:28,662 - modules.friend_request_window - INFO - 🔄 步骤3: 执行最终清理操作...
2025-07-30 09:54:28,662 - modules.friend_request_window - INFO - 🧹 简化清理残留窗口...
2025-07-30 09:54:29,663 - modules.friend_request_window - INFO - ✅ 清理完成
2025-07-30 09:54:29,663 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 09:54:29,664 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-30 09:54:29,664 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 09:54:29,664 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 09:54:29,665 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 09:54:29,665 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 09:54:29,665 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 09:54:29,665 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:54:29,666 - modules.friend_request_window - INFO -    📝 备注信息: '014325110120-张曼-2025-07-30 17:54:05'
2025-07-30 09:54:30,166 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 09:54:30,167 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:30,168 - modules.wechat_auto_add_simple - INFO - ✅ 18627098009 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 09:54:30,168 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18627098009
2025-07-30 09:54:30,169 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:33,689 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2931
2025-07-30 09:54:33,690 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17729513165 (李星)
2025-07-30 09:54:33,690 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:40,253 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17729513165
2025-07-30 09:54:40,253 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:54:40,253 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17729513165 执行添加朋友操作...
2025-07-30 09:54:40,254 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:54:40,254 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:54:40,255 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:54:40,256 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:54:40,261 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 09:54:40,264 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:54:40,265 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:54:40,265 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:54:40,266 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:54:40,266 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:54:40,268 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:54:40,269 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:54:40,272 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:54:40,274 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:54:40,279 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 09:54:40,282 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 09:54:40,284 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:54:40,787 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:54:40,789 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 328x454
2025-07-30 09:54:40,864 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差44.50, 边缘比例0.0388
2025-07-30 09:54:40,873 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095440.png
2025-07-30 09:54:40,876 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:54:40,880 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:54:40,881 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:54:40,883 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:54:40,884 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:54:40,889 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095440.png
2025-07-30 09:54:40,894 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-07-30 09:54:40,895 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(325,452), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 09:54:40,897 - WeChatAutoAdd - INFO - 重要轮廓: 位置(108,236), 尺寸112x30, 长宽比3.73, 已知特征:False
2025-07-30 09:54:40,899 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度112 (需要70-95)
2025-07-30 09:54:40,902 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(3,209), 尺寸325x245, 长宽比1.33, 面积79625
2025-07-30 09:54:40,903 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸2x245, 长宽比0.01, 面积490
2025-07-30 09:54:40,904 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 09:54:40,906 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 09:54:40,911 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 112x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 09:54:40,913 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 09:54:40,921 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095440.png
2025-07-30 09:54:40,922 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 09:54:40,925 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 09:54:40,931 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_095440.png
2025-07-30 09:54:40,951 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 09:54:40,955 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0327, 平均亮度=249.2, 亮度标准差=13.1
2025-07-30 09:54:40,957 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 09:54:40,962 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:54:41,264 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(164, 251)
2025-07-30 09:54:42,043 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:54:42,045 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:54:42,046 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:42,047 - modules.wechat_auto_add_simple - INFO - ✅ 17729513165 添加朋友操作执行成功
2025-07-30 09:54:42,047 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:42,047 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:54:44,049 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:54:44,050 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:54:44,050 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:54:44,051 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:54:44,051 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:54:44,051 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:54:44,052 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:54:44,052 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:54:44,052 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:54:44,052 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17729513165
2025-07-30 09:54:44,053 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:54:44,053 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:54:44,054 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:54:44,054 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:54:44,054 - modules.friend_request_window - INFO -    📱 phone: '17729513165'
2025-07-30 09:54:44,054 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:54:44,055 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:54:44,534 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:54:44,534 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:54:44,535 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:54:44,535 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:54:44,536 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17729513165
2025-07-30 09:54:44,536 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:54:44,537 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:54:44,537 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:54:44,538 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:54:44,538 - modules.friend_request_window - INFO -    📱 手机号码: 17729513165
2025-07-30 09:54:44,538 - modules.friend_request_window - INFO -    🆔 准考证: 014325110123
2025-07-30 09:54:44,538 - modules.friend_request_window - INFO -    👤 姓名: 李星
2025-07-30 09:54:44,539 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:54:44,539 - modules.friend_request_window - INFO -    📝 备注格式: '014325110123-李星-2025-07-30 17:54:44'
2025-07-30 09:54:44,541 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:54:44,541 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110123-李星-2025-07-30 17:54:44'
2025-07-30 09:54:44,542 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:54:44,547 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 09:54:44,548 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 09:54:44,549 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:44,550 - modules.wechat_auto_add_simple - INFO - ✅ 17729513165 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 09:54:44,550 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17729513165
2025-07-30 09:54:44,551 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:54:45,904 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 09:54:45,904 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 09:54:45,905 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 09:54:45,906 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 09:54:45,907 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 09:54:45,907 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 09:54:45,907 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 09:54:45,907 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 09:54:45,908 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 09:54:45,908 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-30 09:54:45,908 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 09:54:45,909 - __main__ - INFO - � 更新全局进度：已处理 4/2935 个联系人
2025-07-30 09:54:45,909 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 09:54:48,910 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 09:54:48,911 - __main__ - INFO - 📊 当前进度：已处理 4/2935 个联系人
2025-07-30 09:54:48,911 - __main__ - INFO - 
============================================================
2025-07-30 09:54:48,911 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 2 轮)
2025-07-30 09:54:48,912 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 09:54:48,912 - __main__ - INFO - 📊 全局进度：已处理 4/2935 个联系人
2025-07-30 09:54:48,912 - __main__ - INFO - ============================================================
2025-07-30 09:54:48,912 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 09:54:48,913 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 09:54:48,913 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 09:54:48,913 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 09:54:48,913 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:54:49,223 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:54:49,223 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:54:49,223 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:54:49,224 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:54:49,224 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:54:49,224 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:54:49,225 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:54:49,225 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:54:49,225 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:54:49,225 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:54:49,427 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:54:49,427 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:54:49,428 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 09:54:49,428 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:54:49,731 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:54:49,731 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:54:49,732 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:54:49,732 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:54:49,732 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:54:49,733 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:54:49,733 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:54:49,733 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:54:49,734 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:54:49,734 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:54:49,936 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:54:49,936 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:54:49,938 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 09:54:50,239 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:54:50,240 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:54:50,240 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:54:50,240 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:54:50,240 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:54:50,241 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:54:50,241 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 09:54:50,241 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 09:54:51,242 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 09:54:51,242 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 09:54:51,243 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 09:54:51,243 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 09:54:51,243 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 09:54:51,244 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 09:54:51,244 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 09:54:51,244 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 09:54:51,445 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 09:54:51,446 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 09:54:53,826 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 09:54:53,827 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 09:54:53,827 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 09:54:56,267 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 09:54:56,468 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 09:54:56,469 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 09:54:58,842 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 09:54:58,842 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 09:54:58,842 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 09:55:01,679 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 09:55:01,880 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 09:55:01,880 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 09:55:04,259 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 09:55:04,260 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 09:55:04,260 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 09:55:06,903 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 09:55:07,105 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 09:55:07,106 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 09:55:09,474 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 09:55:09,475 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 09:55:09,475 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-30 09:55:12,432 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 09:55:12,633 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 09:55:12,634 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 09:55:15,008 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 09:55:15,008 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 09:55:15,009 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:55:15,009 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:55:15,009 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:55:15,011 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:55:15,011 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:55:15,012 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:55:15,014 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 09:55:15,014 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3148976)
2025-07-30 09:55:15,015 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148976) - 增强版
2025-07-30 09:55:15,318 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:55:15,318 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:55:15,318 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:55:15,319 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:55:15,319 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 09:55:15,319 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:55:15,320 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 09:55:15,320 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:55:15,522 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:55:15,522 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:55:15,523 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 09:55:15,523 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 09:55:15,523 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 09:55:15,523 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 09:55:15,524 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 09:55:16,524 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 09:55:16,525 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:55:16,526 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:55:16,527 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:55:16,527 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:55:16,529 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 09:55:16,530 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 09:55:16,530 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 09:55:16,531 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 09:55:16,531 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 09:55:16,531 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:55:16,841 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:55:16,841 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:55:16,841 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:55:16,842 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:55:16,842 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:55:16,842 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:55:16,843 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:55:16,843 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:55:16,843 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:55:16,844 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:55:17,046 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:55:17,046 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:55:17,048 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 09:55:17,349 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:55:17,349 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 09:55:17,349 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 09:55:18,350 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 09:55:18,350 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 09:55:18,350 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 09:55:18,353 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_095518.log
2025-07-30 09:55:18,354 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:55:18,354 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 09:55:18,355 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 09:55:18,355 - __main__ - INFO - 🔄 传递全局联系人索引: 4
2025-07-30 09:55:18,356 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 09:55:18,356 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 09:55:18,358 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 09:55:18,359 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 09:55:18,359 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 09:55:18,359 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 09:55:18,360 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 09:55:18,360 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 09:55:18,361 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:55:18,362 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3148976
2025-07-30 09:55:18,362 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148976) - 增强版
2025-07-30 09:55:18,670 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:55:18,670 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:55:18,671 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:55:18,672 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:55:18,672 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 09:55:18,672 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:55:18,672 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 09:55:18,673 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:55:18,875 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:55:18,875 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:55:18,877 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3148976 (API返回: None)
2025-07-30 09:55:19,177 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:55:19,178 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 09:55:19,178 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 09:55:19,178 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 09:55:19,179 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:55:19,180 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 09:55:19,180 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 09:55:19,184 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 09:55:19,185 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 09:55:19,675 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 09:55:19,676 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:55:19,937 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2931 个
2025-07-30 09:55:19,938 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 5 个联系人开始处理
2025-07-30 09:55:19,938 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2927 个
2025-07-30 09:55:19,939 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2927 个 (总计: 3135 个)
2025-07-30 09:55:19,939 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 09:55:19,939 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 09:55:19,940 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:55:19,940 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 09:55:19,940 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2927
2025-07-30 09:55:19,940 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13084582015 (胡硕)
2025-07-30 09:55:19,941 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:55:26,516 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13084582015
2025-07-30 09:55:26,517 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:55:26,517 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13084582015 执行添加朋友操作...
2025-07-30 09:55:26,517 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:55:26,518 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:55:26,518 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:55:26,520 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:55:26,525 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 09:55:26,526 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:55:26,527 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:55:26,527 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:55:26,527 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:55:26,527 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:55:26,528 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:55:26,528 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:55:26,532 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:55:26,534 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:55:26,536 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-30 09:55:26,537 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:55:27,039 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:55:27,041 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 09:55:27,100 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 09:55:27,101 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 09:55:27,109 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095527.png
2025-07-30 09:55:27,111 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:55:27,113 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:55:27,114 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:55:27,117 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:55:27,118 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:55:27,123 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095527.png
2025-07-30 09:55:27,124 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 09:55:27,126 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 09:55:27,128 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 09:55:27,130 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 09:55:27,131 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 09:55:27,132 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 09:55:27,134 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 09:55:27,135 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 09:55:27,144 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095527.png
2025-07-30 09:55:27,146 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 09:55:27,148 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 09:55:27,153 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_095527.png
2025-07-30 09:55:27,170 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 09:55:27,172 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 09:55:27,174 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 09:55:27,176 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:55:27,478 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 09:55:28,246 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:55:28,248 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:55:28,250 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:55:28,250 - modules.wechat_auto_add_simple - INFO - ✅ 13084582015 添加朋友操作执行成功
2025-07-30 09:55:28,251 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:55:28,251 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:55:30,252 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:55:30,253 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:55:30,253 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:55:30,253 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:55:30,253 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:55:30,254 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:55:30,254 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:55:30,254 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:55:30,255 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:55:30,255 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13084582015
2025-07-30 09:55:30,255 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:55:30,256 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:55:30,256 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:55:30,256 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:55:30,257 - modules.friend_request_window - INFO -    📱 phone: '13084582015'
2025-07-30 09:55:30,257 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:55:30,257 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:55:30,730 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:55:30,731 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:55:30,732 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:55:30,732 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:55:30,733 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13084582015
2025-07-30 09:55:30,733 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:55:30,734 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:55:30,734 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:55:30,734 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:55:30,735 - modules.friend_request_window - INFO -    📱 手机号码: 13084582015
2025-07-30 09:55:30,735 - modules.friend_request_window - INFO -    🆔 准考证: 014325110127
2025-07-30 09:55:30,735 - modules.friend_request_window - INFO -    👤 姓名: 胡硕
2025-07-30 09:55:30,736 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:55:30,736 - modules.friend_request_window - INFO -    📝 备注格式: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:30,736 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:55:30,737 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:30,737 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:55:30,738 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 9635814, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 09:55:30,740 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 9635814)
2025-07-30 09:55:30,740 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 09:55:30,740 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 09:55:30,740 - modules.friend_request_window - INFO - 🔄 激活窗口: 9635814
2025-07-30 09:55:31,443 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 09:55:31,444 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 09:55:31,444 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 09:55:31,445 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 09:55:31,445 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 09:55:31,445 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:55:31,446 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 09:55:31,446 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:55:31,446 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 09:55:31,446 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 09:55:31,447 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 09:55:31,447 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 09:55:31,447 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 09:55:31,448 - modules.friend_request_window - INFO -    📝 remark参数: '014325110127-胡硕-2025-07-30 17:55:30' (类型: <class 'str'>, 长度: 35)
2025-07-30 09:55:31,448 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 09:55:31,448 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:31,448 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 09:55:31,449 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 09:55:31,449 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 09:55:31,449 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 09:55:31,450 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 09:55:31,450 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 09:55:31,450 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 09:55:32,357 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 09:55:37,597 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 09:55:37,597 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 09:55:37,598 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 09:55:37,598 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 09:55:37,599 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:55:37,907 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:55:37,907 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:55:38,809 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:55:38,817 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:55:38,817 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 09:55:38,818 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 09:55:38,819 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 09:55:38,819 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 09:55:39,320 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 09:55:39,321 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 09:55:39,321 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 09:55:39,321 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 09:55:39,322 - modules.friend_request_window - INFO -    📝 内容: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:39,322 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 09:55:39,323 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110127-\xe8\x83\xa1\xe7\xa1\x95-2025-07-30 17:55:30'
2025-07-30 09:55:39,323 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 09:55:40,240 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 09:55:45,690 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 09:55:45,690 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 09:55:45,690 - modules.friend_request_window - INFO -    📝 原始文本: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:45,691 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 09:55:45,691 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:55:46,002 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:55:46,002 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:55:46,905 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:55:46,915 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:55:46,915 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:46,916 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 09:55:46,916 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:46,916 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 09:55:47,417 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:47,418 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 09:55:47,418 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 09:55:47,418 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 09:55:47,419 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 09:55:47,419 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 09:55:47,419 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 09:55:48,220 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 09:55:48,220 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 09:55:48,221 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 09:55:48,842 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:55:48,843 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 09:55:48,844 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 09:55:48,844 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 09:55:49,362 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:49,611 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:49,869 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:50,108 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:50,344 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:50,594 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:50,848 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:51,100 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:51,336 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:51,570 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:51,803 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:52,039 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:52,274 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:52,506 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:52,737 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:52,968 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:53,201 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:53,433 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:53,664 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:55:53,880 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 09:55:53,880 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 09:55:54,881 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:55:54,885 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 09:55:54,885 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 09:55:54,886 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 09:55:54,886 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 09:55:54,887 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:55:54,887 - modules.friend_request_window - INFO -    📝 备注信息: '014325110127-胡硕-2025-07-30 17:55:30'
2025-07-30 09:55:55,388 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 09:55:55,390 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:55:55,391 - modules.wechat_auto_add_simple - INFO - ✅ 13084582015 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 09:55:55,392 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13084582015
2025-07-30 09:55:55,393 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:55:59,822 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2927
2025-07-30 09:55:59,822 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13269162871 (方一芃)
2025-07-30 09:55:59,822 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:56:06,401 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13269162871
2025-07-30 09:56:06,402 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:56:06,402 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13269162871 执行添加朋友操作...
2025-07-30 09:56:06,402 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:56:06,403 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:56:06,404 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:56:06,406 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:56:06,411 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 09:56:06,414 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:56:06,415 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:56:06,415 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:56:06,415 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:56:06,416 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:56:06,416 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:56:06,416 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:56:06,422 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:56:06,425 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:56:06,428 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-30 09:56:06,430 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:56:06,933 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:56:06,935 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 09:56:07,004 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差35.57, 边缘比例0.0383
2025-07-30 09:56:07,013 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095607.png
2025-07-30 09:56:07,016 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:56:07,018 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:56:07,021 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:56:07,023 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:56:07,026 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:56:07,032 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095607.png
2025-07-30 09:56:07,035 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 09:56:07,038 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 09:56:07,041 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 09:56:07,044 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 09:56:07,046 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 09:56:07,049 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 09:56:07,052 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 09:56:07,056 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 09:56:07,067 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095607.png
2025-07-30 09:56:07,069 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 09:56:07,072 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 09:56:07,076 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_095607.png
2025-07-30 09:56:07,101 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 09:56:07,105 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 09:56:07,109 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 09:56:07,112 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:56:07,416 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 09:56:08,185 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:56:08,188 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:56:08,193 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:56:08,193 - modules.wechat_auto_add_simple - INFO - ✅ 13269162871 添加朋友操作执行成功
2025-07-30 09:56:08,195 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:56:08,195 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:56:10,197 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:56:10,197 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:56:10,197 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:56:10,198 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:56:10,198 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:56:10,198 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:56:10,199 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:56:10,199 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:56:10,199 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:56:10,199 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13269162871
2025-07-30 09:56:10,200 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:56:10,200 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:56:10,201 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:56:10,201 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:56:10,201 - modules.friend_request_window - INFO -    📱 phone: '13269162871'
2025-07-30 09:56:10,202 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:56:10,202 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:56:10,731 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:56:10,732 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:56:10,732 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:56:10,732 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:56:10,734 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13269162871
2025-07-30 09:56:10,734 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:56:10,735 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:56:10,735 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:56:10,735 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:56:10,736 - modules.friend_request_window - INFO -    📱 手机号码: 13269162871
2025-07-30 09:56:10,737 - modules.friend_request_window - INFO -    🆔 准考证: 014325110128
2025-07-30 09:56:10,737 - modules.friend_request_window - INFO -    👤 姓名: 方一芃
2025-07-30 09:56:10,737 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:56:10,738 - modules.friend_request_window - INFO -    📝 备注格式: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:10,738 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:56:10,739 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:10,739 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:56:10,740 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3607428, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 09:56:10,742 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3607428)
2025-07-30 09:56:10,742 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 09:56:10,742 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 09:56:10,743 - modules.friend_request_window - INFO - 🔄 激活窗口: 3607428
2025-07-30 09:56:11,445 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 09:56:11,445 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 09:56:11,446 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 09:56:11,446 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 09:56:11,446 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 09:56:11,447 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:56:11,447 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 09:56:11,447 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:56:11,447 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 09:56:11,447 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 09:56:11,448 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 09:56:11,448 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 09:56:11,448 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 09:56:11,448 - modules.friend_request_window - INFO -    📝 remark参数: '014325110128-方一芃-2025-07-30 17:56:10' (类型: <class 'str'>, 长度: 36)
2025-07-30 09:56:11,449 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 09:56:11,449 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:11,449 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 09:56:11,449 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 09:56:11,450 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 09:56:11,450 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 09:56:11,450 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 09:56:11,450 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 09:56:11,451 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 09:56:12,371 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 09:56:17,613 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 09:56:17,613 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 09:56:17,613 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 09:56:17,614 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 09:56:17,614 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:56:17,924 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:56:17,924 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:56:18,827 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:56:18,835 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:56:18,836 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 09:56:18,836 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 09:56:18,837 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 09:56:18,837 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 09:56:19,338 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 09:56:19,339 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 09:56:19,339 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 09:56:19,339 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 09:56:19,340 - modules.friend_request_window - INFO -    📝 内容: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:19,340 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 09:56:19,340 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110128-\xe6\x96\xb9\xe4\xb8\x80\xe8\x8a\x83-2025-07-30 17:56:10'
2025-07-30 09:56:19,341 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 09:56:20,254 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 09:56:25,493 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 09:56:25,493 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 09:56:25,494 - modules.friend_request_window - INFO -    📝 原始文本: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:25,494 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 09:56:25,495 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：智能查找确定按钮位置，备用固定坐标点击...' (前50字符)
2025-07-30 09:56:25,804 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 09:56:25,805 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 09:56:26,707 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 09:56:26,718 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 09:56:26,718 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:26,720 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 09:56:26,720 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:26,720 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 09:56:27,221 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:27,222 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 09:56:27,222 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 09:56:27,222 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 09:56:27,223 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 09:56:27,223 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 09:56:27,223 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 09:56:28,024 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 09:56:28,024 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 09:56:28,024 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 09:56:28,637 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:56:28,637 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 09:56:28,637 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 09:56:28,638 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 09:56:29,155 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:29,387 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:29,626 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:29,859 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:30,092 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:30,324 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:30,557 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:30,790 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:31,022 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:31,256 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:31,488 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:31,719 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:31,951 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:32,182 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:32,415 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:32,648 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:32,879 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:33,112 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:33,343 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:33,574 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 09:56:33,790 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 09:56:33,791 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 09:56:34,791 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:56:34,794 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 09:56:34,794 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 09:56:34,794 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 09:56:34,795 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 09:56:34,795 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:56:34,795 - modules.friend_request_window - INFO -    📝 备注信息: '014325110128-方一芃-2025-07-30 17:56:10'
2025-07-30 09:56:35,296 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 09:56:35,297 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:56:35,297 - modules.wechat_auto_add_simple - INFO - ✅ 13269162871 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 09:56:35,298 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13269162871
2025-07-30 09:56:35,299 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:56:36,647 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 09:56:36,648 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 09:56:36,648 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 09:56:36,649 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 09:56:36,650 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 09:56:36,650 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 09:56:36,650 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 09:56:36,650 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 09:56:36,651 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 09:56:36,651 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 09:56:36,651 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 09:56:36,652 - __main__ - INFO - � 更新全局进度：已处理 6/2935 个联系人
2025-07-30 09:56:36,653 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 09:56:39,653 - __main__ - INFO - 
============================================================
2025-07-30 09:56:39,654 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 2 轮)
2025-07-30 09:56:39,654 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 09:56:39,654 - __main__ - INFO - 📊 全局进度：已处理 6/2935 个联系人
2025-07-30 09:56:39,655 - __main__ - INFO - ============================================================
2025-07-30 09:56:39,655 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 09:56:39,655 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 09:56:39,655 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 09:56:39,656 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 09:56:39,656 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 09:56:39,657 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 09:56:40,182 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:56:40,183 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:56:40,183 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:56:40,184 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:56:40,184 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:56:40,184 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:56:40,185 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:56:40,185 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:56:40,185 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:56:40,186 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:56:40,387 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:56:40,388 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:56:40,388 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 09:56:40,388 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 09:56:40,695 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:56:40,695 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:56:40,696 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:56:40,696 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:56:40,696 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:56:40,697 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:56:40,697 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:56:40,697 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:56:40,697 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:56:40,698 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:56:40,900 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:56:40,901 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:56:40,902 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 09:56:41,203 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:56:41,203 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:56:41,204 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:56:41,204 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:56:41,204 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:56:41,205 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:56:41,205 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 09:56:41,205 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 09:56:42,206 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 09:56:42,206 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 09:56:42,207 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 09:56:42,207 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 09:56:42,207 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 09:56:42,208 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 09:56:42,208 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 09:56:42,208 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 09:56:42,409 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 09:56:42,410 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 09:56:44,785 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 09:56:44,786 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 09:56:44,786 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 09:56:47,682 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 09:56:47,883 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 09:56:47,883 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 09:56:50,252 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 09:56:50,252 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 09:56:50,252 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 09:56:53,115 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 09:56:53,316 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 09:56:53,317 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 09:56:55,685 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 09:56:55,685 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 09:56:55,685 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 09:56:58,295 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 09:56:58,496 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 09:56:58,497 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 09:57:00,864 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 09:57:00,865 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 09:57:00,865 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 09:57:03,328 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 09:57:03,529 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 09:57:03,529 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 09:57:05,901 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 09:57:05,902 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 09:57:05,902 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:57:05,902 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 09:57:05,903 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:57:05,904 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:57:05,905 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 09:57:05,905 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:57:05,906 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:57:05,906 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:57:05,907 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3672964, 进程: Weixin.exe)
2025-07-30 09:57:05,909 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 09:57:05,909 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3148976)
2025-07-30 09:57:05,910 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148976) - 增强版
2025-07-30 09:57:06,228 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:57:06,229 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:57:06,230 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:57:06,230 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:57:06,230 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 09:57:06,231 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:57:06,434 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 09:57:06,435 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:57:06,636 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:57:06,637 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:57:06,637 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 09:57:06,637 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 09:57:06,638 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 09:57:06,638 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 09:57:06,638 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 09:57:07,639 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 09:57:07,639 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 09:57:07,641 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148976, 进程: Weixin.exe)
2025-07-30 09:57:07,642 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:57:07,643 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 09:57:07,643 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 09:57:07,644 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 09:57:07,645 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3672964, 进程: Weixin.exe)
2025-07-30 09:57:07,647 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 09:57:07,648 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 09:57:07,648 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 09:57:07,648 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 09:57:07,648 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 09:57:07,649 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 09:57:07,965 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:57:07,966 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:57:07,966 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:57:07,967 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:57:07,967 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:57:07,967 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:57:07,967 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:57:07,968 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:57:07,968 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:57:07,968 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:57:08,170 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:57:08,171 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:57:08,172 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 09:57:08,473 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:57:08,474 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 09:57:08,474 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 09:57:09,475 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 09:57:09,475 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 09:57:09,475 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 09:57:09,479 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_095709.log
2025-07-30 09:57:09,479 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:57:09,480 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 09:57:09,480 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 09:57:09,481 - __main__ - INFO - 🔄 传递全局联系人索引: 6
2025-07-30 09:57:09,481 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 09:57:09,481 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 09:57:09,484 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 09:57:09,484 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 09:57:09,484 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 09:57:09,484 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 09:57:09,485 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 09:57:09,485 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 09:57:09,485 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 09:57:09,486 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 09:57:09,486 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:57:09,487 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3672964
2025-07-30 09:57:09,487 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3672964) - 增强版
2025-07-30 09:57:09,795 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:57:09,795 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:57:09,795 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 09:57:09,796 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 09:57:09,796 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 09:57:09,797 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 09:57:10,001 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 09:57:10,001 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 09:57:10,203 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:57:10,203 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:57:10,209 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3672964 (API返回: None)
2025-07-30 09:57:10,510 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:57:10,510 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 09:57:10,511 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 09:57:10,511 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 09:57:10,512 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 09:57:10,512 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 09:57:10,513 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 09:57:10,517 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 09:57:10,517 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 09:57:10,985 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 09:57:10,986 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:57:11,224 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2929 个
2025-07-30 09:57:11,225 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 7 个联系人开始处理
2025-07-30 09:57:11,226 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2923 个
2025-07-30 09:57:11,226 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2923 个 (总计: 3135 个)
2025-07-30 09:57:11,226 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 09:57:11,227 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 09:57:11,227 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:11,228 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 09:57:11,228 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2923
2025-07-30 09:57:11,228 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13552924384 (胡运帅)
2025-07-30 09:57:11,228 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:17,777 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13552924384
2025-07-30 09:57:17,778 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:57:17,778 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13552924384 执行添加朋友操作...
2025-07-30 09:57:17,778 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:57:17,779 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:57:17,779 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:57:17,781 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:57:17,786 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 09:57:17,788 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:57:17,789 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:57:17,789 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:57:17,789 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:57:17,789 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:57:17,790 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:57:17,790 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:57:17,795 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:57:17,797 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:57:17,799 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:57:17,801 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:57:17,804 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 09:57:17,806 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:57:18,308 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:57:18,311 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 09:57:18,379 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差44.78, 边缘比例0.0553
2025-07-30 09:57:18,388 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095718.png
2025-07-30 09:57:18,391 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:57:18,393 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:57:18,395 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:57:18,397 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:57:18,400 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:57:18,406 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095718.png
2025-07-30 09:57:18,409 - WeChatAutoAdd - INFO - 底部区域原始检测到 39 个轮廓
2025-07-30 09:57:18,411 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 09:57:18,413 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 09:57:18,415 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 09:57:18,418 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 09:57:18,420 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 09:57:18,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,279), 尺寸13x2, 长宽比6.50, 面积26
2025-07-30 09:57:18,424 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,275), 尺寸5x6, 长宽比0.83, 面积30
2025-07-30 09:57:18,426 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,271), 尺寸33x13, 长宽比2.54, 面积429
2025-07-30 09:57:18,428 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=271 (距底部154像素区域)
2025-07-30 09:57:18,432 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 09:57:18,435 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(142,271), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,269), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,438 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,266), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,441 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,266), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:18,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,261), 尺寸13x20, 长宽比0.65, 面积260
2025-07-30 09:57:18,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,260), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 09:57:18,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,259), 尺寸3x6, 长宽比0.50, 面积18
2025-07-30 09:57:18,450 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 09:57:18,452 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,258), 尺寸15x19, 长宽比0.79, 面积285
2025-07-30 09:57:18,454 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:18,457 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,460 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 09:57:18,465 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,467 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 09:57:18,471 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 09:57:18,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,254), 尺寸13x6, 长宽比2.17, 面积78
2025-07-30 09:57:18,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,482 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,486 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:18,489 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,492 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,250), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 09:57:18,497 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 09:57:18,500 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=69.1 (阈值:60)
2025-07-30 09:57:18,503 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 09:57:18,506 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=64.1 (阈值:60)
2025-07-30 09:57:18,509 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸34x23, 长宽比1.48, 面积782
2025-07-30 09:57:18,512 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=76.6 (阈值:60)
2025-07-30 09:57:18,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 09:57:18,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 09:57:18,521 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 09:57:18,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,241), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,531 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,237), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 09:57:18,533 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,237), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:18,535 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,235), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:18,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,230), 尺寸31x14, 长宽比2.21, 面积434
2025-07-30 09:57:18,542 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.1 (阈值:60)
2025-07-30 09:57:18,545 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸28x14, 长宽比2.00, 面积392
2025-07-30 09:57:18,548 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=95.1 (阈值:60)
2025-07-30 09:57:18,551 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 09:57:18,556 - WeChatAutoAdd - INFO - 底部区域找到 5 个按钮候选
2025-07-30 09:57:18,559 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 09:57:18,562 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 09:57:18,568 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 09:57:18,579 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095718.png
2025-07-30 09:57:18,582 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 09:57:18,584 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:57:18,888 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 09:57:19,667 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:57:19,669 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:57:19,673 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:19,674 - modules.wechat_auto_add_simple - INFO - ✅ 13552924384 添加朋友操作执行成功
2025-07-30 09:57:19,674 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:19,675 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:57:21,677 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:57:21,677 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:57:21,677 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:57:21,678 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:57:21,678 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:57:21,678 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:57:21,679 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:57:21,679 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:57:21,679 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:57:21,680 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13552924384
2025-07-30 09:57:21,681 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:57:21,681 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:57:21,681 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:57:21,682 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:57:21,682 - modules.friend_request_window - INFO -    📱 phone: '13552924384'
2025-07-30 09:57:21,682 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:57:21,682 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:57:22,160 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:57:22,160 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:57:22,161 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:57:22,161 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:57:22,162 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13552924384
2025-07-30 09:57:22,162 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:57:22,163 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:57:22,164 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:57:22,164 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:57:22,164 - modules.friend_request_window - INFO -    📱 手机号码: 13552924384
2025-07-30 09:57:22,164 - modules.friend_request_window - INFO -    🆔 准考证: 014325110133
2025-07-30 09:57:22,165 - modules.friend_request_window - INFO -    👤 姓名: 胡运帅
2025-07-30 09:57:22,165 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:57:22,165 - modules.friend_request_window - INFO -    📝 备注格式: '014325110133-胡运帅-2025-07-30 17:57:22'
2025-07-30 09:57:22,166 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:57:22,166 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110133-胡运帅-2025-07-30 17:57:22'
2025-07-30 09:57:22,166 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:57:22,168 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 09:57:22,168 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 09:57:22,169 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:22,169 - modules.wechat_auto_add_simple - INFO - ✅ 13552924384 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 09:57:22,170 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13552924384
2025-07-30 09:57:22,171 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:25,778 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2923
2025-07-30 09:57:25,778 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15520854174 (李硕)
2025-07-30 09:57:25,779 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:32,339 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15520854174
2025-07-30 09:57:32,340 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 09:57:32,340 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15520854174 执行添加朋友操作...
2025-07-30 09:57:32,341 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 09:57:32,341 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 09:57:32,342 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 09:57:32,344 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 09:57:32,348 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 09:57:32,350 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 09:57:32,351 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 09:57:32,351 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 09:57:32,351 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 09:57:32,352 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 09:57:32,352 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 09:57:32,352 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 09:57:32,359 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:57:32,362 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:57:32,365 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 09:57:32,369 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 09:57:32,372 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 09:57:32,375 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 09:57:32,878 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 09:57:32,880 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 09:57:32,945 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差32.41, 边缘比例0.0486
2025-07-30 09:57:32,954 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_095732.png
2025-07-30 09:57:32,957 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 09:57:32,959 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 09:57:32,962 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 09:57:32,964 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 09:57:32,966 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 09:57:32,972 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_095732.png
2025-07-30 09:57:32,974 - WeChatAutoAdd - INFO - 底部区域原始检测到 32 个轮廓
2025-07-30 09:57:32,976 - WeChatAutoAdd - INFO - 重要轮廓: 位置(117,236), 尺寸93x31, 长宽比3.00, 已知特征:False
2025-07-30 09:57:32,979 - WeChatAutoAdd - INFO - 发现目标候选: 位置(117,236), 尺寸93x31
2025-07-30 09:57:32,981 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(306,209), 尺寸1x86, 长宽比0.01, 面积86
2025-07-30 09:57:32,983 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:32,986 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:32,987 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:32,990 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:32,992 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 09:57:32,994 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:32,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:33,000 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:33,002 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,209), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 09:57:33,004 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(190,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:33,007 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:33,009 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 09:57:33,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:33,016 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:33,018 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,209), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 09:57:33,021 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:33,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,209), 尺寸10x1, 长宽比10.00, 面积10
2025-07-30 09:57:33,033 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:33,037 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:33,041 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(118,209), 尺寸5x1, 长宽比5.00, 面积5
2025-07-30 09:57:33,053 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,209), 尺寸11x1, 长宽比11.00, 面积11
2025-07-30 09:57:33,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(100,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:33,063 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(94,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 09:57:33,065 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:33,068 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:33,071 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 09:57:33,074 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,209), 尺寸6x1, 长宽比6.00, 面积6
2025-07-30 09:57:33,077 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(23,209), 尺寸282x168, 长宽比1.68, 面积47376
2025-07-30 09:57:33,080 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,209), 尺寸2x88, 长宽比0.02, 面积176
2025-07-30 09:57:33,083 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 09:57:33,085 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 09:57:33,088 - WeChatAutoAdd - INFO - 选择目标候选按钮: Y=236, 符合'添加到通讯录'特征
2025-07-30 09:57:33,091 - WeChatAutoAdd - INFO - 在底部找到按钮: (163, 251), 尺寸: 93x31, 位置得分: 1.0, 目标候选: True, 绿色按钮: False, 特殊位置: False
2025-07-30 09:57:33,093 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 09:57:33,103 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_095733.png
2025-07-30 09:57:33,106 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 09:57:33,108 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 09:57:33,411 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(163, 251) -> 屏幕坐标(1363, 251)
2025-07-30 09:57:34,182 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 09:57:34,185 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 09:57:34,187 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:34,187 - modules.wechat_auto_add_simple - INFO - ✅ 15520854174 添加朋友操作执行成功
2025-07-30 09:57:34,187 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:34,188 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 09:57:36,190 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 09:57:36,190 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 09:57:36,190 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 09:57:36,191 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 09:57:36,191 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 09:57:36,191 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 09:57:36,192 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 09:57:36,192 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 09:57:36,192 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 09:57:36,192 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15520854174
2025-07-30 09:57:36,193 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 09:57:36,193 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 09:57:36,194 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 09:57:36,194 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 09:57:36,195 - modules.friend_request_window - INFO -    📱 phone: '15520854174'
2025-07-30 09:57:36,195 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 09:57:36,196 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 09:57:36,726 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 09:57:36,726 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 09:57:36,726 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 09:57:36,727 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 09:57:36,728 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15520854174
2025-07-30 09:57:36,728 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 09:57:36,729 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:57:36,731 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 09:57:36,731 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 09:57:36,731 - modules.friend_request_window - INFO -    📱 手机号码: 15520854174
2025-07-30 09:57:36,732 - modules.friend_request_window - INFO -    🆔 准考证: 014325110134
2025-07-30 09:57:36,732 - modules.friend_request_window - INFO -    👤 姓名: 李硕
2025-07-30 09:57:36,732 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 09:57:36,733 - modules.friend_request_window - INFO -    📝 备注格式: '014325110134-李硕-2025-07-30 17:57:36'
2025-07-30 09:57:36,733 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 09:57:36,733 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110134-李硕-2025-07-30 17:57:36'
2025-07-30 09:57:36,734 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 09:57:36,736 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 09:57:36,736 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 09:57:36,737 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:36,737 - modules.wechat_auto_add_simple - INFO - ✅ 15520854174 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 09:57:36,738 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15520854174
2025-07-30 09:57:36,739 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 09:57:38,090 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 09:57:38,091 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 09:57:38,091 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 09:57:38,092 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 09:57:38,093 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 09:57:38,093 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 09:57:38,093 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 09:57:38,094 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 09:57:38,094 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 09:57:38,094 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-30 09:57:38,094 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 09:57:38,095 - __main__ - INFO - � 更新全局进度：已处理 8/2935 个联系人
2025-07-30 09:57:38,096 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 09:57:41,097 - __main__ - INFO - 🔄 完成第 2 轮窗口循环，重新开始下一轮
2025-07-30 09:57:41,098 - __main__ - INFO - 📊 当前进度：已处理 8/2935 个联系人
2025-07-30 09:57:41,098 - __main__ - INFO - 
============================================================
2025-07-30 09:57:41,099 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 3 轮)
2025-07-30 09:57:41,099 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 09:57:41,099 - __main__ - INFO - 📊 全局进度：已处理 8/2935 个联系人
2025-07-30 09:57:41,099 - __main__ - INFO - ============================================================
2025-07-30 09:57:41,100 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 09:57:41,100 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 09:57:41,100 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 09:57:41,101 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 09:57:41,101 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:57:41,419 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:57:41,420 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:57:41,420 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:57:41,420 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:57:41,421 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:57:41,421 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:57:41,421 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:57:41,422 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:57:41,422 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:57:41,422 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:57:41,625 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:57:41,625 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:57:41,626 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 09:57:41,626 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 09:57:41,930 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 09:57:41,931 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 09:57:41,931 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:57:41,932 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 09:57:41,932 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 09:57:41,932 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:57:41,933 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:57:41,933 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:57:41,934 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:57:41,934 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 09:57:42,135 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 09:57:42,136 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 09:57:42,138 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 09:57:42,438 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 09:57:42,439 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 09:57:42,439 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 09:57:42,439 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 09:57:42,440 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 09:57:42,440 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 09:57:42,440 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 09:57:42,440 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 09:57:43,441 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 09:57:43,441 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 09:57:43,442 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 09:57:43,442 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 09:57:43,442 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 09:57:43,443 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 09:57:43,443 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 09:57:43,443 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 09:57:43,644 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 09:57:43,644 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 09:57:46,035 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 09:57:46,038 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 09:57:46,039 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
