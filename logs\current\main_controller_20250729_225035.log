2025-07-29 22:50:35,377 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:50:35,378 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:50:35,379 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 22:50:35,380 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 22:50:35,381 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 22:50:35,382 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 22:50:35,382 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 22:50:35,384 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 22:50:35,385 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 22:50:35,385 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 22:50:35,386 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:50:35,391 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:50:35,398 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_225035.log
2025-07-29 22:50:35,405 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:50:35,406 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 22:50:35,406 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 22:50:35,407 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 22:50:35,407 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 22:50:35,408 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 06:50:35
2025-07-29 22:50:35,408 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 22:50:35,408 - __main__ - INFO - 📅 启动时间: 2025-07-30 06:50:35
2025-07-29 22:50:35,409 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 22:50:35,409 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 22:50:35,945 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:50:35,946 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 22:50:36,495 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:50:36,495 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 22:50:36,499 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 22:50:36,499 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 22:50:36,499 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 22:50:36,500 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 22:50:36,501 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 22:50:37,488 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 22:50:37,488 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 22:50:37,490 - __main__ - INFO - 📋 待处理联系人数: 2938
2025-07-29 22:50:37,490 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 22:50:37,491 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2938
2025-07-29 22:50:37,491 - __main__ - INFO - 
============================================================
2025-07-29 22:50:37,491 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-29 22:50:37,492 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 22:50:37,492 - __main__ - INFO - 📊 全局进度：已处理 0/2938 个联系人
2025-07-29 22:50:37,492 - __main__ - INFO - ============================================================
2025-07-29 22:50:37,493 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 22:50:37,494 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 22:50:37,495 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 22:50:37,496 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 22:50:37,497 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 22:50:37,820 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:50:37,820 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:50:37,820 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:50:37,821 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 22:50:37,821 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 22:50:37,821 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:50:37,822 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:50:37,822 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:50:37,822 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:50:37,823 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 22:50:38,024 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:50:38,025 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:50:38,026 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 22:50:38,026 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 22:50:38,330 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:50:38,331 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:50:38,331 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:50:38,331 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 22:50:38,332 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 22:50:38,332 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:50:38,332 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:50:38,333 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:50:38,333 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:50:38,333 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 22:50:38,535 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:50:38,536 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:50:38,537 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 22:50:38,838 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 22:50:38,839 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:50:38,839 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:50:38,839 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:50:38,840 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:50:38,840 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:50:38,840 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 22:50:38,841 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 22:50:39,841 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 22:50:39,842 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 22:50:39,842 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 22:50:39,842 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 22:50:39,843 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 22:50:39,843 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 22:50:39,844 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 22:50:39,844 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 22:50:40,045 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 22:50:40,046 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 22:50:42,416 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 22:50:42,417 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 22:50:42,417 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 22:50:44,712 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 22:50:44,913 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 22:50:44,914 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 22:50:47,359 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 22:50:47,360 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 22:50:47,360 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-29 22:50:49,442 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 22:50:49,643 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 22:50:49,644 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 22:50:52,016 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 22:50:52,017 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 22:50:52,017 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 22:50:53,610 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 22:50:53,811 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 22:50:53,812 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 22:50:56,183 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 22:50:56,183 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 22:50:56,183 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 22:50:58,505 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 22:50:58,706 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 22:50:58,707 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 22:51:01,083 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 22:51:01,083 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 22:51:01,084 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 22:51:01,084 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 22:51:01,085 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 22:51:01,086 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:51:01,086 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 22:51:01,087 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:51:01,088 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 22:51:01,089 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4655720, 进程: Weixin.exe)
2025-07-29 22:51:01,091 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 22:51:01,092 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4655720)
2025-07-29 22:51:01,092 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4655720) - 增强版
2025-07-29 22:51:01,397 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:51:01,397 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:51:01,397 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 22:51:01,398 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 22:51:01,399 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 22:51:01,399 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 22:51:01,603 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 22:51:01,604 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 22:51:01,805 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:51:01,806 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:51:01,806 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 22:51:01,807 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 22:51:01,807 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 22:51:01,807 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 22:51:01,808 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 22:51:02,808 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 22:51:02,809 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 22:51:02,810 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:51:02,811 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 22:51:02,813 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:51:02,813 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 22:51:02,815 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4655720, 进程: Weixin.exe)
2025-07-29 22:51:02,817 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 22:51:02,818 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 22:51:02,818 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 22:51:02,819 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 22:51:02,820 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 22:51:02,821 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 22:51:03,130 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:51:03,131 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:51:03,131 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:51:03,131 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 22:51:03,132 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 22:51:03,132 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:51:03,132 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:51:03,133 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:51:03,133 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:51:03,133 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 22:51:03,335 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:51:03,335 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:51:03,337 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 22:51:03,638 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 22:51:03,638 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 22:51:03,638 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 22:51:04,639 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 22:51:04,639 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 22:51:04,640 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 22:51:04,643 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_225104.log
2025-07-29 22:51:04,644 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:51:04,644 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 22:51:04,647 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 22:51:04,647 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-29 22:51:04,647 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 22:51:04,648 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 22:51:04,649 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 22:51:04,650 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 22:51:04,650 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 22:51:04,650 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 22:51:04,651 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 22:51:04,651 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 22:51:04,652 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 22:51:04,654 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:51:04,654 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4655720
2025-07-29 22:51:04,654 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4655720) - 增强版
2025-07-29 22:51:04,961 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:51:04,962 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:51:04,962 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 22:51:04,963 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 22:51:04,963 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 22:51:04,963 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 22:51:04,964 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 22:51:04,964 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 22:51:05,166 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:51:05,166 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:51:05,169 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4655720 (API返回: None)
2025-07-29 22:51:05,470 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 22:51:05,471 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 22:51:05,471 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 22:51:05,472 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 22:51:05,473 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:51:05,473 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 22:51:05,473 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 22:51:05,477 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 22:51:05,477 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 22:51:06,064 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 22:51:06,064 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 22:51:06,322 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2938 个
2025-07-29 22:51:06,323 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2938 个 (总计: 3135 个)
2025-07-29 22:51:06,324 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 22:51:06,324 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 22:51:06,324 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:06,325 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 22:51:06,325 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2938
2025-07-29 22:51:06,325 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15019004476 (王硕)
2025-07-29 22:51:06,326 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:12,900 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15019004476
2025-07-29 22:51:12,900 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 22:51:12,900 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15019004476 执行添加朋友操作...
2025-07-29 22:51:12,901 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 22:51:12,901 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 22:51:12,902 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 22:51:12,903 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 22:51:12,907 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 22:51:12,910 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 22:51:12,911 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 22:51:12,912 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 22:51:12,912 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 22:51:12,913 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 22:51:12,914 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 22:51:12,914 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 22:51:12,918 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 22:51:12,924 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:51:12,927 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:51:12,933 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 22:51:12,935 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 22:51:12,938 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 22:51:13,441 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 22:51:13,442 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 22:51:13,508 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.05, 边缘比例0.0379
2025-07-29 22:51:13,516 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_225113.png
2025-07-29 22:51:13,518 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 22:51:13,520 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 22:51:13,521 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 22:51:13,522 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 22:51:13,523 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 22:51:13,529 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_225113.png
2025-07-29 22:51:13,530 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 22:51:13,532 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 22:51:13,534 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 22:51:13,538 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 22:51:13,544 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 22:51:13,547 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 22:51:13,551 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 22:51:13,552 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 22:51:13,562 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_225113.png
2025-07-29 22:51:13,566 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 22:51:13,569 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 22:51:13,574 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_225113.png
2025-07-29 22:51:13,681 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 22:51:13,682 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 22:51:13,683 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 22:51:13,683 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 22:51:13,985 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 22:51:14,753 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 22:51:14,754 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 22:51:14,755 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:14,755 - modules.wechat_auto_add_simple - INFO - ✅ 15019004476 添加朋友操作执行成功
2025-07-29 22:51:14,756 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:14,756 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 22:51:16,757 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 22:51:16,758 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 22:51:16,758 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 22:51:16,759 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 22:51:16,759 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 22:51:16,759 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 22:51:16,760 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 22:51:16,760 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 22:51:16,761 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 22:51:16,761 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15019004476
2025-07-29 22:51:16,765 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 22:51:16,765 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:51:16,766 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:51:16,767 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 22:51:16,767 - modules.friend_request_window - INFO -    📱 phone: '15019004476'
2025-07-29 22:51:16,768 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 22:51:16,768 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 22:51:17,271 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 22:51:17,272 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 22:51:17,272 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 22:51:17,273 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 22:51:17,274 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15019004476
2025-07-29 22:51:17,275 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 22:51:17,276 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:51:17,277 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 22:51:17,277 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 22:51:17,278 - modules.friend_request_window - INFO -    📱 手机号码: 15019004476
2025-07-29 22:51:17,279 - modules.friend_request_window - INFO -    🆔 准考证: 014325110113
2025-07-29 22:51:17,280 - modules.friend_request_window - INFO -    👤 姓名: 王硕
2025-07-29 22:51:17,280 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 22:51:17,280 - modules.friend_request_window - INFO -    📝 备注格式: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:17,281 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:51:17,282 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:17,282 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 22:51:17,285 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3343836, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 22:51:17,286 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3343836)
2025-07-29 22:51:17,286 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 22:51:17,288 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 22:51:17,289 - modules.friend_request_window - INFO - 🔄 激活窗口: 3343836
2025-07-29 22:51:17,992 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 22:51:17,993 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 22:51:17,993 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 22:51:17,995 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 22:51:17,996 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 22:51:17,996 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:51:17,996 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 22:51:17,996 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:51:17,997 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 22:51:17,997 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 22:51:17,997 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 22:51:17,997 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 22:51:17,998 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 22:51:17,998 - modules.friend_request_window - INFO -    📝 remark参数: '014325110113-王硕-2025-07-30 06:51:17' (类型: <class 'str'>, 长度: 35)
2025-07-29 22:51:17,999 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 22:51:18,000 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:18,001 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 22:51:18,001 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 22:51:18,001 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 22:51:18,002 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 22:51:18,002 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 22:51:18,002 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 22:51:18,002 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 22:51:18,914 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 22:51:24,155 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 22:51:24,155 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 22:51:24,156 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 22:51:24,156 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 22:51:24,158 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:51:24,472 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:51:24,472 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:51:25,375 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:51:25,386 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:51:25,386 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 22:51:25,387 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 22:51:25,388 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 22:51:25,389 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 22:51:25,890 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 22:51:25,890 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 22:51:25,890 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 22:51:25,891 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 22:51:25,891 - modules.friend_request_window - INFO -    📝 内容: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:25,891 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 22:51:25,892 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110113-\xe7\x8e\x8b\xe7\xa1\x95-2025-07-30 06:51:17'
2025-07-29 22:51:25,892 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 22:51:26,797 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 22:51:32,038 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 22:51:32,038 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 22:51:32,039 - modules.friend_request_window - INFO -    📝 原始文本: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:32,039 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 22:51:32,040 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:51:32,351 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:51:32,351 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:51:33,253 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:51:33,264 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:51:33,264 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:33,265 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 22:51:33,266 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:33,266 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 22:51:33,767 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:33,767 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 22:51:33,767 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 22:51:33,768 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 22:51:33,768 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 22:51:33,768 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 22:51:33,769 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 22:51:34,570 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 22:51:34,570 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 22:51:34,571 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 22:51:35,181 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:35,182 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 22:51:35,182 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 22:51:35,183 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 22:51:35,701 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:35,938 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:36,169 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:36,403 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:36,637 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:36,869 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:37,106 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:37,343 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:37,575 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:37,808 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:38,041 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:38,273 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:38,506 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:38,736 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:38,972 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:39,206 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:39,440 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:39,673 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:39,904 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:40,135 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:51:40,349 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 22:51:40,349 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 22:51:41,350 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 22:51:41,353 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 22:51:41,353 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 22:51:41,353 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 22:51:41,354 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 22:51:41,354 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 22:51:41,354 - modules.friend_request_window - INFO -    📝 备注信息: '014325110113-王硕-2025-07-30 06:51:17'
2025-07-29 22:51:41,855 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 22:51:41,856 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:41,856 - modules.wechat_auto_add_simple - INFO - ✅ 15019004476 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 22:51:41,856 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15019004476
2025-07-29 22:51:41,857 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:45,527 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2938
2025-07-29 22:51:45,528 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13255525865 (于子丞)
2025-07-29 22:51:45,528 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:52,107 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13255525865
2025-07-29 22:51:52,107 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 22:51:52,107 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13255525865 执行添加朋友操作...
2025-07-29 22:51:52,107 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 22:51:52,108 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 22:51:52,109 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 22:51:52,110 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 22:51:52,115 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 22:51:52,117 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 22:51:52,118 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 22:51:52,118 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 22:51:52,118 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 22:51:52,119 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 22:51:52,119 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 22:51:52,119 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 22:51:52,127 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 22:51:52,131 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:51:52,133 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:51:52,136 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 22:51:52,140 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 22:51:52,143 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 22:51:52,649 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 22:51:52,650 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 22:51:52,709 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.27, 边缘比例0.0385
2025-07-29 22:51:52,719 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_225152.png
2025-07-29 22:51:52,721 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 22:51:52,722 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 22:51:52,723 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 22:51:52,724 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 22:51:52,727 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 22:51:52,734 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_225152.png
2025-07-29 22:51:52,736 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 22:51:52,737 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 22:51:52,738 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 22:51:52,739 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 22:51:52,740 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 22:51:52,742 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 22:51:52,745 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 22:51:52,751 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 22:51:52,762 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_225152.png
2025-07-29 22:51:52,765 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 22:51:52,767 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 22:51:52,771 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_225152.png
2025-07-29 22:51:52,801 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 22:51:52,805 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 22:51:52,818 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 22:51:52,819 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 22:51:53,121 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 22:51:53,890 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 22:51:53,891 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 22:51:53,895 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:53,895 - modules.wechat_auto_add_simple - INFO - ✅ 13255525865 添加朋友操作执行成功
2025-07-29 22:51:53,896 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:51:53,897 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 22:51:55,899 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 22:51:55,899 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 22:51:55,900 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 22:51:55,900 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 22:51:55,900 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 22:51:55,901 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 22:51:55,901 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 22:51:55,901 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 22:51:55,901 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 22:51:55,902 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13255525865
2025-07-29 22:51:55,902 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 22:51:55,903 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:51:55,903 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:51:55,903 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 22:51:55,903 - modules.friend_request_window - INFO -    📱 phone: '13255525865'
2025-07-29 22:51:55,904 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 22:51:55,904 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 22:51:56,410 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 22:51:56,410 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 22:51:56,411 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 22:51:56,411 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 22:51:56,412 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13255525865
2025-07-29 22:51:56,412 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 22:51:56,413 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:51:56,414 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 22:51:56,415 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 22:51:56,415 - modules.friend_request_window - INFO -    📱 手机号码: 13255525865
2025-07-29 22:51:56,416 - modules.friend_request_window - INFO -    🆔 准考证: 014325110114
2025-07-29 22:51:56,417 - modules.friend_request_window - INFO -    👤 姓名: 于子丞
2025-07-29 22:51:56,418 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 22:51:56,418 - modules.friend_request_window - INFO -    📝 备注格式: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:51:56,419 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:51:56,419 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:51:56,420 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 22:51:56,421 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3933868, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 22:51:56,426 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3933868)
2025-07-29 22:51:56,426 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 22:51:56,427 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 22:51:56,428 - modules.friend_request_window - INFO - 🔄 激活窗口: 3933868
2025-07-29 22:51:57,131 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 22:51:57,132 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 22:51:57,132 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 22:51:57,132 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 22:51:57,133 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 22:51:57,133 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:51:57,133 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 22:51:57,134 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:51:57,134 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 22:51:57,134 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 22:51:57,135 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 22:51:57,135 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 22:51:57,135 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 22:51:57,136 - modules.friend_request_window - INFO -    📝 remark参数: '014325110114-于子丞-2025-07-30 06:51:56' (类型: <class 'str'>, 长度: 36)
2025-07-29 22:51:57,136 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 22:51:57,136 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:51:57,137 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 22:51:57,137 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 22:51:57,138 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 22:51:57,139 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 22:51:57,139 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 22:51:57,139 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 22:51:57,140 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 22:51:58,045 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 22:52:03,288 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 22:52:03,288 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 22:52:03,288 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 22:52:03,289 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 22:52:03,290 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:52:03,599 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:52:03,599 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:52:04,502 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:52:04,512 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:52:04,513 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 22:52:04,513 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 22:52:04,513 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 22:52:04,514 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 22:52:05,014 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 22:52:05,015 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 22:52:05,015 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 22:52:05,015 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 22:52:05,016 - modules.friend_request_window - INFO -    📝 内容: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:52:05,016 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 22:52:05,016 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110114-\xe4\xba\x8e\xe5\xad\x90\xe4\xb8\x9e-2025-07-30 06:51:56'
2025-07-29 22:52:05,016 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 22:52:05,928 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 22:52:11,172 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 22:52:11,173 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 22:52:11,173 - modules.friend_request_window - INFO -    📝 原始文本: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:52:11,173 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 22:52:11,174 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:52:11,482 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:52:11,483 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:52:12,385 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:52:12,395 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:52:12,396 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:52:12,396 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 22:52:12,397 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:52:12,398 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 22:52:12,898 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:52:12,899 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 22:52:12,899 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 22:52:12,899 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 22:52:12,899 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 22:52:12,900 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 22:52:12,900 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 22:52:13,700 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 22:52:13,701 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 22:52:13,701 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 22:52:14,311 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:52:14,312 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 22:52:14,312 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 22:52:14,312 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 22:52:14,828 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:15,065 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:15,297 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:15,532 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:15,766 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:15,998 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:16,247 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:16,482 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:16,711 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:16,944 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:17,176 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:17,410 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:17,645 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:17,881 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:18,111 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:18,346 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:18,582 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:18,815 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:19,053 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:19,283 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:52:19,500 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 22:52:19,500 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 22:52:20,501 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 22:52:20,504 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 22:52:20,504 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 22:52:20,504 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 22:52:20,505 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 22:52:20,505 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 22:52:20,505 - modules.friend_request_window - INFO -    📝 备注信息: '014325110114-于子丞-2025-07-30 06:51:56'
2025-07-29 22:52:21,006 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 22:52:21,007 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:52:21,007 - modules.wechat_auto_add_simple - INFO - ✅ 13255525865 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 22:52:21,007 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13255525865
2025-07-29 22:52:21,008 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:52:22,377 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 22:52:22,378 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 22:52:22,378 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 22:52:22,379 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 22:52:22,380 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 22:52:22,380 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 22:52:22,380 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 22:52:22,381 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 22:52:22,381 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 22:52:22,381 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 22:52:22,381 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 22:52:22,382 - __main__ - INFO - � 更新全局进度：已处理 2/2938 个联系人
2025-07-29 22:52:22,382 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 22:52:25,383 - __main__ - INFO - 
============================================================
2025-07-29 22:52:25,383 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-29 22:52:25,384 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 22:52:25,384 - __main__ - INFO - 📊 全局进度：已处理 2/2938 个联系人
2025-07-29 22:52:25,384 - __main__ - INFO - ============================================================
2025-07-29 22:52:25,384 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 22:52:25,385 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 22:52:25,385 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 22:52:25,385 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 22:52:25,386 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 22:52:25,709 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:52:25,709 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:52:25,709 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:52:25,710 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 22:52:25,710 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 22:52:25,710 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:52:25,711 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:52:25,711 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:52:25,711 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:52:25,712 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 22:52:25,913 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:52:25,913 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:52:25,914 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 22:52:25,914 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 22:52:26,218 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:52:26,218 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:52:26,219 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:52:26,219 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 22:52:26,219 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 22:52:26,220 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:52:26,220 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:52:26,220 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:52:26,221 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:52:26,221 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 22:52:26,423 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:52:26,423 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:52:26,425 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 22:52:26,726 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 22:52:26,726 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:52:26,726 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:52:26,727 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:52:26,727 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:52:26,727 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:52:26,728 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 22:52:26,728 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 22:52:27,728 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 22:52:27,729 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 22:52:27,729 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 22:52:27,729 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 22:52:27,730 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 22:52:27,730 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 22:52:27,730 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 22:52:27,731 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 22:52:27,931 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 22:52:27,932 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 22:52:30,310 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 22:52:30,310 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 22:52:30,310 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-29 22:52:32,680 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 22:52:32,881 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 22:52:32,881 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 22:52:35,249 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 22:52:35,249 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 22:52:35,249 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-29 22:52:36,759 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 22:52:36,960 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 22:52:36,961 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 22:52:39,343 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 22:52:39,343 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 22:52:39,343 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-29 22:52:41,048 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 22:52:41,249 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 22:52:41,250 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 22:52:43,626 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 22:52:43,626 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 22:52:43,626 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 22:52:45,205 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 22:52:45,406 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 22:52:45,407 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 22:52:47,775 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 22:52:47,776 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 22:52:47,776 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 22:52:47,776 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 22:52:47,777 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 22:52:47,778 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:52:47,778 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 22:52:47,779 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4655720, 进程: Weixin.exe)
2025-07-29 22:52:47,780 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:52:47,780 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 22:52:47,782 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6621258, 进程: Weixin.exe)
2025-07-29 22:52:47,785 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 22:52:47,786 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4655720)
2025-07-29 22:52:47,787 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4655720) - 增强版
2025-07-29 22:52:48,111 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:52:48,112 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:52:48,112 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 22:52:48,113 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 22:52:48,113 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-29 22:52:48,113 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 22:52:48,316 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 22:52:48,316 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 22:52:48,518 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:52:48,518 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:52:48,518 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 22:52:48,519 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 22:52:48,519 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 22:52:48,519 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 22:52:48,519 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 22:52:49,520 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 22:52:49,521 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 22:52:49,522 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4655720, 进程: Weixin.exe)
2025-07-29 22:52:49,523 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:52:49,523 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 22:52:49,524 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 22:52:49,524 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 22:52:49,525 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6621258, 进程: Weixin.exe)
2025-07-29 22:52:49,527 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 22:52:49,528 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 22:52:49,529 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 22:52:49,530 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 22:52:49,531 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 22:52:49,531 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 22:52:49,859 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:52:49,859 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:52:49,860 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 22:52:49,860 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 22:52:49,860 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 22:52:49,861 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 22:52:49,861 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 22:52:49,861 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 22:52:49,862 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 22:52:49,862 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 22:52:50,064 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:52:50,065 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:52:50,066 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 22:52:50,367 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 22:52:50,368 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 22:52:50,368 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 22:52:51,369 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 22:52:51,369 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-29 22:52:51,370 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 22:52:51,372 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_225251.log
2025-07-29 22:52:51,373 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:52:51,373 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 22:52:51,374 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 22:52:51,375 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-29 22:52:51,376 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 22:52:51,377 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 22:52:51,382 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-29 22:52:51,382 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-29 22:52:51,383 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 22:52:51,383 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 22:52:51,384 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 22:52:51,384 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-29 22:52:51,384 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-29 22:52:51,385 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 22:52:51,386 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:52:51,388 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6621258
2025-07-29 22:52:51,389 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6621258) - 增强版
2025-07-29 22:52:51,699 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 22:52:51,700 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 22:52:51,700 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 22:52:51,701 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 22:52:51,701 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 22:52:51,701 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 22:52:51,905 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 22:52:51,906 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 22:52:52,107 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 22:52:52,108 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 22:52:52,110 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6621258 (API返回: None)
2025-07-29 22:52:52,411 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 22:52:52,411 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 22:52:52,412 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 22:52:52,412 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 22:52:52,413 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 22:52:52,414 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 22:52:52,414 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 22:52:52,417 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 22:52:52,417 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 22:52:52,905 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 22:52:52,906 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 22:52:53,160 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2936 个
2025-07-29 22:52:53,161 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-29 22:52:53,162 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2934 个
2025-07-29 22:52:53,162 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2934 个 (总计: 3135 个)
2025-07-29 22:52:53,162 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 22:52:53,162 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 22:52:53,163 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:52:53,163 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 22:52:53,163 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2934
2025-07-29 22:52:53,164 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15546662126 (丁政予)
2025-07-29 22:52:53,164 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:52:59,736 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15546662126
2025-07-29 22:52:59,736 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 22:52:59,736 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15546662126 执行添加朋友操作...
2025-07-29 22:52:59,737 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 22:52:59,737 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 22:52:59,738 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 22:52:59,739 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 22:52:59,744 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 22:52:59,746 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 22:52:59,746 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 22:52:59,748 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 22:52:59,748 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 22:52:59,749 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 22:52:59,749 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 22:52:59,751 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 22:52:59,759 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 22:52:59,761 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:52:59,763 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 22:52:59,765 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:52:59,767 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 22:52:59,768 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 22:52:59,769 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 22:53:00,275 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 22:53:00,277 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 22:53:00,342 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差47.18, 边缘比例0.0532
2025-07-29 22:53:00,350 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_225300.png
2025-07-29 22:53:00,352 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 22:53:00,353 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 22:53:00,355 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 22:53:00,359 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 22:53:00,360 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 22:53:00,365 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_225300.png
2025-07-29 22:53:00,367 - WeChatAutoAdd - INFO - 底部区域原始检测到 46 个轮廓
2025-07-29 22:53:00,368 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-29 22:53:00,369 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-29 22:53:00,372 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 22:53:00,377 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-29 22:53:00,379 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-29 22:53:00,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,277), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,382 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,276), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,384 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,276), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 22:53:00,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,275), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 22:53:00,386 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(260,274), 尺寸3x6, 长宽比0.50, 面积18
2025-07-29 22:53:00,389 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(256,273), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,393 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(260,271), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(257,269), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 22:53:00,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,266), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 22:53:00,397 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(257,265), 尺寸4x3, 长宽比1.33, 面积12
2025-07-29 22:53:00,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,264), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 22:53:00,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,262), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,401 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,262), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,404 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(263,260), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-29 22:53:00,410 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 22:53:00,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,412 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,257), 尺寸13x13, 长宽比1.00, 面积169
2025-07-29 22:53:00,414 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-29 22:53:00,416 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,256), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 22:53:00,419 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,256), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 22:53:00,420 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 22:53:00,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 22:53:00,427 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,428 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,253), 尺寸7x6, 长宽比1.17, 面积42
2025-07-29 22:53:00,429 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,431 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 22:53:00,452 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,464 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 22:53:00,469 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=60.5 (阈值:60)
2025-07-29 22:53:00,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,477 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 22:53:00,479 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=65.6 (阈值:60)
2025-07-29 22:53:00,483 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-29 22:53:00,484 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=72.1 (阈值:60)
2025-07-29 22:53:00,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 22:53:00,489 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=79.4 (阈值:60)
2025-07-29 22:53:00,494 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-29 22:53:00,500 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-29 22:53:00,501 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-29 22:53:00,502 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,240), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 22:53:00,503 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:00,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,235), 尺寸14x9, 长宽比1.56, 面积126
2025-07-29 22:53:00,510 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=87.5 (阈值:60)
2025-07-29 22:53:00,512 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,232), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 22:53:00,514 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,231), 尺寸24x13, 长宽比1.85, 面积312
2025-07-29 22:53:00,516 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=93.7 (阈值:60)
2025-07-29 22:53:00,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,230), 尺寸12x9, 长宽比1.33, 面积108
2025-07-29 22:53:00,518 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=101.7 (阈值:60)
2025-07-29 22:53:00,520 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸22x14, 长宽比1.57, 面积308
2025-07-29 22:53:00,524 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=84.1 (阈值:60)
2025-07-29 22:53:00,526 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 22:53:00,527 - WeChatAutoAdd - INFO - 底部区域找到 4 个按钮候选
2025-07-29 22:53:00,529 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-29 22:53:00,531 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-29 22:53:00,533 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-29 22:53:00,542 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_225300.png
2025-07-29 22:53:00,545 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-29 22:53:00,546 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 22:53:00,848 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-29 22:53:01,624 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 22:53:01,625 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 22:53:01,626 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:01,627 - modules.wechat_auto_add_simple - INFO - ✅ 15546662126 添加朋友操作执行成功
2025-07-29 22:53:01,627 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:01,627 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 22:53:03,630 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 22:53:03,630 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 22:53:03,631 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 22:53:03,631 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 22:53:03,631 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 22:53:03,632 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 22:53:03,632 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 22:53:03,633 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 22:53:03,633 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 22:53:03,634 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15546662126
2025-07-29 22:53:03,634 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 22:53:03,635 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:53:03,635 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:53:03,637 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 22:53:03,640 - modules.friend_request_window - INFO -    📱 phone: '15546662126'
2025-07-29 22:53:03,641 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 22:53:03,644 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 22:53:04,149 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 22:53:04,150 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 22:53:04,150 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 22:53:04,151 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 22:53:04,152 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15546662126
2025-07-29 22:53:04,152 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 22:53:04,153 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:53:04,153 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 22:53:04,154 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 22:53:04,154 - modules.friend_request_window - INFO -    📱 手机号码: 15546662126
2025-07-29 22:53:04,155 - modules.friend_request_window - INFO -    🆔 准考证: 014325110117
2025-07-29 22:53:04,156 - modules.friend_request_window - INFO -    👤 姓名: 丁政予
2025-07-29 22:53:04,157 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 22:53:04,157 - modules.friend_request_window - INFO -    📝 备注格式: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:04,158 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:53:04,159 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:04,159 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 22:53:04,163 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2493358, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 22:53:04,165 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2493358)
2025-07-29 22:53:04,165 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 22:53:04,165 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 22:53:04,166 - modules.friend_request_window - INFO - 🔄 激活窗口: 2493358
2025-07-29 22:53:04,868 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 22:53:04,868 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 22:53:04,869 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 22:53:04,869 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 22:53:04,869 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 22:53:04,870 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:53:04,870 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 22:53:04,870 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:53:04,871 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 22:53:04,871 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 22:53:04,872 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 22:53:04,872 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 22:53:04,872 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 22:53:04,872 - modules.friend_request_window - INFO -    📝 remark参数: '014325110117-丁政予-2025-07-30 06:53:04' (类型: <class 'str'>, 长度: 36)
2025-07-29 22:53:04,873 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 22:53:04,873 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:04,873 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 22:53:04,873 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 22:53:04,874 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 22:53:04,874 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 22:53:04,875 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 22:53:04,875 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 22:53:04,876 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 22:53:05,792 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 22:53:11,033 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 22:53:11,034 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 22:53:11,034 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 22:53:11,034 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 22:53:11,035 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:53:11,344 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:53:11,344 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:53:12,247 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:53:12,256 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:53:12,256 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 22:53:12,257 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 22:53:12,258 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 22:53:12,258 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 22:53:12,759 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 22:53:12,759 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 22:53:12,760 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 22:53:12,760 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 22:53:12,760 - modules.friend_request_window - INFO -    📝 内容: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:12,760 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 22:53:12,761 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110117-\xe4\xb8\x81\xe6\x94\xbf\xe4\xba\x88-2025-07-30 06:53:04'
2025-07-29 22:53:12,761 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 22:53:13,674 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 22:53:18,920 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 22:53:18,921 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 22:53:18,921 - modules.friend_request_window - INFO -    📝 原始文本: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:18,921 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 22:53:18,922 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:53:19,233 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:53:19,234 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:53:20,137 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:53:20,145 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:53:20,146 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:20,146 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 22:53:20,147 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:20,148 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 22:53:20,649 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:20,649 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 22:53:20,649 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 22:53:20,650 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 22:53:20,650 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 22:53:20,650 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 22:53:20,651 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 22:53:21,451 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 22:53:21,452 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 22:53:21,452 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 22:53:22,075 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:22,075 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 22:53:22,076 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 22:53:22,076 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 22:53:22,593 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:22,594 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:22,824 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:22,825 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,056 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,057 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,288 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,289 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,522 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,523 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,757 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,758 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,992 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:23,993 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,229 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,230 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,461 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,462 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,698 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,698 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,929 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:24,930 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,163 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,163 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,399 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,400 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,633 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,634 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,868 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:25,868 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,101 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,102 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,334 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,334 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,566 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,567 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,800 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:26,801 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:27,032 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:27,033 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 22:53:27,248 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 22:53:27,248 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 22:53:28,249 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 22:53:28,252 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 22:53:28,252 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 22:53:28,253 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 22:53:28,253 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 22:53:28,253 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 22:53:28,254 - modules.friend_request_window - INFO -    📝 备注信息: '014325110117-丁政予-2025-07-30 06:53:04'
2025-07-29 22:53:28,755 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 22:53:28,755 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:28,756 - modules.wechat_auto_add_simple - INFO - ✅ 15546662126 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 22:53:28,756 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15546662126
2025-07-29 22:53:28,757 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:32,502 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2934
2025-07-29 22:53:32,503 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18162602497 (李贤文)
2025-07-29 22:53:32,503 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:39,083 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18162602497
2025-07-29 22:53:39,083 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 22:53:39,084 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18162602497 执行添加朋友操作...
2025-07-29 22:53:39,084 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 22:53:39,084 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 22:53:39,085 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 22:53:39,087 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 22:53:39,094 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-29 22:53:39,099 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 22:53:39,100 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 22:53:39,100 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 22:53:39,101 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 22:53:39,101 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 22:53:39,103 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 22:53:39,103 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 22:53:39,112 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 22:53:39,116 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:53:39,118 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 22:53:39,127 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 22:53:39,129 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 22:53:39,133 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 22:53:39,134 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 22:53:39,641 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 22:53:39,642 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 22:53:39,704 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差41.63, 边缘比例0.0430
2025-07-29 22:53:39,715 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_225339.png
2025-07-29 22:53:39,717 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 22:53:39,720 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 22:53:39,724 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 22:53:39,726 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 22:53:39,729 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 22:53:39,734 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_225339.png
2025-07-29 22:53:39,740 - WeChatAutoAdd - INFO - 底部区域原始检测到 21 个轮廓
2025-07-29 22:53:39,743 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-29 22:53:39,745 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-29 22:53:39,747 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 22:53:39,749 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-29 22:53:39,751 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-29 22:53:39,756 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-29 22:53:39,759 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 22:53:39,760 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-29 22:53:39,762 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:39,764 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 22:53:39,766 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 22:53:39,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:39,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:39,777 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 22:53:39,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 22:53:39,782 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 22:53:39,784 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.5 (阈值:60)
2025-07-29 22:53:39,789 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-29 22:53:39,797 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-29 22:53:39,799 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-29 22:53:39,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,232), 尺寸4x9, 长宽比0.44, 面积36
2025-07-29 22:53:39,814 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,231), 尺寸10x12, 长宽比0.83, 面积120
2025-07-29 22:53:39,818 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,230), 尺寸32x13, 长宽比2.46, 面积416
2025-07-29 22:53:39,827 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=86.8 (阈值:60)
2025-07-29 22:53:39,830 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,230), 尺寸7x6, 长宽比1.17, 面积42
2025-07-29 22:53:39,834 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,230), 尺寸15x13, 长宽比1.15, 面积195
2025-07-29 22:53:39,842 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.7 (阈值:60)
2025-07-29 22:53:39,844 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 22:53:39,847 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-07-29 22:53:39,848 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-29 22:53:39,850 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-29 22:53:39,855 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-29 22:53:39,866 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_225339.png
2025-07-29 22:53:39,870 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-29 22:53:39,875 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 22:53:40,177 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-29 22:53:40,946 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 22:53:40,947 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 22:53:40,949 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:40,949 - modules.wechat_auto_add_simple - INFO - ✅ 18162602497 添加朋友操作执行成功
2025-07-29 22:53:40,950 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:53:40,950 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 22:53:42,954 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 22:53:42,955 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 22:53:42,955 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 22:53:42,956 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 22:53:42,956 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 22:53:42,956 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 22:53:42,956 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 22:53:42,957 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 22:53:42,957 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 22:53:42,957 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18162602497
2025-07-29 22:53:42,958 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 22:53:42,958 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:53:42,959 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:53:42,959 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 22:53:42,959 - modules.friend_request_window - INFO -    📱 phone: '18162602497'
2025-07-29 22:53:42,959 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 22:53:42,960 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 22:53:43,527 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 22:53:43,528 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 22:53:43,528 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 22:53:43,528 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 22:53:43,530 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18162602497
2025-07-29 22:53:43,530 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 22:53:43,531 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:53:43,531 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 22:53:43,531 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 22:53:43,532 - modules.friend_request_window - INFO -    📱 手机号码: 18162602497
2025-07-29 22:53:43,532 - modules.friend_request_window - INFO -    🆔 准考证: 014325110118
2025-07-29 22:53:43,532 - modules.friend_request_window - INFO -    👤 姓名: 李贤文
2025-07-29 22:53:43,533 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 22:53:43,533 - modules.friend_request_window - INFO -    📝 备注格式: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:53:43,534 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 22:53:43,535 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:53:43,537 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 22:53:43,542 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3606338, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 22:53:43,544 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3606338)
2025-07-29 22:53:43,544 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 22:53:43,544 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 22:53:43,545 - modules.friend_request_window - INFO - 🔄 激活窗口: 3606338
2025-07-29 22:53:44,248 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 22:53:44,248 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 22:53:44,249 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 22:53:44,249 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 22:53:44,250 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 22:53:44,250 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 22:53:44,250 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 22:53:44,250 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 22:53:44,251 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 22:53:44,251 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 22:53:44,251 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 22:53:44,252 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 22:53:44,252 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 22:53:44,252 - modules.friend_request_window - INFO -    📝 remark参数: '014325110118-李贤文-2025-07-30 06:53:43' (类型: <class 'str'>, 长度: 36)
2025-07-29 22:53:44,253 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 22:53:44,253 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:53:44,253 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 22:53:44,253 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 22:53:44,254 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 22:53:44,254 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 22:53:44,254 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 22:53:44,255 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 22:53:44,255 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 22:53:45,173 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 22:53:50,412 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 22:53:50,412 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 22:53:50,412 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 22:53:50,412 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 22:53:50,413 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:53:50,722 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:53:50,722 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:53:51,625 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:53:51,633 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:53:51,633 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 22:53:51,634 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 22:53:51,635 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 22:53:51,636 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 22:53:52,137 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 22:53:52,138 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 22:53:52,138 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 22:53:52,139 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 22:53:52,139 - modules.friend_request_window - INFO -    📝 内容: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:53:52,139 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 22:53:52,140 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110118-\xe6\x9d\x8e\xe8\xb4\xa4\xe6\x96\x87-2025-07-30 06:53:43'
2025-07-29 22:53:52,140 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 22:53:53,055 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 22:53:58,298 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 22:53:58,298 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 22:53:58,299 - modules.friend_request_window - INFO -    📝 原始文本: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:53:58,299 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 22:53:58,300 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 22:53:58,608 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 22:53:58,609 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 22:53:59,512 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 22:53:59,522 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 22:53:59,523 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:53:59,523 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 22:53:59,524 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:53:59,525 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 22:54:00,025 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110118-李贤文-2025-07-30 06:53:43'
2025-07-29 22:54:00,026 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 22:54:00,026 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 22:54:00,027 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 22:54:00,027 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 22:54:00,027 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 22:54:00,028 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 22:54:00,828 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 22:54:00,829 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 22:54:00,829 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 22:54:01,438 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 22:54:01,438 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 22:54:01,438 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 22:54:01,439 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 22:54:01,940 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 22:54:01,944 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 22:54:01,944 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 22:54:01,944 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 22:54:01,945 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 22:54:01,945 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 22:54:01,945 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 22:54:01,946 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 22:54:01,946 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 22:54:01,947 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 22:54:01,947 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 22:54:01,948 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 22:54:01,948 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 22:54:01,949 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 22:54:01,949 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 22:54:01,950 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 22:54:01,950 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 22:54:01,951 - modules.friend_request_window - INFO - 📊 窗口 Weixin 错误次数: 1/3
2025-07-29 22:54:01,974 - modules.friend_request_window - INFO - 📊 窗口状态统计: 0/5 个窗口已失败
2025-07-29 22:54:01,974 - modules.friend_request_window - INFO - 🔄 步骤1: 点击错误提示窗口的'确定'按钮...
2025-07-29 22:54:01,975 - modules.friend_request_window - INFO - 🔍 开始智能识别和点击错误对话框的确定按钮...
2025-07-29 22:54:01,975 - modules.friend_request_window - INFO - 📋 从检测结果获取窗口信息: Weixin
2025-07-29 22:54:02,476 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 22:54:02,477 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 22:54:02,477 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 22:54:02,477 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 22:54:02,478 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 22:54:02,478 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 22:54:02,478 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 22:54:02,478 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 22:54:03,389 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 22:54:03,389 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 22:54:03,390 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 22:54:04,390 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭出现频繁错误的微信窗口...
2025-07-29 22:54:04,391 - modules.friend_request_window - INFO - 🎯 精确关闭出现频繁错误的微信窗口: Weixin
2025-07-29 22:54:04,391 - modules.friend_request_window - INFO - 📋 目标窗口句柄: 3541822
2025-07-29 22:54:04,391 - modules.friend_request_window - INFO - 📋 执行顺序: 1.关闭添加朋友窗口 → 2.关闭微信主窗口 → 3.清理残留窗口
2025-07-29 22:54:04,391 - modules.friend_request_window - INFO - 🔄 步骤1: 智能关闭添加朋友窗口...
2025-07-29 22:54:04,392 - modules.friend_request_window - INFO - 🔍 智能搜索添加朋友窗口...
2025-07-29 22:54:04,393 - modules.friend_request_window - INFO - 🔍 发现添加朋友窗口: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-29 22:54:04,393 - modules.friend_request_window - INFO - 🔍 发现添加朋友窗口: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-29 22:54:04,395 - modules.friend_request_window - INFO - 📊 共发现 2 个添加朋友窗口
2025-07-29 22:54:04,396 - modules.friend_request_window - INFO - 🎯 关闭添加朋友窗口: 添加朋友
2025-07-29 22:54:04,896 - modules.friend_request_window - INFO - ✅ 成功关闭添加朋友窗口: 添加朋友
2025-07-29 22:54:04,897 - modules.friend_request_window - INFO - 🎯 关闭添加朋友窗口: 添加朋友
2025-07-29 22:54:05,398 - modules.friend_request_window - INFO - ✅ 成功关闭添加朋友窗口: 添加朋友
2025-07-29 22:54:05,399 - modules.friend_request_window - INFO - 🔄 备用方案：使用坐标点击关闭...
2025-07-29 22:54:05,399 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 22:54:05,899 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 22:54:07,027 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 22:54:07,028 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭目标微信主窗口...
2025-07-29 22:54:07,028 - modules.friend_request_window - WARNING - ⚠️ 目标微信窗口已不存在或无效: 3541822
2025-07-29 22:54:07,029 - modules.friend_request_window - INFO - 🔄 步骤3: 清理残留的错误对话框和相关窗口...
2025-07-29 22:54:07,029 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 22:54:07,030 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 22:54:07,031 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 22:54:07,032 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 22:54:07,033 - modules.friend_request_window - INFO - 📊 共发现 3 个可能的错误对话框
2025-07-29 22:54:07,033 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 22:54:07,235 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 22:54:07,436 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 22:54:07,638 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 22:54:07,656 - modules.friend_request_window - INFO - ✅ 精确关闭完成，仅关闭了出现频繁错误的微信窗口
2025-07-29 22:54:07,662 - modules.friend_request_window - INFO - 🔄 步骤3: 执行最终清理操作...
2025-07-29 22:54:07,663 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 22:54:07,665 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 22:54:07,671 - modules.friend_request_window - INFO - 📊 共发现 1 个可能的错误对话框
2025-07-29 22:54:07,677 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 22:54:07,881 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
