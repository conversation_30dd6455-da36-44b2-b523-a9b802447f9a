2025-07-29 15:33:05,394 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:33:05,395 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:33:05,395 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 15:33:05,395 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:33:05,396 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 15:33:05,397 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:33:05,397 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:33:05,398 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:33:05,399 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:33:05,400 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:33:05,401 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:33:05,403 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:33:05,406 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_153305.log
2025-07-29 15:33:05,407 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:33:05,412 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 15:33:05,413 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 15:33:05,414 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 15:33:05,420 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 15:33:05,421 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 23:33:05
2025-07-29 15:33:05,422 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 15:33:05,423 - __main__ - INFO - 📅 启动时间: 2025-07-29 23:33:05
2025-07-29 15:33:05,423 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 15:33:05,424 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:33:05,970 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:33:05,970 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:33:06,504 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:33:06,505 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:33:06,507 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 15:33:06,507 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 15:33:06,508 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 15:33:06,509 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 15:33:06,509 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 15:33:07,457 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 15:33:07,458 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 15:33:07,458 - __main__ - INFO - 📋 待处理联系人数: 2938
2025-07-29 15:33:07,459 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 15:33:07,459 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2938
2025-07-29 15:33:07,461 - __main__ - INFO - 
============================================================
2025-07-29 15:33:07,461 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-29 15:33:07,462 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 15:33:07,462 - __main__ - INFO - 📊 全局进度：已处理 0/2938 个联系人
2025-07-29 15:33:07,462 - __main__ - INFO - ============================================================
2025-07-29 15:33:07,463 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 15:33:07,467 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 15:33:07,473 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 15:33:07,474 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 15:33:07,475 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:33:07,780 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:33:07,780 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:33:07,781 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:33:07,781 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:33:07,782 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:33:07,782 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:33:07,783 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:33:07,783 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:33:07,783 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:33:07,783 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:33:07,985 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:33:07,987 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:33:07,989 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 15:33:07,990 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:33:08,293 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:33:08,294 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:33:08,294 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:33:08,295 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:33:08,295 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:33:08,295 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:33:08,295 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:33:08,296 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:33:08,296 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:33:08,296 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:33:08,498 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:33:08,498 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:33:08,500 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 15:33:08,800 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:33:08,804 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:33:08,805 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:33:08,805 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:33:08,806 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:33:08,806 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:33:08,807 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 15:33:08,807 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 15:33:09,808 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 15:33:09,809 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 15:33:09,809 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 15:33:09,810 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 15:33:09,810 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 15:33:09,811 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 15:33:09,812 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 15:33:09,812 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 15:33:10,013 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 15:33:10,014 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 15:33:12,385 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 15:33:12,386 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 15:33:12,386 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-29 15:33:14,267 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 15:33:14,469 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 15:33:14,469 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 15:33:16,851 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 15:33:16,852 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 15:33:16,852 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 15:33:19,159 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 15:33:19,360 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 15:33:19,361 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 15:33:21,729 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 15:33:21,730 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 15:33:21,731 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 15:33:23,336 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 15:33:23,537 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 15:33:23,537 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 15:33:25,918 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 15:33:25,918 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 15:33:25,919 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-29 15:33:28,688 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 15:33:28,889 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 15:33:28,890 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 15:33:31,268 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 15:33:31,269 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 15:33:31,269 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 15:33:31,270 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 15:33:31,270 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:33:31,272 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:33:31,272 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:33:31,273 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:33:31,274 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:33:31,275 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 855292, 进程: Weixin.exe)
2025-07-29 15:33:31,278 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 15:33:31,283 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 855292)
2025-07-29 15:33:31,284 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 855292) - 增强版
2025-07-29 15:33:31,590 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:33:31,590 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:33:31,591 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 15:33:31,591 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 15:33:31,591 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 15:33:31,592 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 15:33:31,795 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 15:33:31,795 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 15:33:31,997 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:33:31,998 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:33:31,998 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 15:33:31,999 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 15:33:32,000 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 15:33:32,000 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 15:33:32,000 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 15:33:33,001 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 15:33:33,002 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:33:33,003 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:33:33,003 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:33:33,004 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:33:33,005 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:33:33,006 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 855292, 进程: Weixin.exe)
2025-07-29 15:33:33,007 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 15:33:33,008 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 15:33:33,009 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 15:33:33,009 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 15:33:33,010 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 15:33:33,010 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:33:33,318 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:33:33,318 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:33:33,318 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:33:33,319 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:33:33,319 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:33:33,319 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:33:33,320 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:33:33,320 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:33:33,320 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:33:33,321 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:33:33,523 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:33:33,523 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:33:33,525 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 15:33:33,826 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:33:33,827 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 15:33:33,827 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 15:33:34,828 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 15:33:34,828 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 15:33:34,828 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 15:33:34,831 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_153334.log
2025-07-29 15:33:34,832 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:33:34,833 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 15:33:34,833 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 15:33:34,834 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-29 15:33:34,834 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 15:33:34,835 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 15:33:34,837 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 15:33:34,837 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 15:33:34,838 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 15:33:34,838 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 15:33:34,839 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 15:33:34,839 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 15:33:34,839 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 15:33:34,840 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:33:34,841 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 855292
2025-07-29 15:33:34,842 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 855292) - 增强版
2025-07-29 15:33:35,153 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:33:35,153 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:33:35,154 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 15:33:35,154 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 15:33:35,154 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 15:33:35,155 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 15:33:35,155 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 15:33:35,155 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 15:33:35,357 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:33:35,357 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:33:35,360 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 855292 (API返回: None)
2025-07-29 15:33:35,661 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:33:35,662 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 15:33:35,662 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 15:33:35,662 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 15:33:35,665 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:33:35,665 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 15:33:35,666 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 15:33:35,669 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 15:33:35,670 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 15:33:36,173 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 15:33:36,173 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:33:36,447 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2938 个
2025-07-29 15:33:36,448 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2938 个 (总计: 3135 个)
2025-07-29 15:33:36,448 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 15:33:36,449 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 15:33:36,449 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:33:36,449 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 15:33:36,450 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2938
2025-07-29 15:33:36,450 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15019004476 (王硕)
2025-07-29 15:33:36,451 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:33:43,028 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15019004476
2025-07-29 15:33:43,031 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 15:33:43,031 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15019004476 执行添加朋友操作...
2025-07-29 15:33:43,032 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 15:33:43,032 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 15:33:43,033 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:33:43,034 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 15:33:43,042 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 15:33:43,054 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 15:33:43,056 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 15:33:43,057 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 15:33:43,057 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 15:33:43,058 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 15:33:43,061 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 15:33:43,062 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 15:33:43,075 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 15:33:43,078 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:33:43,079 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:33:43,086 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 15:33:43,088 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 15:33:43,091 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 15:33:43,595 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 15:33:43,595 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 15:33:43,677 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.98, 边缘比例0.0379
2025-07-29 15:33:43,685 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_153343.png
2025-07-29 15:33:43,689 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 15:33:43,690 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 15:33:43,692 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 15:33:43,705 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 15:33:43,705 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 15:33:43,711 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_153343.png
2025-07-29 15:33:43,712 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-07-29 15:33:43,715 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,450), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 15:33:43,717 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 15:33:43,722 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 15:33:43,725 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 15:33:43,726 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 15:33:43,727 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 15:33:43,728 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 15:33:43,729 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 15:33:43,739 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_153343.png
2025-07-29 15:33:43,742 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 15:33:43,743 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 15:33:43,747 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_153343.png
2025-07-29 15:33:43,820 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 15:33:43,821 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 15:33:43,822 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 15:33:43,822 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 15:33:44,123 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 15:33:44,901 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 15:33:44,902 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 15:33:44,903 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:33:44,903 - modules.wechat_auto_add_simple - INFO - ✅ 15019004476 添加朋友操作执行成功
2025-07-29 15:33:44,904 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:33:44,904 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 15:33:46,906 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 15:33:46,906 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 15:33:46,907 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 15:33:46,907 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:33:46,907 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:33:46,907 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:33:46,908 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:33:46,908 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:33:46,909 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 15:33:46,909 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15019004476
2025-07-29 15:33:46,913 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 15:33:46,913 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:33:46,914 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:33:46,914 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 15:33:46,916 - modules.friend_request_window - INFO -    📱 phone: '15019004476'
2025-07-29 15:33:46,917 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 15:33:46,917 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 15:33:47,432 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 15:33:47,432 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 15:33:47,433 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 15:33:47,433 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:33:47,435 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15019004476
2025-07-29 15:33:47,436 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 15:33:47,437 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:33:47,439 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 15:33:47,441 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 15:33:47,442 - modules.friend_request_window - INFO -    📱 手机号码: 15019004476
2025-07-29 15:33:47,443 - modules.friend_request_window - INFO -    🆔 准考证: 014325110113
2025-07-29 15:33:47,444 - modules.friend_request_window - INFO -    👤 姓名: 王硕
2025-07-29 15:33:47,445 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:33:47,451 - modules.friend_request_window - INFO -    📝 备注格式: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:33:47,452 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:33:47,453 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:33:47,454 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 15:33:47,455 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5113898, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 15:33:47,457 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5113898)
2025-07-29 15:33:47,457 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 15:33:47,458 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 15:33:47,459 - modules.friend_request_window - INFO - 🔄 激活窗口: 5113898
2025-07-29 15:33:48,162 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 15:33:48,165 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 15:33:48,166 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 15:33:48,167 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 15:33:48,167 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 15:33:48,168 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:33:48,168 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 15:33:48,168 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:33:48,170 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 15:33:48,171 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 15:33:48,173 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 15:33:48,173 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 15:33:48,174 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 15:33:48,174 - modules.friend_request_window - INFO -    📝 remark参数: '014325110113-王硕-2025-07-29 23:33:47' (类型: <class 'str'>, 长度: 35)
2025-07-29 15:33:48,175 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 15:33:48,176 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:33:48,176 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 15:33:48,177 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 15:33:48,177 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 15:33:48,177 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 15:33:48,178 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 15:33:48,178 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 15:33:48,182 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 15:33:49,101 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 15:33:54,354 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 15:33:54,354 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 15:33:54,355 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 15:33:54,355 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 15:33:54,357 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 15:33:54,667 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:33:54,667 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:33:55,570 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:33:55,581 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:33:55,582 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 15:33:55,582 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 15:33:55,582 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 15:33:55,583 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 15:33:56,083 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 15:33:56,084 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 15:33:56,084 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 15:33:56,085 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 15:33:56,085 - modules.friend_request_window - INFO -    📝 内容: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:33:56,087 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 15:33:56,088 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110113-\xe7\x8e\x8b\xe7\xa1\x95-2025-07-29 23:33:47'
2025-07-29 15:33:56,089 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 15:33:56,999 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 15:34:02,244 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 15:34:02,244 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 15:34:02,245 - modules.friend_request_window - INFO -    📝 原始文本: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:34:02,245 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 15:34:02,246 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-29 15:34:02,556 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:34:02,557 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:34:03,459 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:34:03,469 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:34:03,469 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:34:03,470 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 15:34:03,471 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:34:03,472 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 15:34:03,972 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110113-王硕-2025-07-29 23:33:47'
2025-07-29 15:34:03,973 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 15:34:03,973 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 15:34:03,973 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 15:34:03,973 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 15:34:03,974 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 15:34:03,974 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 15:34:04,775 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 15:34:04,775 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 15:34:04,775 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 15:34:05,386 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:34:05,387 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 15:34:05,387 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 15:34:05,387 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 15:34:05,889 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 15:34:05,892 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 15:34:05,892 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 15:34:05,892 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 15:34:05,893 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 15:34:05,893 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 15:34:05,893 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 15:34:05,894 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 15:34:05,894 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 15:34:05,896 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 15:34:05,896 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 15:34:05,897 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 15:34:05,897 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 15:34:05,898 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 15:34:05,898 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 15:34:05,899 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 15:34:05,900 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 15:34:05,901 - modules.friend_request_window - INFO - 📊 窗口 Weixin 错误次数: 1/3
2025-07-29 15:34:05,922 - modules.friend_request_window - INFO - 📊 窗口状态统计: 0/4 个窗口已失败
2025-07-29 15:34:05,922 - modules.friend_request_window - INFO - 🔄 步骤1: 点击错误提示窗口的'确定'按钮...
2025-07-29 15:34:05,923 - modules.friend_request_window - INFO - 🔍 开始智能识别和点击错误对话框的确定按钮...
2025-07-29 15:34:05,923 - modules.friend_request_window - INFO - 📋 从检测结果获取窗口信息: Weixin
2025-07-29 15:34:06,424 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 15:34:06,425 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 15:34:06,425 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 15:34:06,426 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 15:34:06,426 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 15:34:06,426 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 15:34:06,426 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 15:34:06,427 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 15:34:07,334 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 15:34:07,335 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 15:34:07,335 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 15:34:08,336 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭出现频繁错误的微信窗口...
2025-07-29 15:34:08,336 - modules.friend_request_window - INFO - 🎯 精确关闭出现频繁错误的微信窗口: Weixin
2025-07-29 15:34:08,336 - modules.friend_request_window - INFO - 📋 目标窗口句柄: 6751018
2025-07-29 15:34:08,337 - modules.friend_request_window - INFO - 📋 执行顺序: 1.关闭添加朋友窗口 → 2.关闭微信主窗口 → 3.清理残留窗口
2025-07-29 15:34:08,337 - modules.friend_request_window - INFO - 🔄 步骤1: 智能关闭添加朋友窗口...
2025-07-29 15:34:08,337 - modules.friend_request_window - INFO - 🔍 智能搜索添加朋友窗口...
2025-07-29 15:34:08,338 - modules.friend_request_window - INFO - 🔍 发现添加朋友窗口: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-29 15:34:08,340 - modules.friend_request_window - INFO - 📊 共发现 1 个添加朋友窗口
2025-07-29 15:34:08,340 - modules.friend_request_window - INFO - 🎯 关闭添加朋友窗口: 添加朋友
2025-07-29 15:34:08,841 - modules.friend_request_window - INFO - ✅ 成功关闭添加朋友窗口: 添加朋友
2025-07-29 15:34:08,842 - modules.friend_request_window - INFO - 🔄 备用方案：使用坐标点击关闭...
2025-07-29 15:34:08,842 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 15:34:09,343 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 15:34:10,452 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 15:34:10,453 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭目标微信主窗口...
2025-07-29 15:34:10,453 - modules.friend_request_window - WARNING - ⚠️ 目标微信窗口已不存在或无效: 6751018
2025-07-29 15:34:10,453 - modules.friend_request_window - INFO - 🔄 步骤3: 清理残留的错误对话框和相关窗口...
2025-07-29 15:34:10,454 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 15:34:10,455 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 15:34:10,455 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 15:34:10,456 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 15:34:10,457 - modules.friend_request_window - INFO - 📊 共发现 3 个可能的错误对话框
2025-07-29 15:34:10,457 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 15:34:10,658 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 15:34:10,859 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 15:34:11,064 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 15:34:11,078 - modules.friend_request_window - INFO - ✅ 精确关闭完成，仅关闭了出现频繁错误的微信窗口
2025-07-29 15:34:11,080 - modules.friend_request_window - INFO - 🔄 步骤3: 执行最终清理操作...
2025-07-29 15:34:11,080 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 15:34:11,082 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 15:34:11,084 - modules.friend_request_window - INFO - 📊 共发现 1 个可能的错误对话框
2025-07-29 15:34:11,085 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 15:34:11,290 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 15:34:11,347 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-29 15:34:11,385 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-29 15:34:11,405 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-29 15:34:11,426 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-29 15:34:11,458 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
