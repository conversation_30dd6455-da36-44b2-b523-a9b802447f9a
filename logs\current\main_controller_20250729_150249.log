2025-07-29 15:02:49,692 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:02:49,693 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:02:49,693 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 15:02:49,694 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:02:49,695 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 15:02:49,695 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:02:49,695 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:02:49,696 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:02:49,696 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:02:49,697 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:02:49,698 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:02:49,700 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:02:49,703 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_150249.log
2025-07-29 15:02:49,704 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:02:49,705 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 15:02:49,706 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 15:02:49,706 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 15:02:49,707 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 15:02:49,708 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 23:02:49
2025-07-29 15:02:49,709 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 15:02:49,712 - __main__ - INFO - 📅 启动时间: 2025-07-29 23:02:49
2025-07-29 15:02:49,713 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 15:02:49,713 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:02:50,257 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:02:50,258 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:02:50,837 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:02:50,839 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:02:50,842 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 15:02:50,843 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 15:02:50,843 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 15:02:50,845 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 15:02:50,848 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 15:02:51,723 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 15:02:51,723 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 15:02:51,724 - __main__ - INFO - 📋 待处理联系人数: 2940
2025-07-29 15:02:51,724 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 15:02:51,724 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2940
2025-07-29 15:02:51,725 - __main__ - INFO - 
============================================================
2025-07-29 15:02:51,725 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-29 15:02:51,725 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 15:02:51,725 - __main__ - INFO - 📊 全局进度：已处理 0/2940 个联系人
2025-07-29 15:02:51,726 - __main__ - INFO - ============================================================
2025-07-29 15:02:51,726 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 15:02:51,726 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 15:02:51,727 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 15:02:51,727 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 15:02:51,728 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:02:52,032 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:02:52,033 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:02:52,033 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:02:52,033 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:02:52,034 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:02:52,034 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:02:52,034 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:02:52,035 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:02:52,035 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:02:52,035 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:02:52,237 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:02:52,238 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:02:52,238 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 15:02:52,238 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:02:52,542 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:02:52,543 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:02:52,543 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:02:52,544 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:02:52,544 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:02:52,544 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:02:52,544 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:02:52,545 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:02:52,545 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:02:52,545 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:02:52,747 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:02:52,748 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:02:52,749 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 15:02:53,050 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:02:53,051 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:02:53,051 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:02:53,051 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:02:53,052 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:02:53,052 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:02:53,052 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 15:02:53,053 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 15:02:54,053 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 15:02:54,054 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 15:02:54,054 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 15:02:54,054 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 15:02:54,055 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 15:02:54,055 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 15:02:54,055 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 15:02:54,055 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 15:02:54,256 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 15:02:54,257 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 15:02:56,630 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 15:02:56,630 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 15:02:56,631 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 15:02:58,221 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 15:02:58,422 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 15:02:58,423 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 15:03:00,796 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 15:03:00,797 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 15:03:00,797 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 15:03:02,632 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 15:03:02,833 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 15:03:02,834 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 15:03:05,202 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 15:03:05,203 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 15:03:05,203 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 15:03:07,543 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 15:03:07,743 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 15:03:07,744 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 15:03:10,113 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 15:03:10,113 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 15:03:10,114 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 15:03:11,696 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 15:03:11,897 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 15:03:11,898 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 15:03:14,279 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 15:03:14,279 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 15:03:14,280 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 15:03:14,280 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 15:03:14,280 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:03:14,282 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:03:14,282 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:03:14,283 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:03:14,283 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:03:14,284 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 920506, 进程: Weixin.exe)
2025-07-29 15:03:14,287 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 15:03:14,287 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 920506)
2025-07-29 15:03:14,288 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 920506) - 增强版
2025-07-29 15:03:14,591 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:03:14,591 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:03:14,592 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 15:03:14,592 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 15:03:14,592 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 15:03:14,593 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 15:03:14,796 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 15:03:14,797 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 15:03:14,998 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:03:14,999 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:03:14,999 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 15:03:14,999 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 15:03:14,999 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 15:03:15,000 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 15:03:15,000 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 15:03:16,000 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 15:03:16,001 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:03:16,002 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:03:16,003 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:03:16,004 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:03:16,004 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:03:16,005 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 920506, 进程: Weixin.exe)
2025-07-29 15:03:16,008 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 15:03:16,009 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 15:03:16,009 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 15:03:16,010 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 15:03:16,010 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 15:03:16,011 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:03:16,318 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:03:16,318 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:03:16,319 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:03:16,319 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:03:16,319 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:03:16,319 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:03:16,320 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:03:16,320 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:03:16,321 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:03:16,321 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:03:16,523 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:03:16,524 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:03:16,525 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 15:03:16,826 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:03:16,826 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 15:03:16,826 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 15:03:17,827 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 15:03:17,828 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 15:03:17,828 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 15:03:17,831 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_150317.log
2025-07-29 15:03:17,831 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:03:17,832 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 15:03:17,832 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 15:03:17,832 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-29 15:03:17,833 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 15:03:17,833 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 15:03:17,835 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 15:03:17,836 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 15:03:17,836 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 15:03:17,837 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 15:03:17,837 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 15:03:17,837 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 15:03:17,837 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 15:03:17,838 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:03:17,839 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 920506
2025-07-29 15:03:17,839 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 920506) - 增强版
2025-07-29 15:03:18,148 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:03:18,148 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:03:18,149 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 15:03:18,149 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 15:03:18,150 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 15:03:18,150 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 15:03:18,150 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 15:03:18,150 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 15:03:18,352 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:03:18,353 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:03:18,357 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 920506 (API返回: None)
2025-07-29 15:03:18,658 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:03:18,659 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 15:03:18,659 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 15:03:18,659 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 15:03:18,660 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:03:18,660 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 15:03:18,661 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 15:03:18,665 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 15:03:18,666 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 15:03:19,159 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 15:03:19,159 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:03:19,499 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2940 个
2025-07-29 15:03:19,500 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2940 个 (总计: 3135 个)
2025-07-29 15:03:19,500 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 15:03:19,500 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 15:03:19,501 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:03:19,501 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 15:03:19,502 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2940
2025-07-29 15:03:19,502 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 19904882280 (黎敏峰)
2025-07-29 15:03:19,503 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:03:26,073 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 19904882280
2025-07-29 15:03:26,074 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 15:03:26,074 - modules.wechat_auto_add_simple - INFO - 👥 开始为 19904882280 执行添加朋友操作...
2025-07-29 15:03:26,074 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 15:03:26,075 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 15:03:26,075 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:03:26,076 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 15:03:26,081 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 15:03:26,081 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 15:03:26,082 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 15:03:26,083 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 15:03:26,083 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 15:03:26,084 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 15:03:26,084 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 15:03:26,084 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 15:03:26,088 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 15:03:26,093 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:03:26,096 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:03:26,099 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 15:03:26,103 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 15:03:26,104 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 15:03:26,606 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 15:03:26,608 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 15:03:26,684 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-29 15:03:26,684 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-29 15:03:26,692 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_150326.png
2025-07-29 15:03:26,694 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 15:03:26,695 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 15:03:26,696 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 15:03:26,697 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 15:03:26,698 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 15:03:26,703 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_150326.png
2025-07-29 15:03:26,705 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 15:03:26,706 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 15:03:26,707 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 15:03:26,708 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 15:03:26,709 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 15:03:26,710 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 15:03:26,711 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 15:03:26,712 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 15:03:26,720 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_150326.png
2025-07-29 15:03:26,723 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 15:03:26,724 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 15:03:26,730 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_150326.png
2025-07-29 15:03:26,790 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 15:03:26,792 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 15:03:26,794 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 15:03:26,796 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 15:03:27,098 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 15:03:27,866 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 15:03:27,867 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 15:03:27,868 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:03:27,868 - modules.wechat_auto_add_simple - INFO - ✅ 19904882280 添加朋友操作执行成功
2025-07-29 15:03:27,868 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:03:27,869 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 15:03:29,871 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 15:03:29,871 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 15:03:29,871 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 15:03:29,872 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:03:29,872 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:03:29,872 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:03:29,873 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:03:29,873 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:03:29,873 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 15:03:29,873 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 19904882280
2025-07-29 15:03:29,877 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 15:03:29,878 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:03:29,878 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:03:29,878 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 15:03:29,879 - modules.friend_request_window - INFO -    📱 phone: '19904882280'
2025-07-29 15:03:29,880 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 15:03:29,880 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 15:03:30,377 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 15:03:30,378 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 15:03:30,378 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 15:03:30,379 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:03:30,380 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 19904882280
2025-07-29 15:03:30,381 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 15:03:30,382 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:03:30,383 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 15:03:30,387 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 15:03:30,388 - modules.friend_request_window - INFO -    📱 手机号码: 19904882280
2025-07-29 15:03:30,388 - modules.friend_request_window - INFO -    🆔 准考证: 014325110109
2025-07-29 15:03:30,389 - modules.friend_request_window - INFO -    👤 姓名: 黎敏峰
2025-07-29 15:03:30,390 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:03:30,395 - modules.friend_request_window - INFO -    📝 备注格式: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:30,396 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:03:30,397 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:30,397 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 15:03:30,399 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1182418, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 15:03:30,401 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1182418)
2025-07-29 15:03:30,401 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 15:03:30,402 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 15:03:30,402 - modules.friend_request_window - INFO - 🔄 激活窗口: 1182418
2025-07-29 15:03:31,105 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 15:03:31,106 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 15:03:31,106 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 15:03:31,107 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 15:03:31,107 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 15:03:31,107 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:03:31,108 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 15:03:31,108 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:03:31,109 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 15:03:31,109 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 15:03:31,109 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 15:03:31,110 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 15:03:31,110 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 15:03:31,111 - modules.friend_request_window - INFO -    📝 remark参数: '014325110109-黎敏峰-2025-07-29 23:03:30' (类型: <class 'str'>, 长度: 36)
2025-07-29 15:03:31,111 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 15:03:31,112 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:31,113 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 15:03:31,115 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 15:03:31,115 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 15:03:31,116 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 15:03:31,117 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 15:03:31,117 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 15:03:31,118 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 15:03:32,028 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 15:03:37,270 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 15:03:37,271 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 15:03:37,271 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 15:03:37,271 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 15:03:37,272 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python test_frequency...' (前50字符)
2025-07-29 15:03:37,585 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:03:37,586 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:03:38,488 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:03:38,498 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:03:38,499 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 15:03:38,499 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 15:03:38,500 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 15:03:38,500 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 15:03:39,001 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 15:03:39,001 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 15:03:39,001 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 15:03:39,002 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 15:03:39,002 - modules.friend_request_window - INFO -    📝 内容: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:39,002 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 15:03:39,003 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110109-\xe9\xbb\x8e\xe6\x95\x8f\xe5\xb3\xb0-2025-07-29 23:03:30'
2025-07-29 15:03:39,003 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 15:03:39,911 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 15:03:45,154 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 15:03:45,155 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 15:03:45,156 - modules.friend_request_window - INFO -    📝 原始文本: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:45,157 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 15:03:45,158 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python test_frequency...' (前50字符)
2025-07-29 15:03:45,471 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:03:45,472 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:03:46,376 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:03:46,386 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:03:46,386 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:46,387 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 15:03:46,387 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:46,388 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 15:03:46,889 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:46,889 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 15:03:46,890 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 15:03:46,890 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 15:03:46,890 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 15:03:46,891 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 15:03:46,891 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 15:03:47,691 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 15:03:47,692 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 15:03:47,693 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 15:03:48,312 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:03:48,312 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 15:03:48,313 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 15:03:48,313 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 15:03:48,832 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:49,063 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:49,296 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:49,532 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:49,768 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:50,004 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:50,236 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:50,471 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:50,705 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:50,941 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:51,177 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:51,409 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:51,646 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:51,882 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:52,117 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:52,468 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:52,701 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:52,935 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:53,169 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:03:53,385 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 15:03:53,385 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 15:03:54,386 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 15:03:54,389 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 15:03:54,390 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 15:03:54,390 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 15:03:54,390 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 15:03:54,391 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:03:54,391 - modules.friend_request_window - INFO -    📝 备注信息: '014325110109-黎敏峰-2025-07-29 23:03:30'
2025-07-29 15:03:54,892 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 15:03:54,893 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:03:54,893 - modules.wechat_auto_add_simple - INFO - ✅ 19904882280 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 15:03:54,894 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 19904882280
2025-07-29 15:03:54,895 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:03:58,456 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2940
2025-07-29 15:03:58,456 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18672909010 (张鹏)
2025-07-29 15:03:58,457 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:04:05,206 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18672909010
2025-07-29 15:04:05,296 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 15:04:05,363 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18672909010 执行添加朋友操作...
2025-07-29 15:04:05,408 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 15:04:05,450 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 15:04:05,499 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:04:05,665 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 15:04:05,726 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 15:04:05,938 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 15:04:05,982 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 15:04:06,012 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 15:04:06,067 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 15:04:06,098 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 15:04:06,138 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 15:04:06,184 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 15:04:06,303 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 15:04:06,353 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:04:06,411 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:04:06,478 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 15:04:06,763 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 15:04:07,001 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 15:04:07,919 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 15:04:08,075 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 15:04:08,772 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.63, 边缘比例0.0366
2025-07-29 15:04:09,442 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_150409.png
2025-07-29 15:04:09,704 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 15:04:09,843 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 15:04:09,964 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 15:04:10,054 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 15:04:10,177 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 15:04:10,268 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_150410.png
2025-07-29 15:04:10,295 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 15:04:10,307 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 15:04:10,328 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 15:04:10,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 15:04:10,397 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 15:04:10,429 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 15:04:10,454 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 15:04:10,464 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 15:04:10,484 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_150410.png
2025-07-29 15:04:10,495 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 15:04:10,502 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 15:04:10,528 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_150410.png
2025-07-29 15:04:10,569 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 15:04:10,645 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 15:04:10,705 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 15:04:10,800 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 15:04:11,160 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 15:04:11,981 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 15:04:11,996 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 15:04:12,003 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:04:12,004 - modules.wechat_auto_add_simple - INFO - ✅ 18672909010 添加朋友操作执行成功
2025-07-29 15:04:12,005 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:04:12,017 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 15:04:14,022 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 15:04:14,023 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 15:04:14,023 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 15:04:14,023 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:04:14,024 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:04:14,024 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:04:14,024 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:04:14,024 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:04:14,025 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 15:04:14,025 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18672909010
2025-07-29 15:04:14,026 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 15:04:14,026 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:04:14,026 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:04:14,026 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 15:04:14,027 - modules.friend_request_window - INFO -    📱 phone: '18672909010'
2025-07-29 15:04:14,027 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 15:04:14,027 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 15:04:14,568 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 15:04:14,568 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 15:04:14,568 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 15:04:14,569 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:04:14,570 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18672909010
2025-07-29 15:04:14,570 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 15:04:14,571 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:04:14,571 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 15:04:14,572 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 15:04:14,572 - modules.friend_request_window - INFO -    📱 手机号码: 18672909010
2025-07-29 15:04:14,573 - modules.friend_request_window - INFO -    🆔 准考证: 014325110110
2025-07-29 15:04:14,573 - modules.friend_request_window - INFO -    👤 姓名: 张鹏
2025-07-29 15:04:14,574 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:04:14,574 - modules.friend_request_window - INFO -    📝 备注格式: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:14,575 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:04:14,575 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:14,575 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 15:04:14,576 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1313490, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 15:04:14,579 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1313490)
2025-07-29 15:04:14,579 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 15:04:14,580 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 15:04:14,581 - modules.friend_request_window - INFO - 🔄 激活窗口: 1313490
2025-07-29 15:04:15,284 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 15:04:15,285 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 15:04:15,285 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 15:04:15,286 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 15:04:15,286 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 15:04:15,286 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:04:15,286 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 15:04:15,287 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:04:15,287 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 15:04:15,287 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 15:04:15,288 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 15:04:15,288 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 15:04:15,289 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 15:04:15,289 - modules.friend_request_window - INFO -    📝 remark参数: '014325110110-张鹏-2025-07-29 23:04:14' (类型: <class 'str'>, 长度: 35)
2025-07-29 15:04:15,290 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 15:04:15,291 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:15,292 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 15:04:15,293 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 15:04:15,293 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 15:04:15,293 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 15:04:15,293 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 15:04:15,294 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 15:04:15,294 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 15:04:16,209 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 15:04:21,467 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 15:04:21,468 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 15:04:21,468 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 15:04:21,468 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 15:04:21,469 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python test_frequency...' (前50字符)
2025-07-29 15:04:21,777 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:04:21,778 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:04:22,681 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:04:22,690 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:04:22,690 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 15:04:22,691 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 15:04:22,692 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 15:04:22,692 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 15:04:23,193 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 15:04:23,193 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 15:04:23,194 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 15:04:23,194 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 15:04:23,194 - modules.friend_request_window - INFO -    📝 内容: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:23,194 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 15:04:23,195 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110110-\xe5\xbc\xa0\xe9\xb9\x8f-2025-07-29 23:04:14'
2025-07-29 15:04:23,195 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 15:04:24,109 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 15:04:29,353 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 15:04:29,353 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 15:04:29,354 - modules.friend_request_window - INFO -    📝 原始文本: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:29,354 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 15:04:29,355 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python test_frequency...' (前50字符)
2025-07-29 15:04:29,664 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:04:29,664 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:04:30,567 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:04:30,577 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:04:30,578 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:30,579 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 15:04:30,579 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:30,580 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 15:04:31,080 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110110-张鹏-2025-07-29 23:04:14'
2025-07-29 15:04:31,081 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 15:04:31,081 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 15:04:31,082 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 15:04:31,082 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 15:04:31,082 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 15:04:31,082 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 15:04:31,883 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 15:04:31,883 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 15:04:31,884 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 15:04:32,493 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:04:32,493 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 15:04:32,494 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 15:04:32,494 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 15:04:32,996 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 15:04:32,998 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 15:04:32,998 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 15:04:32,999 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 15:04:32,999 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 15:04:32,999 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 15:04:32,999 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 15:04:33,000 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 15:04:33,000 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 15:04:33,000 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 15:04:33,000 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 15:04:33,001 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 15:04:33,002 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 15:04:33,003 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 15:04:33,003 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 15:04:33,004 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 15:04:33,005 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 15:04:33,005 - modules.friend_request_window - INFO - 📊 窗口 Weixin 错误次数: 1/3
2025-07-29 15:04:33,026 - modules.friend_request_window - INFO - 📊 窗口状态统计: 0/3 个窗口已失败
2025-07-29 15:04:33,027 - modules.friend_request_window - INFO - 🔄 步骤1: 点击错误提示窗口的'确定'按钮...
2025-07-29 15:04:33,027 - modules.friend_request_window - INFO - 🔍 开始智能识别和点击错误对话框的确定按钮...
2025-07-29 15:04:33,027 - modules.friend_request_window - INFO - 📋 从检测结果获取窗口信息: Weixin
2025-07-29 15:04:33,529 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 15:04:33,530 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 15:04:33,530 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 15:04:33,531 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 15:04:33,531 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 15:04:33,532 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 15:04:33,532 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 15:04:33,532 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 15:04:34,443 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 15:04:34,444 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 15:04:34,444 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 15:04:35,444 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭出现频繁错误的微信窗口...
2025-07-29 15:04:35,445 - modules.friend_request_window - INFO - 🎯 精确关闭出现频繁错误的微信窗口: Weixin
2025-07-29 15:04:35,445 - modules.friend_request_window - INFO - 📋 目标窗口句柄: 2754816
2025-07-29 15:04:35,445 - modules.friend_request_window - INFO - 📋 执行顺序: 1.关闭添加朋友窗口 → 2.关闭微信主窗口 → 3.清理残留窗口
2025-07-29 15:04:35,446 - modules.friend_request_window - INFO - 🔄 步骤1: 智能关闭添加朋友窗口...
2025-07-29 15:04:35,446 - modules.friend_request_window - INFO - 🔍 智能搜索添加朋友窗口...
2025-07-29 15:04:35,447 - modules.friend_request_window - INFO - 🔍 发现添加朋友窗口: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-29 15:04:35,450 - modules.friend_request_window - INFO - 📊 共发现 1 个添加朋友窗口
2025-07-29 15:04:35,450 - modules.friend_request_window - INFO - 🎯 关闭添加朋友窗口: 添加朋友
2025-07-29 15:04:35,951 - modules.friend_request_window - INFO - ✅ 成功关闭添加朋友窗口: 添加朋友
2025-07-29 15:04:35,952 - modules.friend_request_window - INFO - 🔄 备用方案：使用坐标点击关闭...
2025-07-29 15:04:35,952 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 15:04:36,453 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 15:04:37,580 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 15:04:37,581 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭目标微信主窗口...
2025-07-29 15:04:37,581 - modules.friend_request_window - WARNING - ⚠️ 目标微信窗口已不存在或无效: 2754816
2025-07-29 15:04:37,581 - modules.friend_request_window - INFO - 🔄 步骤3: 清理残留的错误对话框和相关窗口...
2025-07-29 15:04:37,582 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 15:04:37,583 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 15:04:37,583 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 15:04:37,584 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 15:04:37,585 - modules.friend_request_window - INFO - 📊 共发现 3 个可能的错误对话框
2025-07-29 15:04:37,586 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 15:04:37,786 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 15:04:37,988 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 15:04:38,190 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 15:04:38,211 - modules.friend_request_window - INFO - ✅ 精确关闭完成，仅关闭了出现频繁错误的微信窗口
2025-07-29 15:04:38,221 - modules.friend_request_window - INFO - 🔄 步骤3: 执行最终清理操作...
2025-07-29 15:04:38,222 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 15:04:38,224 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 15:04:38,225 - modules.friend_request_window - INFO - 📊 共发现 1 个可能的错误对话框
2025-07-29 15:04:38,227 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 15:04:38,463 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 15:04:38,680 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-29 15:04:38,703 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-29 15:04:38,724 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-29 15:04:38,742 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-29 15:04:38,768 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-29 15:04:38,787 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 15:04:38,813 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 15:04:38,829 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:04:38,846 - modules.friend_request_window - INFO -    📝 备注信息: '014325110110-张鹏-2025-07-29 23:04:14'
