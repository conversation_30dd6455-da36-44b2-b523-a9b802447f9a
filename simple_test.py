#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证修复
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_import():
    """测试导入"""
    try:
        print("🔍 测试导入频率错误处理器...")
        from modules.frequency_error_handler import FrequencyErrorHandler
        print("✅ 导入成功")
        
        print("🔧 创建处理器实例...")
        handler = FrequencyErrorHandler()
        print("✅ 实例创建成功")
        
        print("🔍 检查新增方法...")
        if hasattr(handler, '_sync_add_friend_window_position'):
            print("✅ _sync_add_friend_window_position 方法存在")
        else:
            print("❌ _sync_add_friend_window_position 方法不存在")
            
        if hasattr(handler, '_find_add_friend_windows'):
            print("✅ _find_add_friend_windows 方法存在")
        else:
            print("❌ _find_add_friend_windows 方法不存在")
        
        print("🎉 基本测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 开始简单测试...")
    success = test_import()
    if success:
        print("✅ 测试成功")
    else:
        print("❌ 测试失败")
