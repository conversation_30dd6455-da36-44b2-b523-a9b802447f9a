2025-07-30 12:19:10,444 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:19:10,445 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:19:10,445 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:10,446 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:10,447 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:19:10,447 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:19:10,449 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:19:10,450 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:19:10,450 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:19:10,451 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:19:10,451 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:19:10,451 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:19:10,453 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:10,456 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_121910.log
2025-07-30 12:19:10,459 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:19:10,461 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:19:10,463 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:19:10,464 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:19:10,464 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:19:10,465 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:19:10,465 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 20:19:10
2025-07-30 12:19:10,466 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:19:10,467 - __main__ - INFO - 📅 启动时间: 2025-07-30 20:19:10
2025-07-30 12:19:10,471 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:19:10,474 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:19:11,009 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:19:11,009 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:19:11,563 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:19:11,563 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:19:11,564 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:19:11,564 - __main__ - INFO - ✅ 找到 2 个可用微信窗口
2025-07-30 12:19:11,565 - __main__ - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 12:19:11,565 - __main__ - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:19:11,565 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 12:19:12,427 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 12:19:12,427 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 12:19:12,428 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:19:12,428 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:19:12,429 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 12:19:12,429 - __main__ - INFO - 
============================================================
2025-07-30 12:19:12,429 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 12:19:12,430 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:19:12,430 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:19:12,430 - __main__ - INFO - ============================================================
2025-07-30 12:19:12,430 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:19:12,431 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:19:12,431 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:19:12,431 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:19:12,431 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:19:12,851 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 12:19:12,851 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:19:12,852 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:19:12,852 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:19:12,853 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:19:12,853 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:19:12,853 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:19:12,853 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:19:12,854 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:19:12,854 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:19:13,056 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 12:19:13,056 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:19:13,057 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:19:13,057 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:19:13,467 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 12:19:13,468 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:19:13,468 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:19:13,469 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:19:13,469 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:19:13,469 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:19:13,469 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:19:13,470 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:19:13,470 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:19:13,470 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:19:13,673 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 12:19:13,673 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:19:13,677 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:19:13,978 - modules.window_manager - WARNING - ⚠️ 窗口可能未完全置于最前面 (当前前台: 9045874)
2025-07-30 12:19:13,978 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:19:13,978 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:19:13,979 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:19:13,979 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:19:13,980 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:19:13,980 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 12:19:13,980 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:19:14,981 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:19:14,982 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 12:19:14,986 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:19:14,998 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:19:15,001 - modules.main_interface - WARNING - ⚠️ 当前前台窗口不是微信窗口: 'main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code' (类名: Chrome_WidgetWin_1)
2025-07-30 12:19:15,002 - modules.main_interface - INFO - 🔄 尝试查找并激活微信窗口...
2025-07-30 12:19:15,003 - modules.main_interface - INFO - 🔄 使用已初始化的window_manager查找并激活微信窗口...
2025-07-30 12:19:15,003 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:19:15,008 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:19:15,009 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:19:15,011 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:19:15,011 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:19:15,013 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:19:15,013 - modules.main_interface - INFO - 🎯 找到微信窗口: 微信 (类名: Qt51514QWindowIcon, 句柄: 263568)
2025-07-30 12:19:15,014 - modules.main_interface - INFO - 🚀 使用window_manager激活窗口（包含位置调整）...
2025-07-30 12:19:15,014 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
