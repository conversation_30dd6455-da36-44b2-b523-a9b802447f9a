2025-07-30 10:55:31,292 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:55:31,293 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:55:31,294 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:55:31,294 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:55:31,295 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 10:55:31,295 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:55:31,296 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:55:31,297 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:55:31,297 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:55:31,297 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:55:31,298 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:55:31,301 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:55:31,305 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_105531.log
2025-07-30 10:55:31,306 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:55:31,306 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:55:31,306 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:55:31,307 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 10:55:31,307 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 10:55:31,308 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 18:55:31
2025-07-30 10:55:31,308 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 10:55:31,309 - __main__ - INFO - 📅 启动时间: 2025-07-30 18:55:31
2025-07-30 10:55:31,312 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 10:55:31,313 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:55:31,854 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:55:31,855 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:55:32,380 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:55:32,380 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:55:32,384 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:55:32,384 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 10:55:32,385 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 10:55:32,386 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 10:55:32,386 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 10:55:33,367 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 10:55:33,367 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 10:55:33,368 - __main__ - INFO - 📋 待处理联系人数: 2905
2025-07-30 10:55:33,369 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 10:55:33,370 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2905
2025-07-30 10:55:33,370 - __main__ - INFO - 
============================================================
2025-07-30 10:55:33,370 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 10:55:33,371 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:55:33,371 - __main__ - INFO - 📊 全局进度：已处理 0/2905 个联系人
2025-07-30 10:55:33,371 - __main__ - INFO - ============================================================
2025-07-30 10:55:33,372 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 10:55:33,372 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:55:33,372 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:55:33,373 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 10:55:33,374 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:55:33,697 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:55:33,697 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:55:33,697 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:55:33,698 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:55:33,699 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:55:33,699 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:55:33,700 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:55:33,700 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:55:33,702 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:55:33,702 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:55:33,904 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:55:33,904 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:55:33,904 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:55:33,905 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:55:34,209 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:55:34,210 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:55:34,211 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:55:34,212 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:55:34,212 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:55:34,213 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:55:34,213 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:55:34,213 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:55:34,214 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:55:34,214 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:55:34,416 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:55:34,417 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:55:34,418 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:55:34,719 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:55:34,719 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:55:34,720 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:55:34,720 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:55:34,720 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:55:34,721 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:55:34,721 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:55:34,721 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:55:35,722 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:55:35,723 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 10:55:35,723 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:55:35,723 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:55:35,724 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:55:35,724 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:55:35,724 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:55:35,725 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:55:35,932 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:55:35,989 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:55:38,387 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:55:38,388 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:55:38,388 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 10:55:41,063 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:55:41,263 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:55:41,265 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:55:43,658 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:55:43,662 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:55:43,670 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 10:55:46,187 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:55:46,388 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:55:46,388 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:55:48,768 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:55:48,769 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:55:48,770 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 10:55:51,642 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:55:51,846 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:55:51,846 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:55:54,217 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:55:54,218 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:55:54,219 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 10:55:55,820 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:55:56,020 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:55:56,021 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:55:58,402 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:55:58,403 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:55:58,403 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:55:58,404 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:55:58,404 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:55:58,406 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:55:58,406 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:55:58,407 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:55:58,407 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:55:58,408 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 789492, 进程: Weixin.exe)
2025-07-30 10:55:58,411 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:55:58,411 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 789492)
2025-07-30 10:55:58,412 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 789492) - 增强版
2025-07-30 10:55:58,717 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:55:58,717 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:55:58,717 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:55:58,718 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:55:58,718 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:55:58,719 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:55:58,923 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:55:58,923 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:55:59,125 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:55:59,125 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:55:59,126 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:55:59,126 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:55:59,126 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:55:59,127 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:55:59,127 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:56:00,128 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:56:00,128 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:56:00,130 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:56:00,130 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:56:00,131 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:56:00,132 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:56:00,133 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 789492, 进程: Weixin.exe)
2025-07-30 10:56:00,135 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:56:00,136 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:56:00,136 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:56:00,136 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:56:00,137 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:56:00,137 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:56:00,445 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:56:00,445 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:56:00,446 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:56:00,446 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:56:00,447 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:56:00,447 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:56:00,448 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:56:00,448 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:56:00,448 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:56:00,449 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:56:00,650 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:56:00,651 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:56:00,652 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:56:00,953 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:56:00,954 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:56:00,954 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:56:01,954 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:56:01,955 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 10:56:01,955 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:56:01,958 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_105601.log
2025-07-30 10:56:01,959 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:56:01,960 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:56:01,961 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:56:01,961 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 10:56:01,962 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:56:01,962 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:56:01,965 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 10:56:01,966 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 10:56:01,967 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:56:01,968 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:56:01,968 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 10:56:01,969 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 10:56:01,970 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:56:01,971 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:56:01,971 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 789492
2025-07-30 10:56:01,975 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 789492) - 增强版
2025-07-30 10:56:02,285 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:56:02,286 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:56:02,286 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:56:02,286 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:56:02,287 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 10:56:02,287 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:56:02,287 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 10:56:02,288 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:56:02,490 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:56:02,490 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:56:02,494 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 789492 (API返回: None)
2025-07-30 10:56:02,794 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:56:02,795 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:56:02,795 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:56:02,795 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:56:02,796 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:56:02,796 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:56:02,797 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:56:02,800 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:56:02,802 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:56:03,295 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:56:03,295 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:56:03,543 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2920 个
2025-07-30 10:56:03,544 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2920 个 (总计: 3135 个)
2025-07-30 10:56:03,545 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:56:03,545 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:56:03,546 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:03,546 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:56:03,547 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2920
2025-07-30 10:56:03,547 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13783597105 (赵甲琪)
2025-07-30 10:56:03,547 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:10,126 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13783597105
2025-07-30 10:56:10,127 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:56:10,127 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13783597105 执行添加朋友操作...
2025-07-30 10:56:10,127 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:56:10,128 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:56:10,128 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:56:10,129 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:56:10,135 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 10:56:10,137 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:56:10,137 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:56:10,138 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:56:10,138 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:56:10,138 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:56:10,139 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:56:10,139 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:56:10,143 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:56:10,147 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:56:10,152 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:56:10,155 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 10:56:10,157 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 10:56:10,165 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:56:10,669 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:56:10,670 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:56:10,739 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.52, 边缘比例0.0398
2025-07-30 10:56:10,747 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_105610.png
2025-07-30 10:56:10,750 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:56:10,751 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:56:10,753 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:56:10,757 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:56:10,759 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:56:10,765 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_105610.png
2025-07-30 10:56:10,767 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 10:56:10,769 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 10:56:10,772 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 10:56:10,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:56:10,777 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 10:56:10,778 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 10:56:10,778 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 10:56:10,781 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:56:10,791 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_105610.png
2025-07-30 10:56:10,794 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:56:10,797 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 10:56:10,802 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_105610.png
2025-07-30 10:56:10,866 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:56:10,867 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 10:56:10,868 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:56:10,868 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:56:11,169 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 10:56:11,937 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:56:11,938 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:56:11,939 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:11,940 - modules.wechat_auto_add_simple - INFO - ✅ 13783597105 添加朋友操作执行成功
2025-07-30 10:56:11,940 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:11,940 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:56:13,943 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:56:13,943 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:56:13,943 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:56:13,944 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:56:13,944 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:56:13,944 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:56:13,945 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:56:13,946 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:56:13,946 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:56:13,946 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13783597105
2025-07-30 10:56:13,950 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:56:13,950 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:56:13,951 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:56:13,951 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:56:13,952 - modules.friend_request_window - INFO -    📱 phone: '13783597105'
2025-07-30 10:56:13,952 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:56:13,953 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:56:14,454 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:56:14,455 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:56:14,455 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:56:14,456 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:56:14,457 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13783597105
2025-07-30 10:56:14,458 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:56:14,458 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:56:14,460 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:56:14,460 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:56:14,460 - modules.friend_request_window - INFO -    📱 手机号码: 13783597105
2025-07-30 10:56:14,461 - modules.friend_request_window - INFO -    🆔 准考证: 014325110136
2025-07-30 10:56:14,462 - modules.friend_request_window - INFO -    👤 姓名: 赵甲琪
2025-07-30 10:56:14,463 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:56:14,463 - modules.friend_request_window - INFO -    📝 备注格式: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:14,464 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:56:14,464 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:14,464 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:56:14,466 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 15467236, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:56:14,468 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 15467236)
2025-07-30 10:56:14,470 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:56:14,470 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:56:14,471 - modules.friend_request_window - INFO - 🔄 激活窗口: 15467236
2025-07-30 10:56:15,175 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:56:15,176 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:56:15,176 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:56:15,176 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:56:15,177 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:56:15,177 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:56:15,177 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:56:15,177 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:56:15,178 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:56:15,178 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:56:15,179 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:56:15,180 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:56:15,181 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:56:15,181 - modules.friend_request_window - INFO -    📝 remark参数: '014325110136-赵甲琪-2025-07-30 18:56:14' (类型: <class 'str'>, 长度: 36)
2025-07-30 10:56:15,182 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:56:15,183 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:15,183 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:56:15,183 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:56:15,184 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:56:15,184 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:56:15,184 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:56:15,185 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:56:15,185 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:56:16,099 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:56:21,345 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:56:21,345 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:56:21,346 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:56:21,346 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:56:21,348 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '...' (前50字符)
2025-07-30 10:56:21,663 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:56:21,663 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:56:22,566 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:56:22,566 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:56:22,567 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:56:22,567 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:56:22,567 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:56:23,068 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:56:23,068 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:56:23,069 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:56:23,069 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:56:23,070 - modules.friend_request_window - INFO -    📝 内容: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:23,070 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 10:56:23,071 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110136-\xe8\xb5\xb5\xe7\x94\xb2\xe7\x90\xaa-2025-07-30 18:56:14'
2025-07-30 10:56:23,071 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:56:23,983 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:56:29,226 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:56:29,226 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:56:29,226 - modules.friend_request_window - INFO -    📝 原始文本: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:29,227 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 10:56:29,227 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 10:56:29,539 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:56:29,540 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:56:30,443 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:56:30,454 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:56:30,455 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:30,455 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:56:30,456 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:30,456 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 10:56:30,957 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:30,957 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:56:30,957 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:56:30,958 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:56:30,958 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:56:30,958 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:56:30,959 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:56:31,759 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:56:31,760 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:56:31,760 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:56:32,383 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:32,383 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:56:32,384 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:56:32,384 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:56:32,902 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:33,139 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:33,371 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:33,606 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:33,838 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:34,068 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:34,308 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:34,550 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:34,784 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:35,018 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:35,251 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:35,488 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:35,723 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:36,028 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:36,302 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:36,542 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:36,775 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:37,010 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:37,244 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:56:37,464 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 10:56:37,464 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 10:56:38,465 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:56:38,468 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 10:56:38,469 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 10:56:38,469 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:56:38,469 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:56:38,469 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:56:38,470 - modules.friend_request_window - INFO -    📝 备注信息: '014325110136-赵甲琪-2025-07-30 18:56:14'
2025-07-30 10:56:38,972 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:56:38,973 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:38,973 - modules.wechat_auto_add_simple - INFO - ✅ 13783597105 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:56:38,973 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13783597105
2025-07-30 10:56:38,974 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:42,557 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2920
2025-07-30 10:56:42,557 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17659905589 (王兴磊)
2025-07-30 10:56:42,558 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:49,120 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17659905589
2025-07-30 10:56:49,120 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:56:49,121 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17659905589 执行添加朋友操作...
2025-07-30 10:56:49,121 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:56:49,121 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:56:49,122 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:56:49,123 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:56:49,128 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 10:56:49,129 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:56:49,130 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:56:49,130 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:56:49,131 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:56:49,131 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:56:49,132 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:56:49,132 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:56:49,140 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:56:49,142 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:56:49,144 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:56:49,153 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 10:56:49,156 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 10:56:49,157 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:56:49,662 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:56:49,663 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:56:49,726 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差52.41, 边缘比例0.0472
2025-07-30 10:56:49,735 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_105649.png
2025-07-30 10:56:49,737 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:56:49,738 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:56:49,741 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:56:49,744 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:56:49,745 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:56:49,752 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_105649.png
2025-07-30 10:56:49,753 - WeChatAutoAdd - INFO - 底部区域原始检测到 25 个轮廓
2025-07-30 10:56:49,755 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 10:56:49,756 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 10:56:49,757 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:56:49,758 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 10:56:49,760 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 10:56:49,762 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 10:56:49,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:56:49,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 10:56:49,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:56:49,770 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:56:49,772 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:56:49,774 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:56:49,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:56:49,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:56:49,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:56:49,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,250), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 10:56:49,783 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 10:56:49,785 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=77.6 (阈值:60)
2025-07-30 10:56:49,786 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 10:56:49,788 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=81.5 (阈值:60)
2025-07-30 10:56:49,790 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 10:56:49,791 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.7 (阈值:60)
2025-07-30 10:56:49,792 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 10:56:49,795 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 10:56:49,799 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 10:56:49,801 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,241), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:56:49,804 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,237), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:56:49,809 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,230), 尺寸14x13, 长宽比1.08, 面积182
2025-07-30 10:56:49,825 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.0 (阈值:60)
2025-07-30 10:56:49,831 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,230), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:56:49,835 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,230), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 10:56:49,836 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸42x15, 长宽比2.80, 面积630
2025-07-30 10:56:49,838 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=90.7 (阈值:60)
2025-07-30 10:56:49,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:56:49,841 - WeChatAutoAdd - INFO - 底部区域找到 4 个按钮候选
2025-07-30 10:56:49,842 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 10:56:49,845 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 10:56:49,849 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 10:56:49,857 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_105649.png
2025-07-30 10:56:49,858 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 10:56:49,859 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:56:50,161 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 10:56:50,931 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:56:50,932 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:56:50,933 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:50,933 - modules.wechat_auto_add_simple - INFO - ✅ 17659905589 添加朋友操作执行成功
2025-07-30 10:56:50,933 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:56:50,934 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:56:52,936 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:56:52,936 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:56:52,936 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:56:52,937 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:56:52,937 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:56:52,937 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:56:52,937 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:56:52,938 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:56:52,938 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:56:52,938 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17659905589
2025-07-30 10:56:52,939 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:56:52,939 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:56:52,940 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:56:52,940 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:56:52,940 - modules.friend_request_window - INFO -    📱 phone: '17659905589'
2025-07-30 10:56:52,940 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:56:52,941 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:56:53,502 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:56:53,503 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:56:53,503 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:56:53,504 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:56:53,505 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17659905589
2025-07-30 10:56:53,505 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:56:53,506 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:56:53,506 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:56:53,506 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:56:53,507 - modules.friend_request_window - INFO -    📱 手机号码: 17659905589
2025-07-30 10:56:53,507 - modules.friend_request_window - INFO -    🆔 准考证: 014325110137
2025-07-30 10:56:53,507 - modules.friend_request_window - INFO -    👤 姓名: 王兴磊
2025-07-30 10:56:53,507 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:56:53,508 - modules.friend_request_window - INFO -    📝 备注格式: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:56:53,508 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:56:53,509 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:56:53,509 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:56:53,511 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 6816934, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:56:53,513 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 6816934)
2025-07-30 10:56:53,513 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:56:53,514 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:56:53,514 - modules.friend_request_window - INFO - 🔄 激活窗口: 6816934
2025-07-30 10:56:54,217 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:56:54,218 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:56:54,218 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:56:54,219 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:56:54,219 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:56:54,219 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:56:54,219 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:56:54,219 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:56:54,220 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:56:54,220 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:56:54,220 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:56:54,220 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:56:54,221 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:56:54,221 - modules.friend_request_window - INFO -    📝 remark参数: '014325110137-王兴磊-2025-07-30 18:56:53' (类型: <class 'str'>, 长度: 36)
2025-07-30 10:56:54,221 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:56:54,222 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:56:54,222 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:56:54,222 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:56:54,222 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:56:54,223 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:56:54,223 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:56:54,223 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:56:54,224 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:56:55,130 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:57:00,373 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:57:00,374 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:57:00,374 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:57:00,374 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:57:00,375 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 10:57:00,685 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:57:00,685 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:57:01,588 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:57:01,598 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:57:01,598 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:57:01,599 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:57:01,600 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:57:01,601 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:57:02,101 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:57:02,102 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:57:02,102 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:57:02,103 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:57:02,103 - modules.friend_request_window - INFO -    📝 内容: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:57:02,103 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 10:57:02,104 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110137-\xe7\x8e\x8b\xe5\x85\xb4\xe7\xa3\x8a-2025-07-30 18:56:53'
2025-07-30 10:57:02,104 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:57:03,013 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:57:08,256 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:57:08,257 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:57:08,257 - modules.friend_request_window - INFO -    📝 原始文本: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:57:08,257 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 10:57:08,258 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 10:57:08,568 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:57:08,568 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:57:09,471 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:57:09,483 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:57:09,483 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:57:09,484 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:57:09,485 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:57:09,486 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 10:57:09,987 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:57:09,987 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:57:09,987 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:57:09,988 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:57:09,988 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:57:09,988 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:57:09,988 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:57:10,789 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:57:10,790 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:57:10,790 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:57:11,398 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:57:11,398 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:57:11,398 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:57:11,399 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:57:11,917 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:12,152 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:12,386 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:12,622 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:12,857 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:13,094 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:13,331 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:13,568 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:13,803 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:14,035 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:14,272 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:14,504 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:14,737 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:14,972 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:15,206 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:15,441 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:15,673 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:15,907 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:16,141 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:16,373 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:57:16,589 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 10:57:16,589 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 10:57:17,590 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:57:17,593 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 10:57:17,594 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 10:57:17,594 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:57:17,594 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:57:17,594 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:57:17,595 - modules.friend_request_window - INFO -    📝 备注信息: '014325110137-王兴磊-2025-07-30 18:56:53'
2025-07-30 10:57:18,095 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:57:18,096 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:57:18,097 - modules.wechat_auto_add_simple - INFO - ✅ 17659905589 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:57:18,097 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17659905589
2025-07-30 10:57:18,098 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:57:19,459 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 10:57:19,460 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 10:57:19,460 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 10:57:19,462 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 10:57:19,462 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 10:57:19,462 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 10:57:19,463 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 10:57:19,463 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 10:57:19,463 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 10:57:19,464 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 10:57:19,464 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 10:57:19,465 - __main__ - INFO - � 更新全局进度：已处理 2/2905 个联系人
2025-07-30 10:57:19,465 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 10:57:22,466 - __main__ - INFO - 
============================================================
2025-07-30 10:57:22,467 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 10:57:22,467 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 10:57:22,467 - __main__ - INFO - 📊 全局进度：已处理 2/2905 个联系人
2025-07-30 10:57:22,467 - __main__ - INFO - ============================================================
2025-07-30 10:57:22,468 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 10:57:22,468 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 10:57:22,468 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:57:22,469 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 10:57:22,469 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:57:22,791 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:57:22,792 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:57:22,792 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:57:22,792 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:57:22,793 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:57:22,793 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:57:22,793 - modules.window_manager - INFO - 📏 当前窗口位置: (15, 87), 大小: 726x650
2025-07-30 10:57:22,794 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:57:22,794 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 10:57:23,098 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-30 10:57:23,098 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:57:23,098 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-30 10:57:23,099 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-30 10:57:23,100 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-30 10:57:23,100 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:57:23,302 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:57:23,302 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:57:23,303 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 10:57:23,303 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:57:23,607 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:57:23,608 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:57:23,608 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:57:23,609 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:57:23,609 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:57:23,609 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:57:23,609 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:57:23,610 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:57:23,610 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:57:23,610 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:57:23,814 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:57:23,814 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:57:23,816 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 10:57:24,117 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:57:24,118 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:57:24,118 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:57:24,118 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:57:24,118 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:57:24,119 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:57:24,119 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:57:24,119 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:57:25,120 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:57:25,120 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 10:57:25,120 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:57:25,121 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:57:25,121 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:57:25,121 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:57:25,122 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:57:25,122 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:57:25,323 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:57:25,324 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:57:27,712 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:57:27,713 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:57:27,713 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 10:57:29,792 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:57:29,992 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:57:29,993 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:57:32,379 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:57:32,379 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:57:32,379 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 10:57:34,786 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:57:34,987 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:57:34,987 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:57:37,360 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:57:37,361 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:57:37,361 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 10:57:39,813 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:57:40,014 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:57:40,015 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:57:42,394 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:57:42,394 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:57:42,394 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-30 10:57:43,907 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:57:44,108 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:57:44,109 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:57:46,493 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:57:46,494 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:57:46,494 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:57:46,494 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:57:46,495 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:57:46,496 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:57:46,497 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:57:46,498 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 789492, 进程: Weixin.exe)
2025-07-30 10:57:46,498 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:57:46,499 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:57:46,500 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 9571230, 进程: Weixin.exe)
2025-07-30 10:57:46,503 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 10:57:46,503 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 789492)
2025-07-30 10:57:46,504 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 789492) - 增强版
2025-07-30 10:57:46,826 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:57:46,826 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:57:46,826 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:57:46,827 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:57:46,827 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x607
2025-07-30 10:57:46,827 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:57:47,032 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:57:47,033 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:57:47,234 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:57:47,235 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:57:47,235 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:57:47,235 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:57:47,236 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:57:47,236 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:57:47,236 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:57:48,237 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:57:48,237 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:57:48,239 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 789492, 进程: Weixin.exe)
2025-07-30 10:57:48,240 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:57:48,240 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:57:48,241 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:57:48,241 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:57:48,243 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 9571230, 进程: Weixin.exe)
2025-07-30 10:57:48,246 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 10:57:48,249 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:57:48,250 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:57:48,250 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:57:48,251 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 10:57:48,251 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:57:48,580 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:57:48,580 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:57:48,580 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:57:48,581 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:57:48,581 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:57:48,581 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:57:48,582 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:57:48,582 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:57:48,582 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:57:48,582 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:57:48,784 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:57:48,785 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:57:48,787 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 10:57:49,088 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:57:49,089 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:57:49,089 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:57:50,090 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:57:50,090 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 10:57:50,090 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:57:50,093 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_105750.log
2025-07-30 10:57:50,094 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:57:50,095 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:57:50,095 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:57:50,096 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 10:57:50,096 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:57:50,097 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:57:50,099 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 10:57:50,100 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 10:57:50,100 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:57:50,101 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:57:50,101 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 10:57:50,102 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 10:57:50,103 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:57:50,104 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:57:50,104 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 9571230
2025-07-30 10:57:50,105 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9571230) - 增强版
2025-07-30 10:57:50,413 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:57:50,414 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:57:50,414 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:57:50,414 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:57:50,415 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:57:50,415 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:57:50,618 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:57:50,619 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:57:50,821 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:57:50,821 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:57:50,825 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 9571230 (API返回: None)
2025-07-30 10:57:51,125 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:57:51,126 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:57:51,126 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:57:51,126 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:57:51,127 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:57:51,128 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:57:51,129 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:57:51,132 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:57:51,133 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:57:51,620 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:57:51,620 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:57:51,870 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2918 个
2025-07-30 10:57:51,871 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 10:57:51,871 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2916 个
2025-07-30 10:57:51,871 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2916 个 (总计: 3135 个)
2025-07-30 10:57:51,872 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:57:51,872 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:57:51,872 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:57:51,874 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:57:51,874 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2916
2025-07-30 10:57:51,874 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15763096192 (王宇)
2025-07-30 10:57:51,875 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:57:58,454 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15763096192
2025-07-30 10:57:58,455 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:57:58,455 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15763096192 执行添加朋友操作...
2025-07-30 10:57:58,456 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:57:58,456 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:57:58,457 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:57:58,458 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:57:58,461 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 10:57:58,464 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:57:58,464 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:57:58,465 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:57:58,467 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:57:58,467 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:57:58,468 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:57:58,469 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:57:58,474 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:57:58,478 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:57:58,480 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x607
2025-07-30 10:57:58,482 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:57:58,484 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 10:57:58,485 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 10:57:58,488 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:57:58,990 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:57:58,991 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:57:59,058 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差35.59, 边缘比例0.0365
2025-07-30 10:57:59,066 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_105759.png
2025-07-30 10:57:59,068 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:57:59,071 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:57:59,074 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:57:59,077 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:57:59,079 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:57:59,084 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_105759.png
2025-07-30 10:57:59,086 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 10:57:59,087 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 10:57:59,089 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 10:57:59,094 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:57:59,095 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 10:57:59,096 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 10:57:59,098 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 10:57:59,099 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:57:59,107 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_105759.png
2025-07-30 10:57:59,112 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:57:59,113 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 10:57:59,118 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_105759.png
2025-07-30 10:57:59,147 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:57:59,150 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 10:57:59,152 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:57:59,159 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:57:59,463 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 10:58:00,243 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:58:00,244 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:58:00,245 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:58:00,245 - modules.wechat_auto_add_simple - INFO - ✅ 15763096192 添加朋友操作执行成功
2025-07-30 10:58:00,246 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:58:00,246 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:58:02,249 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:58:02,250 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:58:02,250 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:58:02,250 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:58:02,251 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:58:02,251 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:58:02,251 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:58:02,252 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:58:02,252 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:58:02,252 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15763096192
2025-07-30 10:58:02,253 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:58:02,253 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:58:02,253 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:58:02,254 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:58:02,254 - modules.friend_request_window - INFO -    📱 phone: '15763096192'
2025-07-30 10:58:02,254 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:58:02,254 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:58:02,768 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:58:02,769 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:58:02,769 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:58:02,769 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:58:02,771 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15763096192
2025-07-30 10:58:02,771 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:58:02,772 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:58:02,773 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:58:02,773 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:58:02,774 - modules.friend_request_window - INFO -    📱 手机号码: 15763096192
2025-07-30 10:58:02,774 - modules.friend_request_window - INFO -    🆔 准考证: 014325110140
2025-07-30 10:58:02,775 - modules.friend_request_window - INFO -    👤 姓名: 王宇
2025-07-30 10:58:02,775 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:58:02,776 - modules.friend_request_window - INFO -    📝 备注格式: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:02,776 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:58:02,777 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:02,778 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:58:02,782 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5179740, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:58:02,784 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5179740)
2025-07-30 10:58:02,784 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:58:02,785 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:58:02,786 - modules.friend_request_window - INFO - 🔄 激活窗口: 5179740
2025-07-30 10:58:03,489 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:58:03,489 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:58:03,490 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:58:03,490 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:58:03,490 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:58:03,490 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:58:03,491 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:58:03,491 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:58:03,491 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:58:03,491 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:58:03,492 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:58:03,492 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:58:03,492 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:58:03,492 - modules.friend_request_window - INFO -    📝 remark参数: '014325110140-王宇-2025-07-30 18:58:02' (类型: <class 'str'>, 长度: 35)
2025-07-30 10:58:03,493 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:58:03,493 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:03,493 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:58:03,493 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:58:03,494 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:58:03,494 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:58:03,495 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:58:03,496 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:58:03,496 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:58:04,410 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:58:09,650 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:58:09,650 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:58:09,651 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:58:09,651 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:58:09,651 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 10:58:09,961 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:58:09,961 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:58:10,864 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:58:10,874 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:58:10,875 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:58:10,875 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:58:10,876 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:58:10,877 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:58:11,378 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:58:11,378 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:58:11,378 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:58:11,379 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:58:11,379 - modules.friend_request_window - INFO -    📝 内容: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:11,379 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 10:58:11,380 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110140-\xe7\x8e\x8b\xe5\xae\x87-2025-07-30 18:58:02'
2025-07-30 10:58:11,380 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:58:12,292 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:58:17,536 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:58:17,537 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:58:17,537 - modules.friend_request_window - INFO -    📝 原始文本: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:17,537 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 10:58:17,538 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 10:58:17,848 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:58:17,848 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:58:18,751 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:58:18,761 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:58:18,761 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:18,762 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:58:18,763 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:18,764 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 10:58:19,265 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:19,265 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:58:19,266 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:58:19,266 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:58:19,266 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:58:19,266 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:58:19,267 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:58:20,068 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:58:20,068 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:58:20,069 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:58:20,674 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:58:20,674 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:58:20,674 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:58:20,675 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:58:21,176 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 10:58:21,179 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 10:58:21,179 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 10:58:21,179 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 10:58:21,179 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 10:58:21,180 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 10:58:21,180 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 10:58:21,180 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 10:58:21,181 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 10:58:21,181 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:58:21,181 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 10:58:21,181 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 10:58:21,186 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 10:58:21,186 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 10:58:21,190 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 10:58:21,192 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 10:58:21,193 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:58:21,194 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 10:58:21,200 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 10:58:21,703 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:58:21,704 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 10:58:21,704 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 10:58:21,704 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 10:58:21,705 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 10:58:21,705 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 10:58:21,705 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:58:21,706 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:58:22,626 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 10:58:22,626 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 10:58:22,626 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 10:58:22,627 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 10:58:22,645 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 10:58:22,645 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 10:58:23,446 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 10:58:23,446 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 10:58:23,447 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 10:58:23,447 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 10:58:23,447 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 10:58:23,448 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 10:58:24,248 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 10:58:24,249 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 10:58:24,249 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 10:58:26,250 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 10:58:26,251 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:58:26,252 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 10:58:26,252 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:58:26,253 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 789492, 进程: Weixin.exe)
2025-07-30 10:58:26,254 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:58:26,255 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:58:26,258 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:58:26,258 - modules.friend_request_window - INFO - 🔄 使用window_manager切换到下一个微信窗口...
2025-07-30 10:58:26,260 - modules.window_manager - INFO - 📍 当前窗口: 微信 (句柄: 197994)
2025-07-30 10:58:26,260 - modules.window_manager - INFO - 🔄 切换到微信窗口 2/2: 添加朋友
2025-07-30 10:58:26,261 - modules.window_manager - INFO - 🎯 目标窗口详情: 句柄=789492, 类名=Qt51514QWindowIcon
2025-07-30 10:58:26,262 - modules.window_manager - INFO - 🚀 开始智能窗口切换: 添加朋友 (句柄: 789492)
2025-07-30 10:58:26,262 - modules.window_manager - INFO - 🔄 关闭添加朋友窗口以避免干扰
2025-07-30 10:58:26,263 - modules.window_manager - INFO - ✅ 已发送关闭命令到窗口: 789492
2025-07-30 10:58:26,616 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 10:58:26,619 - modules.window_manager - ERROR - ❌ 窗口句柄无效: 789492
2025-07-30 10:58:26,619 - modules.window_manager - WARNING - ⚠️ 窗口状态预处理失败
2025-07-30 10:58:26,620 - modules.window_manager - ERROR - ❌ 窗口句柄无效: 789492
2025-07-30 10:58:26,620 - modules.window_manager - WARNING - ⚠️ 窗口切换遇到困难，但将继续尝试
2025-07-30 10:58:26,921 - modules.window_manager - WARNING - ⚠️ 窗口基础状态异常
2025-07-30 10:58:26,921 - modules.window_manager - WARNING - ⚠️ 窗口切换验证失败，但继续执行后续操作
2025-07-30 10:58:26,922 - modules.window_manager - INFO - 📅 安排延迟新闻提示框检测: 添加朋友 (窗口 2)
2025-07-30 10:58:26,923 - modules.window_manager - WARNING - ⚠️ 窗口切换部分失败: 添加朋友
2025-07-30 10:58:26,923 - modules.window_manager - ERROR - ❌ 窗口切换失败
2025-07-30 10:58:26,924 - modules.friend_request_window - ERROR - ❌ window_manager切换窗口失败
2025-07-30 10:58:26,924 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 10:58:26,944 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 10:58:26,945 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 10:58:26,945 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 10:58:26,945 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 197994
2025-07-30 10:58:26,946 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:58:26,946 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:58:27,249 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:58:27,249 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:58:27,250 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:58:27,250 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:58:27,250 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:58:27,251 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:58:27,251 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:58:27,251 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:58:27,252 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:58:27,252 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:58:27,453 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:58:27,454 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:58:27,454 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 10:58:27,455 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 10:58:27,455 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 10:58:27,456 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 10:58:27,456 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 10:58:27,923 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 10:58:27,926 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 10:58:28,427 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 10:58:29,457 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 10:58:29,458 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:58:29,458 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:58:29,459 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 10:58:29,459 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 10:58:29,459 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 10:58:29,460 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:58:29,460 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:58:29,460 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:58:29,460 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:58:29,461 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:58:29,461 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:58:29,662 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:58:29,662 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:58:30,431 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 10:58:30,434 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 10:58:30,935 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 10:58:32,058 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:58:32,058 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:58:32,059 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 10:58:33,939 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 10:58:33,941 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 10:58:34,442 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 10:58:34,930 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:58:35,131 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:58:35,131 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:58:37,508 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:58:37,508 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:58:37,508 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 10:58:39,973 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:58:40,174 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:58:40,175 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:58:42,544 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:58:42,544 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:58:42,545 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-30 10:58:44,072 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:58:44,273 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:58:44,274 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:58:46,658 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:58:46,658 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:58:46,658 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 10:58:49,341 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:58:49,542 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:58:49,543 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:58:51,923 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:58:51,923 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:58:51,924 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:58:51,925 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:58:51,926 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:58:51,926 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:58:51,928 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:58:51,929 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:58:51,939 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 8783630, 进程: Weixin.exe)
2025-07-30 10:58:51,948 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:58:51,949 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 8783630)
2025-07-30 10:58:51,950 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 8783630) - 增强版
2025-07-30 10:58:52,254 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:58:52,255 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:58:52,255 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:58:52,255 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:58:52,256 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:58:52,256 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:58:52,459 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:58:52,460 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:58:52,661 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:58:52,662 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:58:52,662 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:58:52,662 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:58:52,663 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:58:52,663 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:58:52,663 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:58:53,664 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:58:53,665 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:58:53,665 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:58:53,667 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:58:53,668 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:58:53,669 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 8783630, 进程: Weixin.exe)
2025-07-30 10:58:53,671 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:58:53,671 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:58:53,672 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:58:53,673 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:58:53,674 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 10:58:53,676 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 10:58:53,676 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 10:58:53,678 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 10:58:53,679 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 10:58:53,679 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 10:58:53,679 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 10:58:53,680 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:58:53,680 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:58:53,680 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:58:53,680 - modules.friend_request_window - INFO -    📝 备注信息: '014325110140-王宇-2025-07-30 18:58:02'
2025-07-30 10:58:54,181 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:58:54,182 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:58:54,183 - modules.wechat_auto_add_simple - INFO - ✅ 15763096192 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:58:54,183 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15763096192
2025-07-30 10:58:54,184 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:58:57,736 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2916
2025-07-30 10:58:57,737 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13356873403 (辛奕辰)
2025-07-30 10:58:57,737 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:59:04,395 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13356873403
2025-07-30 10:59:04,395 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:59:04,395 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13356873403 执行添加朋友操作...
2025-07-30 10:59:04,396 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:59:04,396 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:59:04,397 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:59:04,398 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:59:04,402 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 10:59:04,404 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:59:04,405 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:59:04,405 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:59:04,405 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:59:04,406 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:59:04,406 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:59:04,406 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:59:04,412 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:59:04,415 - WeChatAutoAdd - DEBUG - 找到微信窗口: ● main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 10:59:04,417 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:59:04,419 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 10:59:04,423 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:59:04,926 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:59:04,928 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 328x454
2025-07-30 10:59:04,984 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差44.01, 边缘比例0.0777
2025-07-30 10:59:04,998 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_105904.png
2025-07-30 10:59:05,002 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:59:05,006 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:59:05,009 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:59:05,010 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:59:05,013 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:59:05,020 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_105905.png
2025-07-30 10:59:05,024 - WeChatAutoAdd - INFO - 底部区域原始检测到 237 个轮廓
2025-07-30 10:59:05,026 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,030 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,453), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 10:59:05,032 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,453), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 10:59:05,034 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,453), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,042 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(229,452), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:59:05,046 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,451), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 10:59:05,048 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,451), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:59:05,052 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,451), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:59:05,058 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,451), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 10:59:05,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,450), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,061 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(148,450), 尺寸6x4, 长宽比1.50, 面积24
2025-07-30 10:59:05,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,450), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,063 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,450), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,066 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,449), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 10:59:05,067 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(90,449), 尺寸5x1, 长宽比5.00, 面积5
2025-07-30 10:59:05,072 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,449), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 10:59:05,075 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,448), 尺寸7x6, 长宽比1.17, 面积42
2025-07-30 10:59:05,082 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,448), 尺寸3x6, 长宽比0.50, 面积18
2025-07-30 10:59:05,086 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,448), 尺寸14x6, 长宽比2.33, 面积84
2025-07-30 10:59:05,092 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,448), 尺寸13x6, 长宽比2.17, 面积78
2025-07-30 10:59:05,094 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,448), 尺寸4x6, 长宽比0.67, 面积24
2025-07-30 10:59:05,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,448), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:59:05,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(204,447), 尺寸27x7, 长宽比3.86, 面积189
2025-07-30 10:59:05,101 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(205,447), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 10:59:05,109 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,447), 尺寸22x7, 长宽比3.14, 面积154
2025-07-30 10:59:05,112 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,447), 尺寸23x7, 长宽比3.29, 面积161
2025-07-30 10:59:05,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,447), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 10:59:05,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,447), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 10:59:05,126 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,446), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,130 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,446), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,132 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(105,443), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,133 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,442), 尺寸9x8, 长宽比1.12, 面积72
2025-07-30 10:59:05,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(88,438), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 10:59:05,141 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(90,436), 尺寸15x18, 长宽比0.83, 面积270
2025-07-30 10:59:05,143 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,436), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,145 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(200,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:59:05,147 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(198,435), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,149 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,435), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:59:05,151 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,434), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 10:59:05,157 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,434), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,158 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(98,432), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,161 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(83,432), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 10:59:05,163 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,431), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,431), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 10:59:05,169 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,431), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,175 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(97,430), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,177 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,429), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 10:59:05,180 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(87,429), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 10:59:05,181 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,428), 尺寸4x8, 长宽比0.50, 面积32
2025-07-30 10:59:05,185 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,428), 尺寸4x8, 长宽比0.50, 面积32
2025-07-30 10:59:05,190 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(240,427), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,192 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,427), 尺寸12x10, 长宽比1.20, 面积120
2025-07-30 10:59:05,194 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,427), 尺寸20x11, 长宽比1.82, 面积220
2025-07-30 10:59:05,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,426), 尺寸6x11, 长宽比0.55, 面积66
2025-07-30 10:59:05,199 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,426), 尺寸5x11, 长宽比0.45, 面积55
2025-07-30 10:59:05,202 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,426), 尺寸9x11, 长宽比0.82, 面积99
2025-07-30 10:59:05,207 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,425), 尺寸11x13, 长宽比0.85, 面积143
2025-07-30 10:59:05,210 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,425), 尺寸23x13, 长宽比1.77, 面积299
2025-07-30 10:59:05,213 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=85.0 (阈值:60)
2025-07-30 10:59:05,215 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,425), 尺寸14x13, 长宽比1.08, 面积182
2025-07-30 10:59:05,217 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-30 10:59:05,220 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,425), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 10:59:05,225 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,423), 尺寸37x31, 长宽比1.19, 面积1147
2025-07-30 10:59:05,227 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=88.4 (阈值:60)
2025-07-30 10:59:05,229 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(303,420), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,231 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(296,420), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,232 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(298,419), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 10:59:05,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(305,418), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:59:05,237 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(303,418), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,242 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(296,418), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(293,418), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:59:05,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(296,415), 尺寸8x3, 长宽比2.67, 面积24
2025-07-30 10:59:05,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(294,413), 尺寸12x4, 长宽比3.00, 面积48
2025-07-30 10:59:05,249 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(302,408), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 10:59:05,250 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(295,408), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 10:59:05,253 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(289,402), 尺寸22x22, 长宽比1.00, 面积484
2025-07-30 10:59:05,258 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=72.5 (阈值:60)
2025-07-30 10:59:05,259 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(272,389), 尺寸56x65, 长宽比0.86, 面积3640
2025-07-30 10:59:05,261 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,389), 尺寸9x3, 长宽比3.00, 面积27
2025-07-30 10:59:05,263 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,388), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,264 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(140,388), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:59:05,266 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,387), 尺寸4x3, 长宽比1.33, 面积12
2025-07-30 10:59:05,269 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(207,387), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,274 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(183,387), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,275 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,387), 尺寸11x5, 长宽比2.20, 面积55
2025-07-30 10:59:05,277 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(142,387), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,386), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 10:59:05,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,386), 尺寸5x6, 长宽比0.83, 面积30
2025-07-30 10:59:05,283 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,385), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 10:59:05,284 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(223,385), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:59:05,290 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,383), 尺寸14x9, 长宽比1.56, 面积126
2025-07-30 10:59:05,292 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(230,383), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 10:59:05,294 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(208,382), 尺寸5x4, 长宽比1.25, 面积20
2025-07-30 10:59:05,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,382), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,297 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,382), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 10:59:05,299 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,382), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,300 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,381), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(232,381), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,381), 尺寸4x10, 长宽比0.40, 面积40
2025-07-30 10:59:05,309 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,381), 尺寸25x9, 长宽比2.78, 面积225
2025-07-30 10:59:05,311 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.9 (阈值:60)
2025-07-30 10:59:05,313 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(209,380), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,315 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(204,380), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 10:59:05,317 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,380), 尺寸17x12, 长宽比1.42, 面积204
2025-07-30 10:59:05,320 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,380), 尺寸18x11, 长宽比1.64, 面积198
2025-07-30 10:59:05,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,379), 尺寸25x13, 长宽比1.92, 面积325
2025-07-30 10:59:05,327 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,379), 尺寸26x12, 长宽比2.17, 面积312
2025-07-30 10:59:05,329 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,379), 尺寸20x8, 长宽比2.50, 面积160
2025-07-30 10:59:05,331 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(118,379), 尺寸18x12, 长宽比1.50, 面积216
2025-07-30 10:59:05,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,373), 尺寸22x22, 长宽比1.00, 面积484
2025-07-30 10:59:05,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,370), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,343 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,369), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,368), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,346 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,367), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 10:59:05,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,366), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,350 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,366), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,366), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:59:05,357 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,365), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(84,365), 尺寸14x16, 长宽比0.88, 面积224
2025-07-30 10:59:05,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,364), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(245,362), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,365 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,360), 尺寸5x6, 长宽比0.83, 面积30
2025-07-30 10:59:05,366 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,360), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 10:59:05,369 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(254,359), 尺寸5x9, 长宽比0.56, 面积45
2025-07-30 10:59:05,374 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,359), 尺寸20x10, 长宽比2.00, 面积200
2025-07-30 10:59:05,377 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(92,359), 尺寸15x22, 长宽比0.68, 面积330
2025-07-30 10:59:05,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,358), 尺寸11x7, 长宽比1.57, 面积77
2025-07-30 10:59:05,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,358), 尺寸15x22, 长宽比0.68, 面积330
2025-07-30 10:59:05,383 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,357), 尺寸28x14, 长宽比2.00, 面积392
2025-07-30 10:59:05,387 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=92.1 (阈值:60)
2025-07-30 10:59:05,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,357), 尺寸16x14, 长宽比1.14, 面积224
2025-07-30 10:59:05,394 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=75.0 (阈值:60)
2025-07-30 10:59:05,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,357), 尺寸14x14, 长宽比1.00, 面积196
2025-07-30 10:59:05,401 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=94.2 (阈值:60)
2025-07-30 10:59:05,411 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,357), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,415 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(29,338), 尺寸6x8, 长宽比0.75, 面积48
2025-07-30 10:59:05,418 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(31,337), 尺寸6x9, 长宽比0.67, 面积54
2025-07-30 10:59:05,427 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,337), 尺寸5x9, 长宽比0.56, 面积45
2025-07-30 10:59:05,431 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(30,332), 尺寸11x3, 长宽比3.67, 面积33
2025-07-30 10:59:05,433 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,332), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 10:59:05,443 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(27,325), 尺寸4x7, 长宽比0.57, 面积28
2025-07-30 10:59:05,446 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,323), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 10:59:05,448 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,322), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,450 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,320), 尺寸12x2, 长宽比6.00, 面积24
2025-07-30 10:59:05,454 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(148,320), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 10:59:05,460 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,317), 尺寸3x5, 长宽比0.60, 面积15
2025-07-30 10:59:05,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(146,317), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,464 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(148,316), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,466 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,315), 尺寸7x9, 长宽比0.78, 面积63
2025-07-30 10:59:05,469 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,314), 尺寸3x6, 长宽比0.50, 面积18
2025-07-30 10:59:05,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,314), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,478 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,314), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 10:59:05,480 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,311), 尺寸14x11, 长宽比1.27, 面积154
2025-07-30 10:59:05,482 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=311 (距底部154像素区域)
2025-07-30 10:59:05,484 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,492 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,311), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 10:59:05,494 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,311), 尺寸84x13, 长宽比6.46, 面积1092
2025-07-30 10:59:05,496 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=311 (距底部154像素区域)
2025-07-30 10:59:05,499 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,501 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,311), 尺寸5x1, 长宽比5.00, 面积5
2025-07-30 10:59:05,509 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,311), 尺寸35x13, 长宽比2.69, 面积455
2025-07-30 10:59:05,510 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=311 (距底部154像素区域)
2025-07-30 10:59:05,512 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,299), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,299), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:59:05,521 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,299), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(251,295), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,530 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(240,294), 尺寸3x5, 长宽比0.60, 面积15
2025-07-30 10:59:05,533 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,291), 尺寸4x3, 长宽比1.33, 面积12
2025-07-30 10:59:05,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,291), 尺寸27x10, 长宽比2.70, 面积270
2025-07-30 10:59:05,544 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=291 (距底部154像素区域)
2025-07-30 10:59:05,549 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,551 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,290), 尺寸5x11, 长宽比0.45, 面积55
2025-07-30 10:59:05,557 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,290), 尺寸5x11, 长宽比0.45, 面积55
2025-07-30 10:59:05,560 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,290), 尺寸6x11, 长宽比0.55, 面积66
2025-07-30 10:59:05,562 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,290), 尺寸5x11, 长宽比0.45, 面积55
2025-07-30 10:59:05,564 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,290), 尺寸9x11, 长宽比0.82, 面积99
2025-07-30 10:59:05,566 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,289), 尺寸8x13, 长宽比0.62, 面积104
2025-07-30 10:59:05,574 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,289), 尺寸23x13, 长宽比1.77, 面积299
2025-07-30 10:59:05,578 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-30 10:59:05,586 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,597 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,289), 尺寸14x13, 长宽比1.08, 面积182
2025-07-30 10:59:05,602 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=289 (距底部154像素区域)
2025-07-30 10:59:05,605 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,609 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(72,287), 尺寸38x38, 长宽比1.00, 面积1444
2025-07-30 10:59:05,612 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=287 (距底部154像素区域)
2025-07-30 10:59:05,616 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,619 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(20,279), 尺寸20x18, 长宽比1.11, 面积360
2025-07-30 10:59:05,624 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=279 (距底部154像素区域)
2025-07-30 10:59:05,627 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:59:05,633 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(139,253), 尺寸8x3, 长宽比2.67, 面积24
2025-07-30 10:59:05,643 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,253), 尺寸8x3, 长宽比2.67, 面积24
2025-07-30 10:59:05,645 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(196,252), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 10:59:05,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,252), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,650 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(148,251), 尺寸31x5, 长宽比6.20, 面积155
2025-07-30 10:59:05,655 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,658 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(147,250), 尺寸4x3, 长宽比1.33, 面积12
2025-07-30 10:59:05,660 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,663 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(199,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,664 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,249), 尺寸3x5, 长宽比0.60, 面积15
2025-07-30 10:59:05,665 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,248), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 10:59:05,668 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,248), 尺寸2x6, 长宽比0.33, 面积12
2025-07-30 10:59:05,675 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(126,248), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 10:59:05,678 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(124,248), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 10:59:05,680 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(90,248), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 10:59:05,682 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(201,247), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 10:59:05,685 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,247), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:59:05,692 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,247), 尺寸4x3, 长宽比1.33, 面积12
2025-07-30 10:59:05,694 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,247), 尺寸6x6, 长宽比1.00, 面积36
2025-07-30 10:59:05,695 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(84,247), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 10:59:05,698 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,246), 尺寸3x6, 长宽比0.50, 面积18
2025-07-30 10:59:05,700 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,246), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,709 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,245), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,712 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,245), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(148,245), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,245), 尺寸1x7, 长宽比0.14, 面积7
2025-07-30 10:59:05,726 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,245), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 10:59:05,727 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,245), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:59:05,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(191,244), 尺寸4x11, 长宽比0.36, 面积44
2025-07-30 10:59:05,733 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,244), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 10:59:05,735 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,243), 尺寸32x13, 长宽比2.46, 面积416
2025-07-30 10:59:05,742 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(217,243), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 10:59:05,743 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,243), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 10:59:05,745 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,243), 尺寸25x13, 长宽比1.92, 面积325
2025-07-30 10:59:05,748 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,243), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 10:59:05,750 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,243), 尺寸8x10, 长宽比0.80, 面积80
2025-07-30 10:59:05,757 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,243), 尺寸6x4, 长宽比1.50, 面积24
2025-07-30 10:59:05,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,243), 尺寸8x1, 长宽比8.00, 面积8
2025-07-30 10:59:05,764 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,243), 尺寸12x13, 长宽比0.92, 面积156
2025-07-30 10:59:05,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(93,243), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 10:59:05,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(77,243), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:59:05,781 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,243), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,784 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(75,241), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,792 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,238), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 10:59:05,794 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(91,237), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,795 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(218,231), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:59:05,798 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(213,231), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:59:05,799 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,230), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:59:05,808 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(19,229), 尺寸22x22, 长宽比1.00, 面积484
2025-07-30 10:59:05,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(251,227), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 10:59:05,812 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,226), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,814 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(240,226), 尺寸3x5, 长宽比0.60, 面积15
2025-07-30 10:59:05,818 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(78,226), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:59:05,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,225), 尺寸7x31, 长宽比0.23, 面积217
2025-07-30 10:59:05,826 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,223), 尺寸6x10, 长宽比0.60, 面积60
2025-07-30 10:59:05,828 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,223), 尺寸4x3, 长宽比1.33, 面积12
2025-07-30 10:59:05,830 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(233,223), 尺寸21x10, 长宽比2.10, 面积210
2025-07-30 10:59:05,834 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,222), 尺寸5x11, 长宽比0.45, 面积55
2025-07-30 10:59:05,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,222), 尺寸6x11, 长宽比0.55, 面积66
2025-07-30 10:59:05,842 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,222), 尺寸5x11, 长宽比0.45, 面积55
2025-07-30 10:59:05,844 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,222), 尺寸9x11, 长宽比0.82, 面积99
2025-07-30 10:59:05,846 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(79,222), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 10:59:05,849 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,221), 尺寸9x13, 长宽比0.69, 面积117
2025-07-30 10:59:05,851 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,221), 尺寸8x13, 长宽比0.62, 面积104
2025-07-30 10:59:05,858 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(145,221), 尺寸23x13, 长宽比1.77, 面积299
2025-07-30 10:59:05,860 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=85.0 (阈值:60)
2025-07-30 10:59:05,862 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(119,221), 尺寸14x13, 长宽比1.08, 面积182
2025-07-30 10:59:05,865 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=78.1 (阈值:60)
2025-07-30 10:59:05,867 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,219), 尺寸37x37, 长宽比1.00, 面积1369
2025-07-30 10:59:05,875 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=60.6 (阈值:60)
2025-07-30 10:59:05,877 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(272,209), 尺寸56x179, 长宽比0.31, 面积10024
2025-07-30 10:59:05,880 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(59,209), 尺寸212x245, 长宽比0.87, 面积51940
2025-07-30 10:59:05,882 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸1x245, 长宽比0.00, 面积245
2025-07-30 10:59:05,884 - WeChatAutoAdd - INFO - 底部区域找到 25 个按钮候选
2025-07-30 10:59:05,889 - WeChatAutoAdd - INFO - 选择特殊位置按钮: Y=287 (距底部154像素)
2025-07-30 10:59:05,892 - WeChatAutoAdd - INFO - 在底部找到按钮: (91, 306), 尺寸: 38x38, 位置得分: 3.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 10:59:05,895 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:59:05,907 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_105905.png
2025-07-30 10:59:05,911 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:59:05,912 - WeChatAutoAdd - INFO - 开始验证按钮区域(91, 306)是否包含'添加到通讯录'文字
2025-07-30 10:59:05,917 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_105905.png
2025-07-30 10:59:05,944 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:59:05,946 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0503, 平均亮度=203.9, 亮度标准差=72.1
2025-07-30 10:59:05,948 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:59:05,950 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:59:06,254 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(91, 306) -> 屏幕坐标(91, 306)
2025-07-30 10:59:07,039 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:59:07,040 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:59:07,042 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:59:07,042 - modules.wechat_auto_add_simple - INFO - ✅ 13356873403 添加朋友操作执行成功
2025-07-30 10:59:07,042 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:59:07,043 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:59:09,044 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:59:09,045 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:59:09,045 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:59:09,045 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:59:09,046 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:59:09,046 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:59:09,047 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:59:09,047 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:59:09,047 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:59:09,048 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13356873403
2025-07-30 10:59:09,049 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:59:09,050 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:59:09,051 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:59:09,054 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:59:09,055 - modules.friend_request_window - INFO -    📱 phone: '13356873403'
2025-07-30 10:59:09,056 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:59:09,059 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
