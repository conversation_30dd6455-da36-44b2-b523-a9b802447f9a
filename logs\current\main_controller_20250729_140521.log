2025-07-29 14:05:21,873 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:05:21,874 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:05:21,874 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 14:05:21,875 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:05:21,876 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 14:05:21,876 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:05:21,876 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:05:21,877 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:05:21,877 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:05:21,877 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:05:21,878 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:05:21,883 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:05:21,888 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_140521.log
2025-07-29 14:05:21,890 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:05:21,891 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:05:21,892 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:05:21,892 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 14:05:21,893 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 14:05:21,894 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 22:05:21
2025-07-29 14:05:21,895 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 14:05:21,897 - __main__ - INFO - 📅 启动时间: 2025-07-29 22:05:21
2025-07-29 14:05:21,899 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 14:05:21,901 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:05:22,452 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:05:22,452 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:05:22,988 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:05:22,988 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:05:22,992 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 14:05:22,993 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 14:05:22,993 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 14:05:22,993 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 14:05:22,994 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 14:05:24,450 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 14:05:24,451 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 14:05:24,451 - __main__ - INFO - 📋 待处理联系人数: 2956
2025-07-29 14:05:24,452 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 14:05:24,452 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2956
2025-07-29 14:05:24,452 - __main__ - INFO - 
============================================================
2025-07-29 14:05:24,453 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-29 14:05:24,453 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:05:24,453 - __main__ - INFO - 📊 全局进度：已处理 0/2956 个联系人
2025-07-29 14:05:24,454 - __main__ - INFO - ============================================================
2025-07-29 14:05:24,454 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 14:05:24,454 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:05:24,455 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:05:24,455 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 14:05:24,455 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:05:24,779 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:05:24,780 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:05:24,780 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:05:24,781 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:05:24,781 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:05:24,781 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:05:24,782 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:05:24,782 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:05:24,782 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:05:24,782 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:05:24,984 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:05:24,984 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:05:24,984 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:05:24,984 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:05:25,290 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:05:25,291 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:05:25,291 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:05:25,292 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:05:25,292 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:05:25,293 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:05:25,293 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:05:25,293 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:05:25,294 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:05:25,294 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:05:25,495 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:05:25,496 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:05:25,498 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:05:25,798 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:05:25,799 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:05:25,799 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:05:25,800 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:05:25,800 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:05:25,800 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:05:25,801 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:05:25,802 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:05:26,807 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:05:26,875 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 14:05:26,888 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:05:26,977 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:05:27,030 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:05:27,062 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:05:27,067 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:05:27,084 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:05:27,305 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:05:27,306 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:05:29,697 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:05:29,697 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:05:29,698 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 14:05:31,957 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:05:32,197 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:05:32,197 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:05:34,567 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:05:34,569 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:05:34,570 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 14:05:36,841 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:05:37,041 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:05:37,042 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:05:39,425 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:05:39,425 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:05:39,426 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-29 14:05:42,072 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:05:42,273 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:05:42,273 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:05:44,659 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:05:44,659 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:05:44,660 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-29 14:05:46,566 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:05:46,767 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:05:46,769 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:05:49,142 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:05:49,143 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:05:49,143 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:05:49,143 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:05:49,144 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:05:49,145 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:05:49,146 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:05:49,146 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:05:49,147 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:05:49,147 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148862, 进程: Weixin.exe)
2025-07-29 14:05:49,150 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:05:49,150 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3148862)
2025-07-29 14:05:49,151 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148862) - 增强版
2025-07-29 14:05:49,454 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:05:49,455 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:05:49,455 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:05:49,456 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:05:49,456 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:05:49,456 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:05:49,661 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:05:49,661 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:05:49,863 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:05:49,863 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:05:49,864 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:05:49,864 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:05:49,864 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:05:49,865 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:05:49,865 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:05:50,866 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:05:50,866 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:05:50,867 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:05:50,868 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:05:50,868 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:05:50,869 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:05:50,870 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3148862, 进程: Weixin.exe)
2025-07-29 14:05:50,873 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:05:50,873 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:05:50,874 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:05:50,875 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:05:50,875 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:05:50,875 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:05:51,183 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:05:51,183 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:05:51,184 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:05:51,184 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:05:51,184 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:05:51,184 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:05:51,185 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:05:51,185 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:05:51,185 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:05:51,186 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:05:51,387 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:05:51,388 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:05:51,390 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:05:51,690 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:05:51,691 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:05:51,691 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:05:52,692 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:05:52,692 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 14:05:52,693 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:05:52,695 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_140552.log
2025-07-29 14:05:52,696 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:05:52,697 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:05:52,697 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:05:52,698 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-29 14:05:52,698 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:05:52,699 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:05:52,700 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 14:05:52,701 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 14:05:52,701 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 14:05:52,701 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:05:52,702 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 14:05:52,702 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 14:05:52,703 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:05:52,704 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:05:52,705 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3148862
2025-07-29 14:05:52,706 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3148862) - 增强版
2025-07-29 14:05:53,013 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:05:53,014 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:05:53,014 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:05:53,015 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:05:53,015 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:05:53,015 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:05:53,016 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:05:53,016 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:05:53,218 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:05:53,218 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:05:53,221 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3148862 (API返回: None)
2025-07-29 14:05:53,522 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:05:53,523 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:05:53,523 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:05:53,523 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:05:53,524 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:05:53,525 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:05:53,525 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:05:53,530 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:05:53,530 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:05:54,196 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:05:54,197 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:05:54,513 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2956 个
2025-07-29 14:05:54,514 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2956 个 (总计: 3135 个)
2025-07-29 14:05:54,514 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:05:54,514 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:05:54,515 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:05:54,515 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:05:54,516 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2956
2025-07-29 14:05:54,516 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15927081081 (王晓轩)
2025-07-29 14:05:54,517 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:01,118 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15927081081
2025-07-29 14:06:01,118 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:06:01,118 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15927081081 执行添加朋友操作...
2025-07-29 14:06:01,119 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:06:01,119 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:06:01,121 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:06:01,121 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:06:01,126 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:06:01,127 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:06:01,128 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:06:01,129 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:06:01,129 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:06:01,130 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:06:01,130 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:06:01,130 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:06:01,138 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:06:01,141 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:06:01,143 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:06:01,144 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:06:01,149 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 14:06:01,151 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:06:01,653 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:06:01,655 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:06:01,726 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差50.59, 边缘比例0.0429
2025-07-29 14:06:01,734 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_140601.png
2025-07-29 14:06:01,735 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:06:01,738 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:06:01,740 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:06:01,741 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:06:01,743 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:06:01,751 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_140601.png
2025-07-29 14:06:01,753 - WeChatAutoAdd - INFO - 底部区域原始检测到 29 个轮廓
2025-07-29 14:06:01,756 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-29 14:06:01,758 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-29 14:06:01,759 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-29 14:06:01,760 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-29 14:06:01,764 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-29 14:06:01,765 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-29 14:06:01,766 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:06:01,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-29 14:06:01,768 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:06:01,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:06:01,774 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:06:01,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-29 14:06:01,782 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=68.5 (阈值:60)
2025-07-29 14:06:01,784 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸34x35, 长宽比0.97, 面积1190
2025-07-29 14:06:01,786 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-29 14:06:01,792 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-29 14:06:01,794 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-29 14:06:01,794 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,795 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,237), 尺寸4x1, 长宽比4.00, 面积4
2025-07-29 14:06:01,796 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,235), 尺寸3x1, 长宽比3.00, 面积3
2025-07-29 14:06:01,798 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,235), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,799 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(140,234), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,800 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,234), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,800 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,234), 尺寸4x6, 长宽比0.67, 面积24
2025-07-29 14:06:01,801 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,234), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:06:01,802 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,234), 尺寸4x6, 长宽比0.67, 面积24
2025-07-29 14:06:01,807 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,233), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:06:01,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(155,230), 尺寸6x4, 长宽比1.50, 面积24
2025-07-29 14:06:01,812 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸42x14, 长宽比3.00, 面积588
2025-07-29 14:06:01,813 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=89.5 (阈值:60)
2025-07-29 14:06:01,814 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:06:01,817 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-07-29 14:06:01,819 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-29 14:06:01,825 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-29 14:06:01,827 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-29 14:06:01,838 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_140601.png
2025-07-29 14:06:01,844 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-29 14:06:01,847 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:06:02,148 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-29 14:06:02,947 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:06:02,948 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:06:02,949 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:02,949 - modules.wechat_auto_add_simple - INFO - ✅ 15927081081 添加朋友操作执行成功
2025-07-29 14:06:02,950 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:02,950 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:06:04,951 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:06:04,952 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:06:04,952 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:06:04,953 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:06:04,953 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:06:04,954 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:06:04,954 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:06:04,956 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:06:04,958 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:06:04,961 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15927081081
2025-07-29 14:06:04,967 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:06:04,967 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:06:04,967 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:06:04,968 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:06:04,968 - modules.friend_request_window - INFO -    📱 phone: '15927081081'
2025-07-29 14:06:04,968 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:06:04,969 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:06:05,855 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:06:05,856 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:06:05,857 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:06:05,858 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:06:05,860 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15927081081
2025-07-29 14:06:05,860 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:06:05,861 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:06:05,864 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:06:05,865 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:06:05,865 - modules.friend_request_window - INFO -    📱 手机号码: 15927081081
2025-07-29 14:06:05,866 - modules.friend_request_window - INFO -    🆔 准考证: 014325110092
2025-07-29 14:06:05,867 - modules.friend_request_window - INFO -    👤 姓名: 王晓轩
2025-07-29 14:06:05,869 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:06:05,869 - modules.friend_request_window - INFO -    📝 备注格式: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:05,871 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:06:05,872 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:05,873 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:06:05,874 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4787404, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:06:05,880 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4787404)
2025-07-29 14:06:05,881 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:06:05,881 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:06:05,882 - modules.friend_request_window - INFO - 🔄 激活窗口: 4787404
2025-07-29 14:06:06,587 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:06:06,588 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:06:06,588 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:06:06,589 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:06:06,589 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:06:06,589 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:06:06,589 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:06:06,590 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:06:06,590 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:06:06,590 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:06:06,590 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:06:06,591 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:06:06,591 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:06:06,591 - modules.friend_request_window - INFO -    📝 remark参数: '014325110092-王晓轩-2025-07-29 22:06:05' (类型: <class 'str'>, 长度: 36)
2025-07-29 14:06:06,591 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:06:06,592 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:06,592 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:06:06,592 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:06:06,593 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:06:06,593 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:06:06,593 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:06:06,594 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:06:06,595 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:06:07,506 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:06:12,753 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:06:12,754 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:06:12,754 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:06:12,755 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:06:12,757 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:06:13,068 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:06:13,069 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:06:13,972 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:06:13,980 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:06:13,980 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:06:13,982 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:06:13,982 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:06:13,983 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:06:14,484 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:06:14,484 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:06:14,484 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:06:14,485 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:06:14,485 - modules.friend_request_window - INFO -    📝 内容: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:14,485 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 14:06:14,485 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110092-\xe7\x8e\x8b\xe6\x99\x93\xe8\xbd\xa9-2025-07-29 22:06:05'
2025-07-29 14:06:14,486 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:06:15,409 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:06:20,654 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:06:20,655 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:06:20,655 - modules.friend_request_window - INFO -    📝 原始文本: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:20,655 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 14:06:20,656 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:06:20,964 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:06:20,964 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:06:21,866 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:06:21,876 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:06:21,876 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:21,877 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:06:21,878 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:21,878 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 14:06:22,379 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:22,379 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:06:22,379 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:06:22,380 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:06:22,380 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:06:22,380 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:06:22,380 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:06:23,181 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:06:23,182 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:06:23,182 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:06:23,791 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:23,791 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:06:23,792 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:06:23,792 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:06:24,313 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:24,548 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:24,782 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:25,038 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:25,295 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:25,534 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:25,766 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:25,999 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:26,230 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:26,464 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:26,698 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:26,933 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:27,165 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:27,397 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:27,630 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:27,864 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:28,096 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:28,332 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:28,566 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:28,797 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:06:29,017 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 14:06:29,018 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 14:06:30,018 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:06:30,023 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:06:30,023 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 14:06:30,023 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:06:30,023 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:06:30,024 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:06:30,024 - modules.friend_request_window - INFO -    📝 备注信息: '014325110092-王晓轩-2025-07-29 22:06:05'
2025-07-29 14:06:30,525 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:06:30,526 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:30,527 - modules.wechat_auto_add_simple - INFO - ✅ 15927081081 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:06:30,527 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15927081081
2025-07-29 14:06:30,528 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:34,200 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2956
2025-07-29 14:06:34,201 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15311594643 (申强)
2025-07-29 14:06:34,201 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:41,096 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15311594643
2025-07-29 14:06:41,096 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:06:41,097 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15311594643 执行添加朋友操作...
2025-07-29 14:06:41,097 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:06:41,098 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:06:41,099 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:06:41,100 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:06:41,106 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-29 14:06:41,114 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:06:41,115 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:06:41,115 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:06:41,116 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:06:41,116 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:06:41,117 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:06:41,117 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:06:41,123 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:06:41,126 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:06:41,127 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:06:41,129 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:06:41,131 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 14:06:41,141 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:06:41,644 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:06:41,646 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:06:41,697 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-29 14:06:41,698 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-29 14:06:41,705 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_140641.png
2025-07-29 14:06:41,707 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:06:41,711 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:06:41,716 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:06:41,718 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:06:41,721 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:06:41,726 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_140641.png
2025-07-29 14:06:41,728 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:06:41,732 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:06:41,734 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:06:41,736 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:06:41,738 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:06:41,741 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:06:41,743 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:06:41,744 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:06:41,752 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_140641.png
2025-07-29 14:06:41,755 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:06:41,757 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:06:41,761 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_140641.png
2025-07-29 14:06:41,820 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:06:41,821 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:06:41,822 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:06:41,823 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:06:42,125 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:06:42,893 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:06:42,894 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:06:42,896 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:42,896 - modules.wechat_auto_add_simple - INFO - ✅ 15311594643 添加朋友操作执行成功
2025-07-29 14:06:42,896 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:06:42,897 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:06:44,899 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:06:44,899 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:06:44,900 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:06:44,902 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:06:44,903 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:06:44,903 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:06:44,904 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:06:44,906 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:06:44,910 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:06:44,910 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15311594643
2025-07-29 14:06:44,914 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:06:44,914 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:06:44,915 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:06:44,916 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:06:44,917 - modules.friend_request_window - INFO -    📱 phone: '15311594643'
2025-07-29 14:06:44,921 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:06:44,923 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:06:45,598 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:06:45,598 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:06:45,599 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:06:45,599 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:06:45,601 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15311594643
2025-07-29 14:06:45,602 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:06:45,603 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:06:45,603 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:06:45,604 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:06:45,604 - modules.friend_request_window - INFO -    📱 手机号码: 15311594643
2025-07-29 14:06:45,605 - modules.friend_request_window - INFO -    🆔 准考证: 014325110094
2025-07-29 14:06:45,605 - modules.friend_request_window - INFO -    👤 姓名: 申强
2025-07-29 14:06:45,605 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:06:45,606 - modules.friend_request_window - INFO -    📝 备注格式: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:06:45,606 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:06:45,606 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:06:45,607 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:06:45,608 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1182788, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:06:45,609 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1182788)
2025-07-29 14:06:45,609 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:06:45,609 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:06:45,610 - modules.friend_request_window - INFO - 🔄 激活窗口: 1182788
2025-07-29 14:06:46,313 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:06:46,314 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:06:46,314 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:06:46,315 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:06:46,315 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:06:46,315 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:06:46,315 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:06:46,316 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:06:46,316 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:06:46,316 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:06:46,316 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:06:46,317 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:06:46,317 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:06:46,317 - modules.friend_request_window - INFO -    📝 remark参数: '014325110094-申强-2025-07-29 22:06:45' (类型: <class 'str'>, 长度: 35)
2025-07-29 14:06:46,319 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:06:46,320 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:06:46,322 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:06:46,322 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:06:46,323 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:06:46,323 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:06:46,323 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:06:46,323 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:06:46,324 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:06:47,239 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:06:52,491 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:06:52,491 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:06:52,492 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:06:52,492 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:06:52,493 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:06:52,803 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:06:52,803 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:06:53,706 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:06:53,717 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:06:53,718 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:06:53,719 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:06:53,722 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:06:53,722 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:06:54,223 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:06:54,223 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:06:54,224 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:06:54,224 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:06:54,224 - modules.friend_request_window - INFO -    📝 内容: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:06:54,225 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 14:06:54,225 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110094-\xe7\x94\xb3\xe5\xbc\xba-2025-07-29 22:06:45'
2025-07-29 14:06:54,226 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:06:55,137 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:07:00,431 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:07:00,432 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:07:00,432 - modules.friend_request_window - INFO -    📝 原始文本: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:07:00,432 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 14:07:00,433 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:07:00,743 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:07:00,743 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:07:01,645 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:07:01,655 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:07:01,655 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:07:01,656 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:07:01,657 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:07:01,657 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 14:07:02,157 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:07:02,158 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:07:02,158 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:07:02,158 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:07:02,159 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:07:02,159 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:07:02,159 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:07:02,960 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:07:02,960 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:07:02,961 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:07:03,582 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:07:03,583 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:07:03,584 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:07:03,584 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:07:04,085 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 14:07:04,088 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 14:07:04,088 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 14:07:04,088 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 14:07:04,089 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 14:07:04,089 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 14:07:04,089 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 14:07:04,090 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 14:07:04,090 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 14:07:04,090 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:07:04,090 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 14:07:04,091 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 14:07:04,092 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 14:07:04,092 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 14:07:04,093 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 14:07:04,093 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 14:07:04,093 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:07:04,093 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-29 14:07:04,094 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-29 14:07:04,595 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 14:07:04,596 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 14:07:04,596 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 14:07:04,596 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 14:07:04,597 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 14:07:04,597 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 14:07:04,597 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 14:07:04,597 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 14:07:05,506 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 14:07:05,506 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 14:07:05,507 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 14:07:05,507 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-29 14:07:05,507 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 14:07:06,008 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 14:07:07,123 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 14:07:07,123 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-29 14:07:07,124 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-29 14:07:07,624 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-29 14:07:08,738 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-29 14:07:08,738 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 14:07:08,754 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-29 14:07:08,755 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-29 14:07:08,755 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-29 14:07:08,755 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-29 14:07:08,757 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-29 14:07:08,757 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:07:08,757 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:07:08,758 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:07:08,759 - modules.friend_request_window - INFO -    📝 备注信息: '014325110094-申强-2025-07-29 22:06:45'
2025-07-29 14:07:09,260 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:07:09,261 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:07:09,261 - modules.wechat_auto_add_simple - INFO - ✅ 15311594643 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:07:09,261 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15311594643
2025-07-29 14:07:09,262 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:07:10,442 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:07:10,442 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:07:10,443 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:07:10,444 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:07:10,444 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:07:10,445 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:07:10,446 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:07:10,446 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:07:10,447 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:07:10,447 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 14:07:10,448 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:07:10,449 - __main__ - INFO - � 更新全局进度：已处理 2/2956 个联系人
2025-07-29 14:07:10,451 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:07:13,452 - __main__ - INFO - 
============================================================
2025-07-29 14:07:13,452 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-29 14:07:13,453 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:07:13,453 - __main__ - INFO - 📊 全局进度：已处理 2/2956 个联系人
2025-07-29 14:07:13,453 - __main__ - INFO - ============================================================
2025-07-29 14:07:13,454 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 14:07:13,454 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:07:13,454 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:07:13,454 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 14:07:13,455 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:07:13,758 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:07:13,758 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:07:13,759 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:07:13,759 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:07:13,759 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:07:13,760 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:07:13,760 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:07:13,760 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:07:13,761 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:07:13,761 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:07:13,963 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:07:13,964 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:07:13,964 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:07:13,964 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:07:14,269 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:07:14,269 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:07:14,270 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:07:14,270 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:07:14,270 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:07:14,270 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:07:14,271 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:07:14,271 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:07:14,271 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:07:14,271 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:07:14,472 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:07:14,479 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:07:14,484 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:07:14,799 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:07:14,800 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:07:14,800 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:07:14,800 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:07:14,801 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:07:14,801 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:07:14,801 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:07:14,801 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:07:15,802 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:07:15,802 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 14:07:15,803 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:07:15,803 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:07:15,803 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:07:15,804 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:07:15,804 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:07:15,804 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:07:16,005 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:07:16,005 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:07:18,387 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:07:18,387 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:07:18,388 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-29 14:07:21,120 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:07:21,321 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:07:21,322 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:07:23,690 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:07:23,691 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:07:23,691 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-29 14:07:25,971 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:07:26,172 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:07:26,172 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:07:28,541 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:07:28,541 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:07:28,542 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-29 14:07:30,736 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:07:30,937 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:07:30,937 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:07:33,319 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:07:33,319 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:07:33,320 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-29 14:07:35,481 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:07:35,682 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:07:35,683 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:07:38,085 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:07:38,085 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:07:38,085 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:07:38,086 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:07:38,086 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:07:38,087 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:07:38,088 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:07:38,089 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:07:38,090 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 14:07:38,091 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 592826)
2025-07-29 14:07:38,091 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 592826) - 增强版
2025-07-29 14:07:38,394 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:07:38,395 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:07:38,395 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:07:38,395 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:07:38,396 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:07:38,396 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:07:38,599 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:07:38,600 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:07:38,802 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:07:38,802 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:07:38,803 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:07:38,803 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:07:38,803 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:07:38,803 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:07:38,804 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:07:39,804 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:07:39,805 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:07:39,806 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:07:39,807 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:07:39,808 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:07:39,809 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 14:07:39,810 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:07:39,810 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:07:39,811 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:07:39,812 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:07:39,812 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:07:40,121 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:07:40,121 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:07:40,122 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:07:40,122 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:07:40,123 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:07:40,123 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:07:40,123 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:07:40,124 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:07:40,125 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:07:40,126 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:07:40,329 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:07:40,329 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:07:40,330 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:07:40,632 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:07:40,632 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:07:40,632 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:07:41,633 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:07:41,633 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-29 14:07:41,634 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:07:41,637 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_140741.log
2025-07-29 14:07:41,638 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:07:41,638 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:07:41,638 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:07:41,638 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-29 14:07:41,639 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:07:41,639 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:07:41,641 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-29 14:07:41,641 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 14:07:41,642 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-29 14:07:41,643 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:07:41,643 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 14:07:41,643 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:07:41,645 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:07:41,646 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 592826
2025-07-29 14:07:41,646 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 592826) - 增强版
2025-07-29 14:07:41,955 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:07:41,955 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:07:41,956 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:07:41,956 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:07:41,957 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:07:41,957 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:07:41,957 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:07:41,958 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:07:42,158 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:07:42,159 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:07:42,161 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 592826 (API返回: None)
2025-07-29 14:07:42,462 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:07:42,552 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:07:42,644 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:07:42,663 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:07:42,693 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:07:42,707 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:07:42,712 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:07:42,723 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:07:42,724 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:07:43,225 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:07:43,226 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:07:43,803 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2954 个
2025-07-29 14:07:43,807 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-29 14:07:43,840 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2952 个
2025-07-29 14:07:43,841 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2952 个 (总计: 3135 个)
2025-07-29 14:07:43,842 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:07:43,842 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:07:43,843 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:07:43,843 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:07:43,845 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2952
2025-07-29 14:07:43,860 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17643505908 (董源臣)
2025-07-29 14:07:43,861 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:07:50,523 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17643505908
2025-07-29 14:07:50,527 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:07:50,527 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17643505908 执行添加朋友操作...
2025-07-29 14:07:50,528 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:07:50,529 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:07:50,530 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:07:50,535 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:07:50,540 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:07:50,543 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:07:50,543 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:07:50,544 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:07:50,566 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:07:50,568 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:07:50,569 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:07:50,569 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:07:50,574 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:07:50,576 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:07:50,578 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:07:50,591 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-29 14:07:50,592 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:07:51,096 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:07:51,100 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:07:51,170 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.29, 边缘比例0.0350
2025-07-29 14:07:51,177 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_140751.png
2025-07-29 14:07:51,181 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:07:51,183 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:07:51,188 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:07:51,189 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:07:51,191 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:07:51,197 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_140751.png
2025-07-29 14:07:51,201 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:07:51,202 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:07:51,204 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:07:51,206 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:07:51,207 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:07:51,209 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:07:51,213 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:07:51,218 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:07:51,226 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_140751.png
2025-07-29 14:07:51,227 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:07:51,228 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:07:51,232 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_140751.png
2025-07-29 14:07:51,256 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:07:51,261 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:07:51,270 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:07:51,273 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:07:51,576 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:07:52,346 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:07:52,350 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:07:52,357 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:07:52,357 - modules.wechat_auto_add_simple - INFO - ✅ 17643505908 添加朋友操作执行成功
2025-07-29 14:07:52,358 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:07:52,359 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:07:54,361 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:07:54,361 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:07:54,362 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:07:54,362 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:07:54,362 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:07:54,363 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:07:54,363 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:07:54,364 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:07:54,365 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:07:54,365 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17643505908
2025-07-29 14:07:54,366 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:07:54,366 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:07:54,366 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:07:54,366 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:07:54,367 - modules.friend_request_window - INFO -    📱 phone: '17643505908'
2025-07-29 14:07:54,367 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:07:54,368 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:07:54,776 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:07:54,777 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:07:54,777 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:07:54,777 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:07:54,779 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17643505908
2025-07-29 14:07:54,779 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:07:54,779 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:07:54,780 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:07:54,782 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:07:54,783 - modules.friend_request_window - INFO -    📱 手机号码: 17643505908
2025-07-29 14:07:54,783 - modules.friend_request_window - INFO -    🆔 准考证: 014325110097
2025-07-29 14:07:54,784 - modules.friend_request_window - INFO -    👤 姓名: 董源臣
2025-07-29 14:07:54,784 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:07:54,784 - modules.friend_request_window - INFO -    📝 备注格式: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:07:54,785 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:07:54,785 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:07:54,786 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:07:54,787 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1641666, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:07:54,790 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1641666)
2025-07-29 14:07:54,791 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:07:54,791 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:07:54,791 - modules.friend_request_window - INFO - 🔄 激活窗口: 1641666
2025-07-29 14:07:55,494 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:07:55,494 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:07:55,495 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:07:55,495 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:07:55,495 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:07:55,495 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:07:55,496 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:07:55,496 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:07:55,496 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:07:55,497 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:07:55,498 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:07:55,498 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:07:55,499 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:07:55,499 - modules.friend_request_window - INFO -    📝 remark参数: '014325110097-董源臣-2025-07-29 22:07:54' (类型: <class 'str'>, 长度: 36)
2025-07-29 14:07:55,499 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:07:55,500 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:07:55,500 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:07:55,501 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:07:55,501 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:07:55,501 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:07:55,502 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:07:55,502 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:07:55,502 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:07:56,419 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:08:01,844 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:08:02,053 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:08:02,089 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:08:02,112 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:08:02,122 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:08:02,433 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:08:02,433 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:08:03,336 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:08:03,345 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:08:03,345 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:08:03,346 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:08:03,346 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:08:03,347 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:08:03,849 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:08:03,849 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:08:03,850 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:08:03,850 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:08:03,850 - modules.friend_request_window - INFO -    📝 内容: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:08:03,851 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 14:08:03,852 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110097-\xe8\x91\xa3\xe6\xba\x90\xe8\x87\xa3-2025-07-29 22:07:54'
2025-07-29 14:08:03,852 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:08:04,767 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:08:10,010 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:08:10,011 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:08:10,011 - modules.friend_request_window - INFO -    📝 原始文本: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:08:10,011 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 14:08:10,012 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:08:10,330 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:08:10,334 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:08:11,281 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:08:11,294 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:08:11,295 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:08:11,296 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:08:11,297 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:08:11,297 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 14:08:11,798 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:08:11,799 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:08:11,799 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:08:11,799 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:08:11,800 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:08:11,800 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:08:11,800 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:08:12,601 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:08:12,601 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:08:12,602 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:08:13,215 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:13,216 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:08:13,216 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:08:13,216 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:08:13,735 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:13,971 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:14,205 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:14,440 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:14,675 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:14,909 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:15,142 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:15,374 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:15,609 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:15,841 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:16,075 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:16,305 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:16,541 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:16,774 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:17,011 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:17,245 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:17,479 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:17,714 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:17,948 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:18,181 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:08:18,398 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 14:08:18,398 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 14:08:19,399 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:08:19,402 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:08:19,403 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 14:08:19,403 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:08:19,404 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:08:19,404 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:08:19,404 - modules.friend_request_window - INFO -    📝 备注信息: '014325110097-董源臣-2025-07-29 22:07:54'
2025-07-29 14:08:19,905 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:08:19,906 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:19,907 - modules.wechat_auto_add_simple - INFO - ✅ 17643505908 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:08:19,907 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17643505908
2025-07-29 14:08:19,908 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:23,389 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2952
2025-07-29 14:08:23,390 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17607138119 (蔡天翊)
2025-07-29 14:08:23,390 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:30,108 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17607138119
2025-07-29 14:08:30,109 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:08:30,109 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17607138119 执行添加朋友操作...
2025-07-29 14:08:30,110 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:08:30,110 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:08:30,111 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:08:30,113 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:08:30,117 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:08:30,119 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:08:30,119 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:08:30,120 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:08:30,120 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:08:30,120 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:08:30,121 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:08:30,121 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:08:30,126 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:08:30,131 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:08:30,135 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:08:30,139 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-29 14:08:30,142 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:08:30,644 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:08:30,646 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:08:30,711 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.34, 边缘比例0.0348
2025-07-29 14:08:30,719 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_140830.png
2025-07-29 14:08:30,721 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:08:30,722 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:08:30,724 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:08:30,725 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:08:30,726 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:08:30,733 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_140830.png
2025-07-29 14:08:30,736 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 14:08:30,737 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:08:30,738 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:08:30,739 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:08:30,741 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 14:08:30,743 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 14:08:30,747 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:08:30,751 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 14:08:30,753 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:08:30,754 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 14:08:30,756 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 14:08:30,757 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 14:08:30,758 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:08:30,760 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:08:30,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:08:30,766 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:08:30,768 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:08:30,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:08:30,771 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:08:30,772 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:08:30,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 14:08:30,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 14:08:30,777 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 14:08:30,780 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:08:30,783 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 14:08:30,785 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 14:08:30,786 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 14:08:30,788 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 14:08:30,792 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 14:08:30,803 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 14:08:30,808 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 14:08:30,816 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 14:08:30,827 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 14:08:30,836 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 14:08:30,844 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 14:08:30,863 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 14:08:30,869 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 14:08:30,877 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 14:08:30,884 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:08:30,889 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 14:08:30,892 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 14:08:30,894 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:08:30,898 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:08:30,909 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_140830.png
2025-07-29 14:08:30,911 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:08:30,914 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 14:08:30,921 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_140830.png
2025-07-29 14:08:30,943 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:08:30,948 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 14:08:30,951 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:08:30,952 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:08:31,254 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 14:08:32,022 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:08:32,023 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:08:32,024 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:32,025 - modules.wechat_auto_add_simple - INFO - ✅ 17607138119 添加朋友操作执行成功
2025-07-29 14:08:32,025 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:32,025 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:08:34,027 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:08:34,027 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:08:34,028 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:08:34,029 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:08:34,029 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:08:34,029 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:08:34,030 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:08:34,030 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:08:34,030 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:08:34,031 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17607138119
2025-07-29 14:08:34,032 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:08:34,032 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:08:34,032 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:08:34,033 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:08:34,034 - modules.friend_request_window - INFO -    📱 phone: '17607138119'
2025-07-29 14:08:34,034 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:08:34,035 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:08:34,413 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:08:34,414 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:08:34,414 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:08:34,414 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:08:34,415 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17607138119
2025-07-29 14:08:34,416 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:08:34,416 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:08:34,417 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:08:34,417 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:08:34,417 - modules.friend_request_window - INFO -    📱 手机号码: 17607138119
2025-07-29 14:08:34,417 - modules.friend_request_window - INFO -    🆔 准考证: 014325110098
2025-07-29 14:08:34,418 - modules.friend_request_window - INFO -    👤 姓名: 蔡天翊
2025-07-29 14:08:34,418 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:08:34,418 - modules.friend_request_window - INFO -    📝 备注格式: '014325110098-蔡天翊-2025-07-29 22:08:34'
2025-07-29 14:08:34,418 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:08:34,419 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110098-蔡天翊-2025-07-29 22:08:34'
2025-07-29 14:08:34,419 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:08:34,422 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:08:34,423 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 14:08:34,424 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:34,424 - modules.wechat_auto_add_simple - INFO - ✅ 17607138119 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 14:08:34,425 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17607138119
2025-07-29 14:08:34,431 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:08:35,479 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:08:35,480 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:08:35,480 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:08:35,481 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:08:35,481 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:08:35,482 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:08:35,482 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:08:35,482 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:08:35,482 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:08:35,483 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-29 14:08:35,483 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:08:35,484 - __main__ - INFO - � 更新全局进度：已处理 4/2956 个联系人
2025-07-29 14:08:35,484 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:08:38,485 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-29 14:08:38,485 - __main__ - INFO - 📊 当前进度：已处理 4/2956 个联系人
2025-07-29 14:08:38,485 - __main__ - INFO - 
============================================================
2025-07-29 14:08:38,485 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 2 轮)
2025-07-29 14:08:38,486 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:08:38,486 - __main__ - INFO - 📊 全局进度：已处理 4/2956 个联系人
2025-07-29 14:08:38,486 - __main__ - INFO - ============================================================
2025-07-29 14:08:38,487 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 14:08:38,487 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:08:38,487 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:08:38,487 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 14:08:38,488 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:08:38,488 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-29 14:08:39,011 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:08:39,012 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:08:39,012 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:08:39,013 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:08:39,013 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:08:39,013 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:08:39,014 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:08:39,014 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:08:39,014 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:08:39,014 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:08:39,216 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:08:39,216 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:08:39,216 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:08:39,217 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:08:39,520 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:08:39,520 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:08:39,521 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:08:39,521 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:08:39,521 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:08:39,522 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:08:39,522 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:08:39,523 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:08:39,523 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:08:39,523 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:08:39,726 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:08:39,727 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:08:39,728 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:08:40,029 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:08:40,069 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:08:40,070 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:08:40,072 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:08:40,072 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:08:40,073 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:08:40,074 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:08:40,074 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:08:41,075 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:08:41,076 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 14:08:41,076 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:08:41,076 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:08:41,077 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:08:41,077 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:08:41,078 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:08:41,078 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:08:41,279 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:08:41,279 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:08:43,664 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:08:43,665 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:08:43,665 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 14:08:45,217 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:08:45,418 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:08:45,419 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:08:47,786 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:08:47,786 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:08:47,787 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 14:08:49,607 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:08:49,808 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:08:49,809 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:08:52,213 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:08:52,214 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:08:52,214 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-29 14:08:53,922 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:08:54,123 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:08:54,124 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:08:56,490 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:08:56,491 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:08:56,491 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 14:08:58,136 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:08:58,377 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:08:58,378 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:09:00,763 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:09:00,764 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:09:00,764 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:09:00,764 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:09:00,765 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:09:00,766 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:09:00,767 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:09:00,767 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:09:00,768 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:09:00,769 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:09:00,770 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1707202, 进程: Weixin.exe)
2025-07-29 14:09:00,774 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 14:09:00,774 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 592826)
2025-07-29 14:09:00,774 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 592826) - 增强版
2025-07-29 14:09:01,097 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:09:01,097 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:09:01,098 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:09:01,098 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:09:01,099 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-29 14:09:01,099 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:09:01,303 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:09:01,304 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:09:01,505 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:09:01,506 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:09:01,506 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:09:01,506 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:09:01,506 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:09:01,507 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:09:01,507 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:09:02,508 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:09:02,508 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:09:02,510 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:09:02,510 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:09:02,511 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:09:02,512 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:09:02,512 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:09:02,515 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1707202, 进程: Weixin.exe)
2025-07-29 14:09:02,518 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 14:09:02,519 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:09:02,520 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:09:02,521 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:09:02,521 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:09:02,522 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:09:02,847 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:09:02,847 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:09:02,848 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:09:02,848 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:09:02,849 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:09:02,850 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:09:02,850 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:09:02,851 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:09:02,852 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:09:02,853 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:09:03,055 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:09:03,056 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:09:03,058 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:09:03,359 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:09:03,360 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:09:03,361 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:09:04,361 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:09:04,362 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 14:09:04,362 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:09:04,364 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_140904.log
2025-07-29 14:09:04,365 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:09:04,366 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:09:04,366 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:09:04,366 - __main__ - INFO - 🔄 传递全局联系人索引: 4
2025-07-29 14:09:04,367 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:09:04,367 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:09:04,371 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-29 14:09:04,373 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-29 14:09:04,374 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 14:09:04,375 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:09:04,375 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 14:09:04,376 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-29 14:09:04,377 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-29 14:09:04,378 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:09:04,380 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:09:04,380 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1707202
2025-07-29 14:09:04,381 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1707202) - 增强版
2025-07-29 14:09:04,689 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:09:04,689 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:09:04,690 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:09:04,690 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:09:04,691 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:09:04,691 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:09:04,894 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:09:04,902 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:09:05,103 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:09:05,105 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:09:05,107 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1707202 (API返回: None)
2025-07-29 14:09:05,408 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:09:05,408 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:09:05,408 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:09:05,409 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:09:05,410 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:09:05,410 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:09:05,410 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:09:05,416 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:09:05,416 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:09:05,786 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:09:05,786 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:09:06,048 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2952 个
2025-07-29 14:09:06,049 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 5 个联系人开始处理
2025-07-29 14:09:06,049 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2948 个
2025-07-29 14:09:06,050 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2948 个 (总计: 3135 个)
2025-07-29 14:09:06,050 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:09:06,051 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:09:06,051 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:06,051 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:09:06,052 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2948
2025-07-29 14:09:06,052 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15072214799 (张曼)
2025-07-29 14:09:06,052 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:12,624 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15072214799
2025-07-29 14:09:12,625 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:09:12,625 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15072214799 执行添加朋友操作...
2025-07-29 14:09:12,625 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:09:12,626 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:09:12,626 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:09:12,628 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:09:12,633 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:09:12,636 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:09:12,636 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:09:12,636 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:09:12,637 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:09:12,637 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:09:12,638 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:09:12,639 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:09:12,644 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:09:12,648 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:09:12,650 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:09:12,653 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:09:12,655 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:09:12,656 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 14:09:12,662 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:09:13,166 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:09:13,168 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:09:13,234 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-29 14:09:13,236 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-29 14:09:13,248 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_140913.png
2025-07-29 14:09:13,251 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:09:13,254 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:09:13,256 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:09:13,258 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:09:13,263 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:09:13,269 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_140913.png
2025-07-29 14:09:13,271 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:09:13,272 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:09:13,274 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:09:13,279 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:09:13,282 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:09:13,283 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:09:13,284 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:09:13,288 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:09:13,297 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_140913.png
2025-07-29 14:09:13,300 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:09:13,301 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:09:13,305 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_140913.png
2025-07-29 14:09:13,331 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:09:13,334 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:09:13,336 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:09:13,339 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:09:13,642 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:09:14,411 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:09:14,413 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:09:14,415 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:14,415 - modules.wechat_auto_add_simple - INFO - ✅ 15072214799 添加朋友操作执行成功
2025-07-29 14:09:14,416 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:14,416 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:09:16,417 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:09:16,418 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:09:16,418 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:09:16,418 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:09:16,419 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:09:16,419 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:09:16,419 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:09:16,420 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:09:16,420 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:09:16,420 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15072214799
2025-07-29 14:09:16,421 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:09:16,421 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:09:16,421 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:09:16,421 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:09:16,422 - modules.friend_request_window - INFO -    📱 phone: '15072214799'
2025-07-29 14:09:16,422 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:09:16,422 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:09:16,827 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:09:16,827 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:09:16,828 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:09:16,828 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:09:16,830 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15072214799
2025-07-29 14:09:16,830 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:09:16,831 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:09:16,832 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:09:16,834 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:09:16,835 - modules.friend_request_window - INFO -    📱 手机号码: 15072214799
2025-07-29 14:09:16,836 - modules.friend_request_window - INFO -    🆔 准考证: 014325110101
2025-07-29 14:09:16,838 - modules.friend_request_window - INFO -    👤 姓名: 张曼
2025-07-29 14:09:16,839 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:09:16,840 - modules.friend_request_window - INFO -    📝 备注格式: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:16,841 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:09:16,841 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:16,843 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:09:16,848 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5311692, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:09:16,849 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5311692)
2025-07-29 14:09:16,849 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:09:16,850 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:09:16,850 - modules.friend_request_window - INFO - 🔄 激活窗口: 5311692
2025-07-29 14:09:17,553 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:09:17,554 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:09:17,554 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:09:17,555 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:09:17,555 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:09:17,555 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:09:17,556 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:09:17,556 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:09:17,556 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:09:17,556 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:09:17,557 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:09:17,557 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:09:17,557 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:09:17,558 - modules.friend_request_window - INFO -    📝 remark参数: '014325110101-张曼-2025-07-29 22:09:16' (类型: <class 'str'>, 长度: 35)
2025-07-29 14:09:17,558 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:09:17,559 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:17,561 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:09:17,563 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:09:17,563 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:09:17,564 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:09:17,566 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:09:17,567 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:09:17,568 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:09:18,480 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:09:23,738 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:09:23,738 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:09:23,738 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:09:23,739 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:09:23,739 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:09:24,048 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:09:24,049 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:09:24,952 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:09:24,962 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:09:24,962 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:09:24,963 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:09:24,965 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:09:24,966 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:09:25,467 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:09:25,468 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:09:25,468 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:09:25,468 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:09:25,468 - modules.friend_request_window - INFO -    📝 内容: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:25,469 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 14:09:25,469 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110101-\xe5\xbc\xa0\xe6\x9b\xbc-2025-07-29 22:09:16'
2025-07-29 14:09:25,469 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:09:26,378 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:09:31,653 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:09:31,653 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:09:31,654 - modules.friend_request_window - INFO -    📝 原始文本: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:31,654 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 14:09:31,654 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:09:31,965 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:09:31,966 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:09:32,869 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:09:32,880 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:09:32,880 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:32,880 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:09:32,881 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:32,882 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 14:09:33,382 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:33,383 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:09:33,383 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:09:33,383 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:09:33,384 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:09:33,384 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:09:33,384 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:09:34,185 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:09:34,185 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:09:34,186 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:09:34,796 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:34,796 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:09:34,796 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:09:34,797 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:09:35,298 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 14:09:35,300 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 14:09:35,301 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 14:09:35,301 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 14:09:35,301 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 14:09:35,301 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 14:09:35,302 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 14:09:35,302 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 14:09:35,302 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 14:09:35,303 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:09:35,303 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 14:09:35,303 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 14:09:35,303 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 14:09:35,304 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 14:09:35,304 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 14:09:35,305 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 14:09:35,305 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:09:35,306 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-29 14:09:35,306 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-29 14:09:35,809 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 14:09:35,810 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 14:09:35,810 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 14:09:35,810 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 14:09:35,810 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 14:09:35,811 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 14:09:35,811 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 14:09:35,811 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 14:09:36,729 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 14:09:36,729 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 14:09:36,729 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 14:09:36,730 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-29 14:09:36,730 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 14:09:37,230 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 14:09:38,344 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 14:09:38,344 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-29 14:09:38,345 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-29 14:09:38,846 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-29 14:09:39,961 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-29 14:09:39,961 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 14:09:39,984 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-29 14:09:39,988 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-29 14:09:39,989 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-29 14:09:39,989 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-29 14:09:39,999 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-29 14:09:40,004 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:09:40,004 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:09:40,005 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:09:40,006 - modules.friend_request_window - INFO -    📝 备注信息: '014325110101-张曼-2025-07-29 22:09:16'
2025-07-29 14:09:40,517 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:09:40,565 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:40,575 - modules.wechat_auto_add_simple - INFO - ✅ 15072214799 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:09:40,576 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15072214799
2025-07-29 14:09:40,577 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:43,962 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2948
2025-07-29 14:09:43,962 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18186117327 (常志鹏)
2025-07-29 14:09:43,962 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:50,625 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18186117327
2025-07-29 14:09:50,625 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:09:50,626 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18186117327 执行添加朋友操作...
2025-07-29 14:09:50,626 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:09:50,626 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:09:50,627 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:09:50,629 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:09:50,633 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:09:50,636 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:09:50,637 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:09:50,638 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:09:50,638 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:09:50,639 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:09:50,640 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:09:50,641 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:09:50,647 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:09:50,650 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:09:50,652 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:09:50,655 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:09:50,660 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 14:09:50,663 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:09:51,170 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:09:51,172 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 328x454
2025-07-29 14:09:51,239 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差24.36, 边缘比例0.0285
2025-07-29 14:09:51,247 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_140951.png
2025-07-29 14:09:51,250 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:09:51,254 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:09:51,256 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:09:51,262 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:09:51,264 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:09:51,272 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_140951.png
2025-07-29 14:09:51,279 - WeChatAutoAdd - INFO - 底部区域原始检测到 39 个轮廓
2025-07-29 14:09:51,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸1x3, 长宽比0.33, 面积3
2025-07-29 14:09:51,285 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:09:51,287 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:09:51,291 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:09:51,296 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 14:09:51,298 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 14:09:51,300 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:09:51,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 14:09:51,303 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:09:51,305 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 14:09:51,310 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 14:09:51,312 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 14:09:51,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:09:51,316 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:09:51,318 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:09:51,319 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:09:51,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:09:51,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:09:51,332 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:09:51,334 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:09:51,337 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 14:09:51,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 14:09:51,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 14:09:51,349 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:09:51,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 14:09:51,354 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 14:09:51,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 14:09:51,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 14:09:51,365 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 14:09:51,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 14:09:51,370 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 14:09:51,378 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 14:09:51,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 14:09:51,394 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 14:09:51,397 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 14:09:51,401 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 14:09:51,543 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 14:09:51,555 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 14:09:51,564 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:09:51,577 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 14:09:51,585 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 14:09:51,591 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:09:51,594 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:09:51,606 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_140951.png
2025-07-29 14:09:51,611 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:09:51,618 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 14:09:51,625 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_140951.png
2025-07-29 14:09:51,652 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:09:51,654 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 14:09:51,656 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:09:51,661 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:09:51,963 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(189, 250)
2025-07-29 14:09:52,783 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:09:52,785 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:09:52,787 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:52,787 - modules.wechat_auto_add_simple - INFO - ✅ 18186117327 添加朋友操作执行成功
2025-07-29 14:09:52,787 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:52,787 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:09:54,789 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:09:54,790 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:09:54,790 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:09:54,790 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:09:54,791 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:09:54,791 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:09:54,791 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:09:54,792 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:09:54,792 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:09:54,792 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18186117327
2025-07-29 14:09:54,793 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:09:54,793 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:09:54,793 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:09:54,794 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:09:54,794 - modules.friend_request_window - INFO -    📱 phone: '18186117327'
2025-07-29 14:09:54,794 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:09:54,794 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:09:55,175 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:09:55,175 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:09:55,175 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:09:55,176 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:09:55,177 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18186117327
2025-07-29 14:09:55,177 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:09:55,178 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:09:55,178 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:09:55,178 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:09:55,179 - modules.friend_request_window - INFO -    📱 手机号码: 18186117327
2025-07-29 14:09:55,179 - modules.friend_request_window - INFO -    🆔 准考证: 014325110102
2025-07-29 14:09:55,179 - modules.friend_request_window - INFO -    👤 姓名: 常志鹏
2025-07-29 14:09:55,179 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:09:55,180 - modules.friend_request_window - INFO -    📝 备注格式: '014325110102-常志鹏-2025-07-29 22:09:55'
2025-07-29 14:09:55,180 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:09:55,181 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110102-常志鹏-2025-07-29 22:09:55'
2025-07-29 14:09:55,181 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:09:55,185 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:09:55,185 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 14:09:55,186 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:55,187 - modules.wechat_auto_add_simple - INFO - ✅ 18186117327 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 14:09:55,187 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18186117327
2025-07-29 14:09:55,188 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:09:56,221 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:09:56,221 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:09:56,222 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:09:56,223 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:09:56,224 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:09:56,224 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:09:56,224 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:09:56,224 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:09:56,225 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:09:56,225 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 14:09:56,225 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:09:56,226 - __main__ - INFO - � 更新全局进度：已处理 6/2956 个联系人
2025-07-29 14:09:56,226 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:09:59,227 - __main__ - INFO - 
============================================================
2025-07-29 14:09:59,227 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 2 轮)
2025-07-29 14:09:59,227 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:09:59,228 - __main__ - INFO - 📊 全局进度：已处理 6/2956 个联系人
2025-07-29 14:09:59,228 - __main__ - INFO - ============================================================
2025-07-29 14:09:59,228 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 14:09:59,228 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:09:59,229 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:09:59,229 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 14:09:59,229 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:09:59,540 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:09:59,541 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:09:59,543 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:09:59,543 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:09:59,544 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:09:59,544 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:09:59,545 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:09:59,545 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:09:59,545 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:09:59,546 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:09:59,747 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:09:59,748 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:09:59,748 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:09:59,748 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:10:00,051 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:10:00,052 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:10:00,052 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:10:00,053 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:10:00,053 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:10:00,053 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:10:00,054 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:10:00,054 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:10:00,054 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:10:00,055 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:10:00,256 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:10:00,257 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:10:00,258 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:10:00,559 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:10:00,560 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:10:00,560 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:10:00,560 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:10:00,561 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:10:00,561 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:10:00,561 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:10:00,561 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:10:01,562 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:10:01,563 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 14:10:01,563 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:10:01,563 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:10:01,564 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:10:01,564 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:10:01,564 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:10:01,564 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:10:01,765 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:10:01,766 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:10:04,143 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:10:04,143 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:10:04,143 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-29 14:10:05,974 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:10:06,175 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:10:06,176 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:10:08,560 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:10:08,560 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:10:08,560 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-29 14:10:11,091 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:10:11,292 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:10:11,293 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:10:13,676 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:10:13,676 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:10:13,677 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-29 14:10:16,032 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:10:16,233 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:10:16,234 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:10:18,608 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:10:18,609 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:10:18,609 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 14:10:20,243 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:10:20,444 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:10:20,445 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:10:22,826 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:10:22,827 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:10:22,827 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:10:22,828 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:10:22,828 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:10:22,829 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:10:22,830 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:10:22,832 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:10:22,833 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:10:22,834 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:10:22,836 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:10:22,836 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 592826)
2025-07-29 14:10:22,837 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 592826) - 增强版
2025-07-29 14:10:23,140 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:10:23,141 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:10:23,141 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:10:23,142 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:10:23,142 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:10:23,142 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:10:23,143 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:10:23,143 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:10:23,345 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:10:23,345 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:10:23,345 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:10:23,346 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:10:23,346 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:10:23,346 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:10:23,347 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:10:24,369 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:10:24,467 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:10:24,530 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:10:24,618 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:10:24,642 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:10:24,643 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:10:24,643 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:10:24,646 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 14:10:24,647 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:10:24,647 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:10:24,647 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:10:24,648 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:10:24,648 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:10:24,957 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:10:24,959 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:10:24,959 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:10:24,959 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:10:24,960 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:10:24,960 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:10:24,961 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:10:24,961 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:10:24,962 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:10:24,962 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:10:25,164 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:10:25,164 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:10:25,165 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:10:25,466 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:10:25,467 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:10:25,467 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:10:26,468 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:10:26,468 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-29 14:10:26,469 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:10:26,472 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_141026.log
2025-07-29 14:10:26,473 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:10:26,473 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:10:26,474 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:10:26,474 - __main__ - INFO - 🔄 传递全局联系人索引: 6
2025-07-29 14:10:26,474 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:10:26,475 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:10:26,477 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 14:10:26,477 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 14:10:26,477 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 14:10:26,478 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:10:26,478 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 14:10:26,480 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-29 14:10:26,481 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:10:26,483 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:10:26,484 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 592826
2025-07-29 14:10:26,484 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 592826) - 增强版
2025-07-29 14:10:26,800 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:10:26,800 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:10:26,800 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:10:26,801 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:10:26,801 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 14:10:26,801 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:10:26,802 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 14:10:26,802 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:10:27,004 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:10:27,005 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:10:27,007 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 592826 (API返回: None)
2025-07-29 14:10:27,308 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:10:27,308 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:10:27,308 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:10:27,309 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:10:27,310 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:10:27,310 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:10:27,310 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:10:27,315 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:10:27,315 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:10:27,736 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:10:27,736 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:10:27,991 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2950 个
2025-07-29 14:10:27,991 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 7 个联系人开始处理
2025-07-29 14:10:27,992 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2944 个
2025-07-29 14:10:27,992 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2944 个 (总计: 3135 个)
2025-07-29 14:10:27,992 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:10:27,993 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:10:27,993 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:10:27,993 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:10:27,994 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2944
2025-07-29 14:10:27,994 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13376515990 (朱志豪)
2025-07-29 14:10:27,994 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:10:34,568 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13376515990
2025-07-29 14:10:34,569 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:10:34,569 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13376515990 执行添加朋友操作...
2025-07-29 14:10:34,569 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:10:34,569 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:10:34,570 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:10:34,572 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:10:34,578 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:10:34,581 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:10:34,582 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:10:34,582 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:10:34,583 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:10:34,583 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:10:34,586 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:10:34,586 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:10:34,598 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:10:34,731 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:10:34,736 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:10:34,766 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:10:34,769 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 14:10:34,775 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:10:35,278 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:10:35,281 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:10:35,338 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差34.34, 边缘比例0.0340
2025-07-29 14:10:35,348 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_141035.png
2025-07-29 14:10:35,353 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:10:35,359 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:10:35,362 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:10:35,364 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:10:35,367 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:10:35,372 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_141035.png
2025-07-29 14:10:35,377 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:10:35,378 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:10:35,381 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:10:35,383 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:10:35,385 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:10:35,392 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:10:35,394 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:10:35,397 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:10:35,407 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_141035.png
2025-07-29 14:10:35,411 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:10:35,413 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:10:35,418 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_141035.png
2025-07-29 14:10:35,443 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:10:35,446 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:10:35,448 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:10:35,451 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:10:35,754 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:10:36,523 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:10:36,525 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:10:36,527 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:10:36,528 - modules.wechat_auto_add_simple - INFO - ✅ 13376515990 添加朋友操作执行成功
2025-07-29 14:10:36,528 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:10:36,528 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:10:38,530 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:10:38,531 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:10:38,531 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:10:38,531 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:10:38,531 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:10:38,532 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:10:38,532 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:10:38,533 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:10:38,533 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:10:38,533 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13376515990
2025-07-29 14:10:38,534 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:10:38,534 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:10:38,535 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:10:38,535 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:10:38,535 - modules.friend_request_window - INFO -    📱 phone: '13376515990'
2025-07-29 14:10:38,535 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:10:38,536 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:10:38,908 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:10:38,909 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:10:38,909 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:10:38,910 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:10:38,911 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13376515990
2025-07-29 14:10:38,912 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:10:38,912 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:10:38,913 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:10:38,913 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:10:38,913 - modules.friend_request_window - INFO -    📱 手机号码: 13376515990
2025-07-29 14:10:38,914 - modules.friend_request_window - INFO -    🆔 准考证: 014325110105
2025-07-29 14:10:38,916 - modules.friend_request_window - INFO -    👤 姓名: 朱志豪
2025-07-29 14:10:38,916 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:10:38,917 - modules.friend_request_window - INFO -    📝 备注格式: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:38,917 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:10:38,918 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:38,921 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:10:38,923 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2033920, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:10:38,927 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2033920)
2025-07-29 14:10:38,928 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:10:38,928 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:10:38,929 - modules.friend_request_window - INFO - 🔄 激活窗口: 2033920
2025-07-29 14:10:39,633 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:10:39,633 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:10:39,634 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:10:39,634 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:10:39,634 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:10:39,634 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:10:39,635 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:10:39,635 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:10:39,635 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:10:39,635 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:10:39,636 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:10:39,636 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:10:39,636 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:10:39,637 - modules.friend_request_window - INFO -    📝 remark参数: '014325110105-朱志豪-2025-07-29 22:10:38' (类型: <class 'str'>, 长度: 36)
2025-07-29 14:10:39,637 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:10:39,637 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:39,637 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:10:39,638 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:10:39,638 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:10:39,639 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:10:39,639 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:10:39,640 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:10:39,640 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:10:40,561 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:10:45,806 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:10:45,806 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:10:45,807 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:10:45,807 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:10:45,807 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:10:46,117 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:10:46,117 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:10:47,020 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:10:47,029 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:10:47,029 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:10:47,030 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:10:47,030 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:10:47,031 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:10:47,532 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:10:47,532 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:10:47,532 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:10:47,533 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:10:47,533 - modules.friend_request_window - INFO -    📝 内容: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:47,533 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 14:10:47,533 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110105-\xe6\x9c\xb1\xe5\xbf\x97\xe8\xb1\xaa-2025-07-29 22:10:38'
2025-07-29 14:10:47,534 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:10:48,439 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:10:53,721 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:10:53,722 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:10:53,722 - modules.friend_request_window - INFO -    📝 原始文本: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:53,722 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 14:10:53,723 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:10:54,032 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:10:54,033 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:10:54,936 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:10:54,945 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:10:54,946 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:54,947 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:10:54,948 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:54,948 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 14:10:55,449 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:10:55,450 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:10:55,450 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:10:55,451 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:10:55,451 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:10:55,451 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:10:55,452 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:10:56,253 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:10:56,253 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:10:56,253 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:10:56,874 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:10:56,874 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:10:56,875 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:10:56,875 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:10:57,393 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:57,626 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:57,858 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:58,093 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:58,324 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:58,562 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:58,794 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:59,030 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:59,269 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:59,501 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:59,739 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:10:59,971 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:00,203 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:00,431 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:00,668 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:00,903 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:01,137 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:01,372 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:01,605 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:01,842 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:02,059 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 14:11:02,059 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 14:11:03,060 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:11:03,062 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:11:03,063 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 14:11:03,063 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:11:03,063 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:11:03,063 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:11:03,064 - modules.friend_request_window - INFO -    📝 备注信息: '014325110105-朱志豪-2025-07-29 22:10:38'
2025-07-29 14:11:03,564 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:11:03,565 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:03,565 - modules.wechat_auto_add_simple - INFO - ✅ 13376515990 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:11:03,566 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13376515990
2025-07-29 14:11:03,567 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:06,813 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2944
2025-07-29 14:11:06,813 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18038026737 (黄鹤)
2025-07-29 14:11:06,814 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:13,383 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18038026737
2025-07-29 14:11:13,383 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:11:13,384 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18038026737 执行添加朋友操作...
2025-07-29 14:11:13,384 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:11:13,384 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:11:13,385 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:11:13,387 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:11:13,392 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:11:13,398 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:11:13,398 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:11:13,398 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:11:13,399 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:11:13,400 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:11:13,401 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:11:13,401 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:11:13,410 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:11:13,413 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:11:13,415 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:11:13,419 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:11:13,426 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 14:11:13,430 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:11:13,935 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:11:13,937 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:11:14,002 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差45.56, 边缘比例0.0362
2025-07-29 14:11:14,010 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_141114.png
2025-07-29 14:11:14,013 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:11:14,015 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:11:14,019 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:11:14,023 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:11:14,026 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:11:14,032 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_141114.png
2025-07-29 14:11:14,037 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:11:14,042 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:11:14,047 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:11:14,055 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:11:14,057 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:11:14,063 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:11:14,067 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:11:14,073 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:11:14,085 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_141114.png
2025-07-29 14:11:14,093 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:11:14,095 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:11:14,102 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_141114.png
2025-07-29 14:11:14,126 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:11:14,130 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:11:14,132 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:11:14,136 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:11:14,442 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:11:15,211 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:11:15,213 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:11:15,215 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:15,216 - modules.wechat_auto_add_simple - INFO - ✅ 18038026737 添加朋友操作执行成功
2025-07-29 14:11:15,216 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:15,216 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:11:17,220 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:11:17,221 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:11:17,221 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:11:17,221 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:11:17,222 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:11:17,222 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:11:17,222 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:11:17,222 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:11:17,223 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:11:17,223 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18038026737
2025-07-29 14:11:17,224 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:11:17,224 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:11:17,225 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:11:17,225 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:11:17,225 - modules.friend_request_window - INFO -    📱 phone: '18038026737'
2025-07-29 14:11:17,225 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:11:17,226 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:11:17,604 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:11:17,604 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:11:17,604 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:11:17,605 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:11:17,606 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18038026737
2025-07-29 14:11:17,606 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:11:17,607 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:11:17,607 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:11:17,607 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:11:17,607 - modules.friend_request_window - INFO -    📱 手机号码: 18038026737
2025-07-29 14:11:17,608 - modules.friend_request_window - INFO -    🆔 准考证: 014325110106
2025-07-29 14:11:17,608 - modules.friend_request_window - INFO -    👤 姓名: 黄鹤
2025-07-29 14:11:17,608 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:11:17,609 - modules.friend_request_window - INFO -    📝 备注格式: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:17,609 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:11:17,610 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:17,610 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:11:17,612 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1707076, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:11:17,614 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1707076)
2025-07-29 14:11:17,615 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:11:17,617 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:11:17,617 - modules.friend_request_window - INFO - 🔄 激活窗口: 1707076
2025-07-29 14:11:18,321 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:11:18,322 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:11:18,322 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:11:18,323 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:11:18,323 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:11:18,323 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:11:18,323 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:11:18,324 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:11:18,324 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:11:18,324 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:11:18,324 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:11:18,325 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:11:18,325 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:11:18,325 - modules.friend_request_window - INFO -    📝 remark参数: '014325110106-黄鹤-2025-07-29 22:11:17' (类型: <class 'str'>, 长度: 35)
2025-07-29 14:11:18,325 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:11:18,326 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:18,326 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:11:18,326 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:11:18,326 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:11:18,326 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:11:18,327 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:11:18,327 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:11:18,328 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:11:19,239 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:11:24,500 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:11:24,500 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:11:24,500 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:11:24,501 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:11:24,502 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:11:24,813 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:11:24,813 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:11:25,715 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:11:25,724 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:11:25,725 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:11:25,725 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:11:25,726 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:11:25,727 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:11:26,228 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:11:26,228 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:11:26,229 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:11:26,229 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:11:26,229 - modules.friend_request_window - INFO -    📝 内容: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:26,229 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 14:11:26,230 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110106-\xe9\xbb\x84\xe9\xb9\xa4-2025-07-29 22:11:17'
2025-07-29 14:11:26,230 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:11:27,136 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:11:32,379 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:11:32,379 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:11:32,379 - modules.friend_request_window - INFO -    📝 原始文本: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:32,380 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 14:11:32,380 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:11:32,691 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:11:32,691 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:11:33,594 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:11:33,603 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:11:33,604 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:33,605 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:11:33,605 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:33,606 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 14:11:34,106 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:34,107 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:11:34,107 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:11:34,107 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:11:34,108 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:11:34,108 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:11:34,108 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:11:34,909 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:11:34,909 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:11:34,909 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:11:35,520 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:35,520 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:11:35,520 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:11:35,521 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:11:36,038 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:36,281 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:36,516 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:36,749 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:36,982 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:37,216 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:37,451 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:37,691 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:37,923 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:38,155 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:38,386 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:38,618 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:38,850 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:39,082 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:39,313 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:39,550 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:39,783 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:40,016 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:40,250 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:40,486 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 14:11:40,704 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 14:11:40,704 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 14:11:41,705 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:11:41,708 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:11:41,709 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 14:11:41,709 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:11:41,709 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:11:41,710 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:11:41,710 - modules.friend_request_window - INFO -    📝 备注信息: '014325110106-黄鹤-2025-07-29 22:11:17'
2025-07-29 14:11:42,210 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:11:42,211 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:42,212 - modules.wechat_auto_add_simple - INFO - ✅ 18038026737 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:11:42,212 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18038026737
2025-07-29 14:11:42,213 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:11:43,252 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:11:43,253 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:11:43,253 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:11:43,254 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:11:43,254 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:11:43,255 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:11:43,255 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:11:43,255 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:11:43,255 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:11:43,256 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-29 14:11:43,256 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:11:43,257 - __main__ - INFO - � 更新全局进度：已处理 8/2956 个联系人
2025-07-29 14:11:43,257 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:11:46,258 - __main__ - INFO - 🔄 完成第 2 轮窗口循环，重新开始下一轮
2025-07-29 14:11:46,258 - __main__ - INFO - 📊 当前进度：已处理 8/2956 个联系人
2025-07-29 14:11:46,258 - __main__ - INFO - 
============================================================
2025-07-29 14:11:46,258 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 3 轮)
2025-07-29 14:11:46,259 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:11:46,259 - __main__ - INFO - 📊 全局进度：已处理 8/2956 个联系人
2025-07-29 14:11:46,259 - __main__ - INFO - ============================================================
2025-07-29 14:11:46,260 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 14:11:46,260 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 14:11:46,260 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:11:46,261 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 14:11:46,261 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:11:46,582 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:11:46,582 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:11:46,583 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:11:46,583 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:11:46,583 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:11:46,584 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:11:46,584 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:11:46,584 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:11:46,585 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:11:46,585 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:11:46,787 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:11:46,788 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:11:46,788 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:11:46,788 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:11:47,092 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:11:47,093 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:11:47,094 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:11:47,094 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:11:47,095 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:11:47,095 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:11:47,096 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:11:47,096 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:11:47,097 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:11:47,097 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:11:47,299 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:11:47,300 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:11:47,303 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:11:47,604 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:11:47,604 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:11:47,605 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:11:47,605 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:11:47,605 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:11:47,606 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:11:47,606 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:11:47,606 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:11:48,607 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:11:48,607 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 14:11:48,607 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:11:48,608 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:11:48,608 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:11:48,608 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:11:48,609 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:11:48,609 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:11:48,810 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:11:48,810 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:11:51,185 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:11:51,186 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:11:51,186 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-29 14:11:53,657 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 14:11:53,858 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 14:11:53,859 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 14:11:56,227 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 14:11:56,227 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 14:11:56,227 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-29 14:11:57,853 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 14:11:58,053 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 14:11:58,054 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 14:12:00,435 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 14:12:00,436 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 14:12:00,437 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-29 14:12:02,380 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 14:12:02,581 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 14:12:02,582 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 14:12:04,950 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 14:12:04,950 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 14:12:04,951 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-29 14:12:07,758 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 14:12:07,959 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 14:12:07,959 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 14:12:10,335 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 14:12:10,335 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 14:12:10,335 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:12:10,336 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 14:12:10,336 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:12:10,338 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:12:10,339 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:12:10,340 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:12:10,340 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:12:10,341 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:12:10,342 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 8324648, 进程: Weixin.exe)
2025-07-29 14:12:10,346 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 14:12:10,346 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 592826)
2025-07-29 14:12:10,347 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 592826) - 增强版
2025-07-29 14:12:10,670 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:12:10,671 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:12:10,671 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:12:10,672 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:12:10,672 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-29 14:12:10,672 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:12:10,875 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:12:10,876 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:12:11,078 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:12:11,078 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:12:11,078 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 14:12:11,079 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 14:12:11,079 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 14:12:11,079 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 14:12:11,079 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 14:12:12,080 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 14:12:12,081 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 14:12:12,082 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 592826, 进程: Weixin.exe)
2025-07-29 14:12:12,083 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:12:12,083 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 14:12:12,084 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 14:12:12,085 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 14:12:12,085 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 8324648, 进程: Weixin.exe)
2025-07-29 14:12:12,088 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-29 14:12:12,089 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 14:12:12,090 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 14:12:12,091 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 14:12:12,091 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 14:12:12,092 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 14:12:12,418 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:12:12,424 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:12:12,432 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:12:12,443 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:12:12,449 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:12:12,459 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:12:12,460 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:12:12,463 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:12:12,463 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:12:12,464 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:12:12,678 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:12:12,681 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:12:12,685 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 14:12:12,989 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:12:12,989 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 14:12:12,989 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 14:12:13,990 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 14:12:13,990 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 14:12:13,991 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 14:12:13,993 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_141213.log
2025-07-29 14:12:13,994 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:12:13,994 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 14:12:13,995 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 14:12:13,995 - __main__ - INFO - 🔄 传递全局联系人索引: 8
2025-07-29 14:12:13,995 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 14:12:13,995 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 14:12:13,998 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-29 14:12:13,998 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-29 14:12:13,999 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 14:12:13,999 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 14:12:14,000 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-29 14:12:14,000 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-29 14:12:14,000 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-29 14:12:14,001 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 14:12:14,002 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:12:14,002 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 8324648
2025-07-29 14:12:14,003 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 8324648) - 增强版
2025-07-29 14:12:14,311 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:12:14,311 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:12:14,312 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 14:12:14,312 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 14:12:14,312 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 14:12:14,313 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 14:12:14,515 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 14:12:14,516 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 14:12:14,719 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:12:14,719 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:12:14,722 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 8324648 (API返回: None)
2025-07-29 14:12:15,023 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:12:15,024 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 14:12:15,024 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 14:12:15,024 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 14:12:15,025 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 14:12:15,025 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 14:12:15,026 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 14:12:15,029 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 14:12:15,029 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 14:12:15,470 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 14:12:15,470 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:12:15,721 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2948 个
2025-07-29 14:12:15,722 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 9 个联系人开始处理
2025-07-29 14:12:15,723 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2940 个
2025-07-29 14:12:15,723 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2940 个 (总计: 3135 个)
2025-07-29 14:12:15,723 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 14:12:15,724 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 14:12:15,725 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:15,725 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 14:12:15,726 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2940
2025-07-29 14:12:15,727 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 19971360808 (林昕)
2025-07-29 14:12:15,727 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:22,279 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 19971360808
2025-07-29 14:12:22,280 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:12:22,280 - modules.wechat_auto_add_simple - INFO - 👥 开始为 19971360808 执行添加朋友操作...
2025-07-29 14:12:22,281 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:12:22,281 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:12:22,282 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:12:22,284 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:12:22,291 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:12:22,295 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:12:22,295 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:12:22,296 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:12:22,296 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:12:22,296 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:12:22,298 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:12:22,298 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:12:22,308 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:12:22,312 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:12:22,319 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:12:22,323 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:12:22,327 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:12:22,330 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 14:12:22,338 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:12:22,842 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:12:22,844 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:12:22,916 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.52, 边缘比例0.0351
2025-07-29 14:12:22,926 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_141222.png
2025-07-29 14:12:22,931 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:12:22,937 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:12:22,939 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:12:22,942 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:12:22,944 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:12:22,953 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_141222.png
2025-07-29 14:12:22,956 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-29 14:12:22,958 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:12:22,961 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:12:22,964 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:12:22,969 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-29 14:12:22,972 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-29 14:12:22,975 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-29 14:12:22,977 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-29 14:12:22,980 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:12:22,986 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-29 14:12:22,990 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-29 14:12:22,992 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-29 14:12:22,995 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:12:23,001 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-29 14:12:23,005 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-29 14:12:23,011 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:12:23,018 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:12:23,027 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-29 14:12:23,034 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:12:23,040 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:12:23,043 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-29 14:12:23,048 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-29 14:12:23,056 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-29 14:12:23,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-29 14:12:23,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-29 14:12:23,071 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-29 14:12:23,073 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-29 14:12:23,076 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-29 14:12:23,079 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-29 14:12:23,096 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-29 14:12:23,106 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-29 14:12:23,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-29 14:12:23,115 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-29 14:12:23,121 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-29 14:12:23,123 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-29 14:12:23,127 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-29 14:12:23,132 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-29 14:12:23,138 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-29 14:12:23,142 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:12:23,145 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-29 14:12:23,148 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-29 14:12:23,153 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:12:23,155 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:12:23,165 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_141223.png
2025-07-29 14:12:23,170 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:12:23,172 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-29 14:12:23,178 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_141223.png
2025-07-29 14:12:23,202 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:12:23,206 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-29 14:12:23,209 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:12:23,211 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:12:23,515 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-29 14:12:24,300 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:12:24,303 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:12:24,305 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:24,305 - modules.wechat_auto_add_simple - INFO - ✅ 19971360808 添加朋友操作执行成功
2025-07-29 14:12:24,306 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:24,306 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:12:26,308 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:12:26,309 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:12:26,309 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:12:26,309 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:12:26,309 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:12:26,310 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:12:26,310 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:12:26,310 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:12:26,310 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:12:26,311 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 19971360808
2025-07-29 14:12:26,312 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:12:26,312 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:12:26,312 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:12:26,313 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:12:26,313 - modules.friend_request_window - INFO -    📱 phone: '19971360808'
2025-07-29 14:12:26,313 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:12:26,314 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:12:26,765 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:12:26,766 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:12:26,766 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:12:26,766 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:12:26,767 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 19971360808
2025-07-29 14:12:26,768 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:12:26,768 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:12:26,769 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:12:26,769 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:12:26,769 - modules.friend_request_window - INFO -    📱 手机号码: 19971360808
2025-07-29 14:12:26,770 - modules.friend_request_window - INFO -    🆔 准考证: 014325110111
2025-07-29 14:12:26,770 - modules.friend_request_window - INFO -    👤 姓名: 林昕
2025-07-29 14:12:26,770 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:12:26,770 - modules.friend_request_window - INFO -    📝 备注格式: '014325110111-林昕-2025-07-29 22:12:26'
2025-07-29 14:12:26,771 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:12:26,772 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110111-林昕-2025-07-29 22:12:26'
2025-07-29 14:12:26,772 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:12:26,775 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 14:12:26,776 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-29 14:12:26,777 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:26,779 - modules.wechat_auto_add_simple - INFO - ✅ 19971360808 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-29 14:12:26,779 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 19971360808
2025-07-29 14:12:26,780 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:30,032 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2940
2025-07-29 14:12:30,032 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13683381985 (张金龙)
2025-07-29 14:12:30,033 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:36,597 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13683381985
2025-07-29 14:12:36,597 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 14:12:36,597 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13683381985 执行添加朋友操作...
2025-07-29 14:12:36,598 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 14:12:36,598 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 14:12:36,599 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 14:12:36,606 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 14:12:36,620 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 14:12:36,625 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 14:12:36,625 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 14:12:36,626 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 14:12:36,626 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 14:12:36,626 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 14:12:36,627 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 14:12:36,627 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 14:12:36,636 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:12:36,641 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:12:36,644 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 14:12:36,652 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 14:12:36,656 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 14:12:36,660 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-29 14:12:36,666 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 14:12:37,171 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 14:12:37,174 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 14:12:37,231 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差46.04, 边缘比例0.0357
2025-07-29 14:12:37,240 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_141237.png
2025-07-29 14:12:37,246 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 14:12:37,251 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 14:12:37,255 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 14:12:37,258 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 14:12:37,261 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 14:12:37,268 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_141237.png
2025-07-29 14:12:37,273 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 14:12:37,275 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 14:12:37,278 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 14:12:37,284 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 14:12:37,292 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 14:12:37,298 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 14:12:37,305 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 14:12:37,308 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 14:12:37,321 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_141237.png
2025-07-29 14:12:37,325 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 14:12:37,328 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 14:12:37,341 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_141237.png
2025-07-29 14:12:37,367 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 14:12:37,375 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 14:12:37,380 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 14:12:37,387 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 14:12:37,691 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 14:12:38,461 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 14:12:38,466 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 14:12:38,472 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:38,473 - modules.wechat_auto_add_simple - INFO - ✅ 13683381985 添加朋友操作执行成功
2025-07-29 14:12:38,474 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:38,474 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 14:12:40,476 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 14:12:40,477 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 14:12:40,477 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 14:12:40,477 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 14:12:40,477 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 14:12:40,478 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 14:12:40,478 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 14:12:40,478 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 14:12:40,478 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 14:12:40,479 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13683381985
2025-07-29 14:12:40,479 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 14:12:40,480 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:12:40,480 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:12:40,480 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 14:12:40,480 - modules.friend_request_window - INFO -    📱 phone: '13683381985'
2025-07-29 14:12:40,481 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 14:12:40,481 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 14:12:40,872 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 14:12:40,872 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 14:12:40,873 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 14:12:40,873 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 14:12:40,874 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13683381985
2025-07-29 14:12:40,874 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 14:12:40,875 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:12:40,875 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 14:12:40,875 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 14:12:40,876 - modules.friend_request_window - INFO -    📱 手机号码: 13683381985
2025-07-29 14:12:40,876 - modules.friend_request_window - INFO -    🆔 准考证: 014325110112
2025-07-29 14:12:40,876 - modules.friend_request_window - INFO -    👤 姓名: 张金龙
2025-07-29 14:12:40,876 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:12:40,877 - modules.friend_request_window - INFO -    📝 备注格式: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:40,877 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 14:12:40,877 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:40,881 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 14:12:40,883 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2231490, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 14:12:40,884 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2231490)
2025-07-29 14:12:40,885 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 14:12:40,885 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 14:12:40,886 - modules.friend_request_window - INFO - 🔄 激活窗口: 2231490
2025-07-29 14:12:41,589 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 14:12:41,589 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 14:12:41,590 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 14:12:41,590 - modules.friend_request_window - INFO -    1. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 14:12:41,590 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 14:12:41,591 - modules.friend_request_window - INFO -    2. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 14:12:41,591 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 14:12:41,591 - modules.friend_request_window - INFO -    3. D:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 14:12:41,591 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 14:12:41,592 - modules.friend_request_window - INFO -    4. D:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 14:12:41,592 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 14:12:41,592 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 14:12:41,592 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 14:12:41,593 - modules.friend_request_window - INFO -    📝 remark参数: '014325110112-张金龙-2025-07-29 22:12:40' (类型: <class 'str'>, 长度: 36)
2025-07-29 14:12:41,593 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 14:12:41,593 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:41,593 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 14:12:41,594 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 14:12:41,594 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 14:12:41,594 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 14:12:41,594 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 14:12:41,595 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 14:12:41,595 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 14:12:42,518 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 14:12:47,759 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 14:12:47,760 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 14:12:47,760 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 14:12:47,760 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 14:12:47,761 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:12:48,072 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:12:48,072 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:12:48,975 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:12:48,984 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:12:48,985 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 14:12:48,985 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 14:12:48,987 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 14:12:48,990 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 14:12:49,492 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 14:12:49,493 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 14:12:49,495 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 14:12:49,496 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 14:12:49,496 - modules.friend_request_window - INFO -    📝 内容: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:49,496 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-29 14:12:49,497 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110112-\xe5\xbc\xa0\xe9\x87\x91\xe9\xbe\x99-2025-07-29 22:12:40'
2025-07-29 14:12:49,497 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 14:12:50,416 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 14:12:55,693 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 14:12:55,694 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 14:12:55,694 - modules.friend_request_window - INFO -    📝 原始文本: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:55,694 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-29 14:12:55,695 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '每个窗口处理2个联系人后切换到下一个窗口 ✅

但是没有循环回到第一个窗口继续处理剩余联系人 ❌...' (前50字符)
2025-07-29 14:12:56,006 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 14:12:56,007 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 14:12:56,910 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 14:12:56,919 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 14:12:56,920 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:56,920 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 14:12:56,921 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:56,921 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-29 14:12:57,422 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:12:57,423 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 14:12:57,423 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 14:12:57,424 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 14:12:57,424 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 14:12:57,424 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 14:12:57,424 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 14:12:58,225 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 14:12:58,225 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 14:12:58,226 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 14:12:58,839 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:12:58,839 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 14:12:58,839 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 14:12:58,840 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 14:12:59,341 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 14:12:59,344 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 14:12:59,344 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 14:12:59,345 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 14:12:59,345 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 14:12:59,345 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 14:12:59,346 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 14:12:59,346 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 14:12:59,346 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 14:12:59,346 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:12:59,347 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 14:12:59,347 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 14:12:59,348 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 14:12:59,348 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 14:12:59,350 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 14:12:59,351 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 14:12:59,351 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 14:12:59,352 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-29 14:12:59,354 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-29 14:12:59,857 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 14:12:59,857 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 14:12:59,857 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 14:12:59,858 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 14:12:59,858 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 14:12:59,859 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 14:12:59,859 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 14:12:59,859 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 14:13:00,766 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 14:13:00,767 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 14:13:00,767 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 14:13:00,767 - modules.friend_request_window - INFO - 🔄 步骤2: 关闭添加朋友窗口...
2025-07-29 14:13:00,767 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 14:13:01,268 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 14:13:02,381 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 14:13:02,382 - modules.friend_request_window - INFO - 🔄 步骤3: 关闭当前微信窗口...
2025-07-29 14:13:02,382 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-29 14:13:02,883 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-29 14:13:03,999 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-29 14:13:03,999 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 14:13:04,019 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-29 14:13:04,020 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-29 14:13:04,020 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-29 14:13:04,021 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-29 14:13:04,022 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-29 14:13:04,022 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 14:13:04,023 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 14:13:04,023 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 14:13:04,023 - modules.friend_request_window - INFO -    📝 备注信息: '014325110112-张金龙-2025-07-29 22:12:40'
2025-07-29 14:13:04,524 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 14:13:04,525 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:13:04,525 - modules.wechat_auto_add_simple - INFO - ✅ 13683381985 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 14:13:04,526 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13683381985
2025-07-29 14:13:04,527 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 14:13:05,546 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-29 14:13:05,546 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-29 14:13:05,547 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-29 14:13:05,548 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-29 14:13:05,548 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-29 14:13:05,549 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-29 14:13:05,549 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-29 14:13:05,549 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-29 14:13:05,549 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-29 14:13:05,550 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-29 14:13:05,550 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-29 14:13:05,551 - __main__ - INFO - � 更新全局进度：已处理 10/2956 个联系人
2025-07-29 14:13:05,551 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-29 14:13:08,551 - __main__ - INFO - 
============================================================
2025-07-29 14:13:08,552 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 3 轮)
2025-07-29 14:13:08,552 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:13:08,552 - __main__ - INFO - 📊 全局进度：已处理 10/2956 个联系人
2025-07-29 14:13:08,553 - __main__ - INFO - ============================================================
2025-07-29 14:13:08,553 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-29 14:13:08,553 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-29 14:13:08,553 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 14:13:08,554 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-29 14:13:08,554 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:13:08,876 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:13:08,876 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:13:08,876 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:13:08,877 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:13:08,877 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:13:08,877 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:13:08,878 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:13:08,878 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:13:08,878 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:13:08,878 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:13:09,080 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:13:09,081 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:13:09,081 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-29 14:13:09,081 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-29 14:13:09,386 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 14:13:09,386 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 14:13:09,386 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:13:09,387 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 14:13:09,387 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 14:13:09,387 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:13:09,388 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:13:09,388 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:13:09,389 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:13:09,389 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 14:13:09,591 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 14:13:09,592 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 14:13:09,593 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-29 14:13:09,894 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 14:13:09,894 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 14:13:09,894 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 14:13:09,895 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 14:13:09,895 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 14:13:09,896 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 14:13:09,896 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 14:13:09,896 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 14:13:10,897 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 14:13:10,898 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-29 14:13:10,898 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 14:13:10,898 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 14:13:10,899 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 14:13:10,900 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 14:13:10,900 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 14:13:10,901 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 14:13:11,102 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 14:13:11,102 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 14:13:13,532 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 14:13:13,532 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 14:13:13,532 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
