2025-07-30 10:21:31,748 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:21:31,749 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:21:31,750 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:21:31,754 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:21:31,755 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 10:21:31,756 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:21:31,757 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:21:31,761 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:21:31,761 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:21:31,763 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:21:31,764 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:21:31,782 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:21:31,786 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_102131.log
2025-07-30 10:21:31,787 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:21:31,787 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:21:31,788 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:21:31,788 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 10:21:31,788 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 10:21:31,789 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 18:21:31
2025-07-30 10:21:31,789 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 10:21:31,790 - __main__ - INFO - 📅 启动时间: 2025-07-30 18:21:31
2025-07-30 10:21:31,790 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 10:21:31,793 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:21:32,332 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:21:32,332 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:21:32,870 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:21:32,882 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:21:32,886 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:21:32,887 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 10:21:32,889 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 10:21:32,914 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 10:21:32,915 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 10:21:34,118 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 10:21:34,118 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 10:21:34,118 - __main__ - INFO - 📋 待处理联系人数: 2923
2025-07-30 10:21:34,119 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 10:21:34,119 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2923
2025-07-30 10:21:34,120 - __main__ - INFO - 
============================================================
2025-07-30 10:21:34,120 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 10:21:34,120 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:21:34,120 - __main__ - INFO - 📊 全局进度：已处理 0/2923 个联系人
2025-07-30 10:21:34,121 - __main__ - INFO - ============================================================
2025-07-30 10:21:34,121 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 10:21:34,121 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:21:34,122 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:21:34,122 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 10:21:34,122 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:21:34,448 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:21:34,449 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:21:34,449 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:21:34,449 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:21:34,449 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:21:34,450 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:21:34,450 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:21:34,450 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:21:34,451 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:21:34,451 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:21:34,653 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:21:34,654 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:21:34,654 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:21:34,654 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:21:34,959 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:21:34,959 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:21:34,960 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:21:34,960 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:21:34,960 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:21:34,961 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:21:34,961 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:21:34,961 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:21:34,962 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:21:34,962 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:21:35,163 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:21:35,164 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:21:35,165 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:21:35,466 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:21:35,467 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:21:35,467 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:21:35,467 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:21:35,468 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:21:35,468 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:21:35,468 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:21:35,469 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:21:36,470 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:21:36,470 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 10:21:36,470 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:21:36,471 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:21:36,471 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:21:36,471 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:21:36,472 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:21:36,472 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:21:36,673 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:21:36,675 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:21:39,061 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:21:39,061 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:21:39,062 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 10:21:40,901 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:21:41,102 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:21:41,103 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:21:43,478 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:21:43,478 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:21:43,479 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 10:21:45,286 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:21:45,486 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:21:45,487 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:21:47,862 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:21:47,862 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:21:47,862 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 10:21:50,675 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:21:50,876 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:21:50,877 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:21:53,260 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:21:53,261 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:21:53,261 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 10:21:55,257 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:21:55,458 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:21:55,459 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:21:57,844 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:21:57,844 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:21:57,844 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:21:57,845 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:21:57,845 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:21:57,847 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:21:57,847 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:21:57,848 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:21:57,848 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:21:57,850 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5507332, 进程: Weixin.exe)
2025-07-30 10:21:57,852 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:21:57,853 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 5507332)
2025-07-30 10:21:57,853 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5507332) - 增强版
2025-07-30 10:21:58,157 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:21:58,157 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:21:58,158 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:21:58,158 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:21:58,158 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:21:58,159 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:21:58,363 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:21:58,363 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:21:58,565 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:21:58,566 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:21:58,566 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:21:58,566 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:21:58,566 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:21:58,567 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:21:58,567 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:21:59,567 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:21:59,568 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:21:59,571 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:21:59,572 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:21:59,575 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:21:59,575 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:21:59,577 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5507332, 进程: Weixin.exe)
2025-07-30 10:21:59,579 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:21:59,580 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:21:59,580 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:21:59,582 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:21:59,583 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:21:59,583 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:21:59,893 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:21:59,893 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:21:59,894 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:21:59,895 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:21:59,895 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:21:59,895 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:21:59,896 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:21:59,896 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:21:59,897 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:21:59,897 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:22:00,099 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:22:00,099 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:22:00,100 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:22:00,401 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:22:00,402 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:22:00,402 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:22:01,402 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:22:01,403 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 10:22:01,403 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:22:01,407 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_102201.log
2025-07-30 10:22:01,408 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:22:01,409 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:22:01,409 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:22:01,410 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 10:22:01,410 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:22:01,410 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:22:01,412 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 10:22:01,412 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 10:22:01,412 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:22:01,413 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:22:01,413 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 10:22:01,413 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 10:22:01,413 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:22:01,415 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:22:01,415 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 5507332
2025-07-30 10:22:01,416 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5507332) - 增强版
2025-07-30 10:22:01,724 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:22:01,724 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:22:01,725 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:22:01,725 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:22:01,725 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 10:22:01,726 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:22:01,726 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 10:22:01,726 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:22:01,928 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:22:01,928 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:22:01,932 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 5507332 (API返回: None)
2025-07-30 10:22:02,233 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:22:02,233 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:22:02,233 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:22:02,234 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:22:02,235 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:22:02,235 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:22:02,236 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:22:02,240 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:22:02,241 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:22:02,774 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:22:02,775 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:22:03,070 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2923 个
2025-07-30 10:22:03,071 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2923 个 (总计: 3135 个)
2025-07-30 10:22:03,073 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:22:03,074 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:22:03,074 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:03,075 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:22:03,075 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2923
2025-07-30 10:22:03,075 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17519194546 (石寿骏)
2025-07-30 10:22:03,076 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:09,666 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17519194546
2025-07-30 10:22:09,667 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:22:09,668 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17519194546 执行添加朋友操作...
2025-07-30 10:22:09,669 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:22:09,671 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:22:09,681 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:22:09,686 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:22:09,695 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 10:22:09,700 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:22:09,700 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:22:09,702 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:22:09,703 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:22:09,703 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:22:09,704 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:22:09,704 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:22:09,714 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:22:09,726 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:22:09,737 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:22:09,747 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 10:22:09,749 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 10:22:09,750 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:22:10,252 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:22:10,253 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:22:10,346 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.45, 边缘比例0.0365
2025-07-30 10:22:10,354 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_102210.png
2025-07-30 10:22:10,358 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:22:10,360 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:22:10,361 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:22:10,362 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:22:10,363 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:22:10,370 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_102210.png
2025-07-30 10:22:10,375 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 10:22:10,376 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 10:22:10,378 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 10:22:10,380 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:22:10,381 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 10:22:10,381 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 10:22:10,385 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 10:22:10,388 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:22:10,400 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_102210.png
2025-07-30 10:22:10,404 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:22:10,414 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 10:22:10,418 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_102210.png
2025-07-30 10:22:10,504 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:22:10,511 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 10:22:10,514 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:22:10,515 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:22:10,818 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 10:22:11,592 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:22:11,593 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:22:11,594 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:11,595 - modules.wechat_auto_add_simple - INFO - ✅ 17519194546 添加朋友操作执行成功
2025-07-30 10:22:11,595 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:11,596 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:22:13,597 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:22:13,598 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:22:13,599 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:22:13,600 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:22:13,602 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:22:13,603 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:22:13,609 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:22:13,611 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:22:13,611 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:22:13,612 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17519194546
2025-07-30 10:22:13,616 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:22:13,616 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:22:13,617 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:22:13,617 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:22:13,618 - modules.friend_request_window - INFO -    📱 phone: '17519194546'
2025-07-30 10:22:13,618 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:22:13,618 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:22:14,399 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:22:14,399 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:22:14,400 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:22:14,401 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:22:14,403 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17519194546
2025-07-30 10:22:14,403 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:22:14,407 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:22:14,409 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:22:14,410 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:22:14,412 - modules.friend_request_window - INFO -    📱 手机号码: 17519194546
2025-07-30 10:22:14,413 - modules.friend_request_window - INFO -    🆔 准考证: 014325110125
2025-07-30 10:22:14,413 - modules.friend_request_window - INFO -    👤 姓名: 石寿骏
2025-07-30 10:22:14,413 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:22:14,414 - modules.friend_request_window - INFO -    📝 备注格式: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:14,414 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:22:14,415 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:14,416 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:22:14,417 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1835780, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:22:14,419 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1835780)
2025-07-30 10:22:14,420 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:22:14,421 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:22:14,422 - modules.friend_request_window - INFO - 🔄 激活窗口: 1835780
2025-07-30 10:22:15,127 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:22:15,127 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:22:15,128 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:22:15,128 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:22:15,129 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:22:15,129 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:22:15,129 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:22:15,130 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:22:15,130 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:22:15,130 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:22:15,131 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:22:15,131 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:22:15,131 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:22:15,131 - modules.friend_request_window - INFO -    📝 remark参数: '014325110125-石寿骏-2025-07-30 18:22:14' (类型: <class 'str'>, 长度: 36)
2025-07-30 10:22:15,132 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:22:15,132 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:15,132 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:22:15,133 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:22:15,133 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:22:15,133 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:22:15,134 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:22:15,135 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:22:15,135 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:22:16,042 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:22:21,288 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:22:21,289 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:22:21,289 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:22:21,290 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:22:21,291 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '智能坐标计...' (前50字符)
2025-07-30 10:22:21,604 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:22:21,605 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:22:22,509 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:22:22,516 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:22:22,517 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:22:22,517 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:22:22,518 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:22:22,518 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:22:23,019 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:22:23,020 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:22:23,020 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:22:23,021 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:22:23,022 - modules.friend_request_window - INFO -    📝 内容: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:23,023 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 10:22:23,024 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110125-\xe7\x9f\xb3\xe5\xaf\xbf\xe9\xaa\x8f-2025-07-30 18:22:14'
2025-07-30 10:22:23,025 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:22:23,943 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:22:29,194 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:22:29,194 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:22:29,194 - modules.friend_request_window - INFO -    📝 原始文本: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:29,195 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 10:22:29,195 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '智能坐标计...' (前50字符)
2025-07-30 10:22:29,505 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:22:29,506 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:22:30,408 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:22:30,417 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:22:30,418 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:30,418 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:22:30,419 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:30,419 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 10:22:30,920 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:30,922 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:22:30,922 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:22:30,923 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:22:30,923 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:22:30,924 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:22:30,925 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:22:31,728 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:22:31,729 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:22:31,729 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:22:32,345 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:32,345 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:22:32,346 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:22:32,346 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:22:32,866 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:33,100 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:33,334 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:33,573 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:33,807 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:34,042 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:34,277 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:34,513 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:34,744 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:34,977 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:35,211 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:35,442 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:35,679 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:35,912 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:36,148 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:36,381 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:36,614 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:36,850 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:37,082 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:37,326 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:22:37,544 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 10:22:37,545 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 10:22:38,546 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:22:38,548 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 10:22:38,549 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 10:22:38,549 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:22:38,549 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:22:38,549 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:22:38,550 - modules.friend_request_window - INFO -    📝 备注信息: '014325110125-石寿骏-2025-07-30 18:22:14'
2025-07-30 10:22:39,050 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:22:39,051 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:39,051 - modules.wechat_auto_add_simple - INFO - ✅ 17519194546 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:22:39,052 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17519194546
2025-07-30 10:22:39,053 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:42,649 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2923
2025-07-30 10:22:42,649 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17382829792 (秦梦浩)
2025-07-30 10:22:42,649 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:49,299 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17382829792
2025-07-30 10:22:49,338 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:22:49,360 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17382829792 执行添加朋友操作...
2025-07-30 10:22:49,367 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:22:49,406 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:22:49,409 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:22:49,415 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:22:49,426 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 10:22:49,450 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:22:49,457 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:22:49,461 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:22:49,462 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:22:49,463 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:22:49,466 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:22:49,475 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:22:49,565 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:22:49,603 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:22:49,630 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:22:49,657 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 10:22:49,663 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 10:22:49,668 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:22:50,179 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:22:50,184 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:22:50,259 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.77, 边缘比例0.0349
2025-07-30 10:22:50,279 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_102250.png
2025-07-30 10:22:50,287 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:22:50,300 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:22:50,310 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:22:50,313 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:22:50,315 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:22:50,332 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_102250.png
2025-07-30 10:22:50,337 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 10:22:50,348 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:22:50,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:22:50,362 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:22:50,365 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 10:22:50,375 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 10:22:50,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:22:50,391 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 10:22:50,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:22:50,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 10:22:50,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 10:22:50,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 10:22:50,409 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:22:50,417 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:22:50,438 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:22:50,498 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:22:50,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:22:50,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:22:50,519 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:22:50,529 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:22:50,531 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 10:22:50,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:22:50,546 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 10:22:50,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:22:50,559 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 10:22:50,562 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 10:22:50,566 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 10:22:50,570 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 10:22:50,577 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 10:22:50,582 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 10:22:50,591 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 10:22:50,598 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 10:22:50,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 10:22:50,625 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 10:22:50,628 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 10:22:50,633 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 10:22:50,650 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 10:22:50,662 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 10:22:50,667 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:22:50,674 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 10:22:50,677 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 10:22:50,679 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 10:22:50,683 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:22:50,695 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_102250.png
2025-07-30 10:22:50,698 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:22:50,699 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 10:22:50,705 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_102250.png
2025-07-30 10:22:50,738 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:22:50,744 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 10:22:50,749 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:22:50,751 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:22:51,061 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 10:22:51,835 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:22:51,846 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:22:51,848 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:51,849 - modules.wechat_auto_add_simple - INFO - ✅ 17382829792 添加朋友操作执行成功
2025-07-30 10:22:51,850 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:51,855 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:22:53,859 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:22:53,859 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:22:53,859 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:22:53,860 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:22:53,860 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:22:53,860 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:22:53,860 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:22:53,861 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:22:53,861 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:22:53,861 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17382829792
2025-07-30 10:22:53,862 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:22:53,862 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:22:53,863 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:22:53,863 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:22:53,863 - modules.friend_request_window - INFO -    📱 phone: '17382829792'
2025-07-30 10:22:53,863 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:22:53,864 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:22:54,477 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:22:54,478 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:22:54,481 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:22:54,485 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:22:54,489 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17382829792
2025-07-30 10:22:54,491 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:22:54,496 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:22:54,498 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:22:54,504 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:22:54,507 - modules.friend_request_window - INFO -    📱 手机号码: 17382829792
2025-07-30 10:22:54,512 - modules.friend_request_window - INFO -    🆔 准考证: 014325110126
2025-07-30 10:22:54,514 - modules.friend_request_window - INFO -    👤 姓名: 秦梦浩
2025-07-30 10:22:54,515 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:22:54,523 - modules.friend_request_window - INFO -    📝 备注格式: '014325110126-秦梦浩-2025-07-30 18:22:54'
2025-07-30 10:22:54,524 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:22:54,527 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110126-秦梦浩-2025-07-30 18:22:54'
2025-07-30 10:22:54,531 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:22:54,547 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 10:22:54,550 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 10:22:54,557 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:54,560 - modules.wechat_auto_add_simple - INFO - ✅ 17382829792 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 10:22:54,564 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17382829792
2025-07-30 10:22:54,568 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:22:56,464 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 10:22:56,464 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 10:22:56,465 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 10:22:56,466 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 10:22:56,466 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 10:22:56,466 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 10:22:56,467 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 10:22:56,467 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 10:22:56,467 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 10:22:56,467 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 10:22:56,467 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 10:22:56,468 - __main__ - INFO - � 更新全局进度：已处理 2/2923 个联系人
2025-07-30 10:22:56,469 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 10:22:59,470 - __main__ - INFO - 
============================================================
2025-07-30 10:22:59,470 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 10:22:59,471 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 10:22:59,471 - __main__ - INFO - 📊 全局进度：已处理 2/2923 个联系人
2025-07-30 10:22:59,472 - __main__ - INFO - ============================================================
2025-07-30 10:22:59,472 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 10:22:59,472 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 10:22:59,473 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:22:59,473 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 10:22:59,473 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:22:59,799 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:22:59,801 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:22:59,802 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:22:59,803 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:22:59,804 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:22:59,804 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:22:59,806 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:22:59,810 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:22:59,811 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:22:59,811 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:23:00,015 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:23:00,016 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:23:00,019 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 10:23:00,021 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:23:00,325 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:23:00,325 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:23:00,326 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:23:00,326 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:23:00,327 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:23:00,327 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:23:00,327 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:23:00,327 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:23:00,328 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:23:00,328 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:23:00,531 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:23:00,532 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:23:00,534 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 10:23:00,835 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:23:00,835 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:23:00,836 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:23:00,836 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:23:00,836 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:23:00,837 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:23:00,837 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:23:00,837 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:23:01,838 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:23:01,839 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 10:23:01,839 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:23:01,840 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:23:01,840 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:23:01,840 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:23:01,841 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:23:01,841 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:23:02,042 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:23:02,043 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:23:04,422 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:23:04,423 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:23:04,423 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 10:23:07,196 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:23:07,397 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:23:07,397 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:23:09,766 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:23:09,766 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:23:09,767 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 10:23:11,462 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:23:11,663 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:23:11,663 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:23:14,040 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:23:14,041 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:23:14,041 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 10:23:16,577 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:23:16,778 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:23:16,778 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:23:19,170 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:23:19,189 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:23:19,221 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 10:23:20,857 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:23:21,058 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:23:21,058 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:23:23,438 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:23:23,438 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:23:23,438 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:23:23,439 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:23:23,439 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:23:23,441 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:23:23,441 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:23:23,442 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5507332, 进程: Weixin.exe)
2025-07-30 10:23:23,443 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:23:23,443 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:23:23,444 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3803402, 进程: Weixin.exe)
2025-07-30 10:23:23,448 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 10:23:23,448 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3803402)
2025-07-30 10:23:23,449 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3803402) - 增强版
2025-07-30 10:23:23,753 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:23:23,753 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:23:23,753 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:23:23,754 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:23:23,754 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:23:23,754 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:23:23,958 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:23:23,959 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:23:24,160 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:23:24,161 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:23:24,161 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:23:24,161 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:23:24,162 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:23:24,162 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:23:24,162 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:23:25,163 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:23:25,164 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:23:25,166 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:23:25,166 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:23:25,171 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5507332, 进程: Weixin.exe)
2025-07-30 10:23:25,175 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:23:25,177 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:23:25,179 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3803402, 进程: Weixin.exe)
2025-07-30 10:23:25,182 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 10:23:25,182 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:23:25,183 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:23:25,183 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:23:25,187 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 10:23:25,188 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:23:25,496 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:23:25,497 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:23:25,497 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:23:25,498 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:23:25,498 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:23:25,498 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:23:25,499 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:23:25,499 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:23:25,500 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:23:25,500 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:23:25,702 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:23:25,703 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:23:25,704 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 10:23:26,005 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:23:26,005 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:23:26,005 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:23:27,006 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:23:27,006 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 10:23:27,007 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:23:27,009 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_102327.log
2025-07-30 10:23:27,010 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:23:27,011 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:23:27,011 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:23:27,011 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 10:23:27,011 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:23:27,012 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:23:27,014 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 10:23:27,015 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 10:23:27,016 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:23:27,019 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:23:27,021 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 10:23:27,021 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 10:23:27,021 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 10:23:27,022 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:23:27,023 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:23:27,023 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3803402
2025-07-30 10:23:27,023 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3803402) - 增强版
2025-07-30 10:23:27,332 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:23:27,332 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:23:27,332 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:23:27,333 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:23:27,333 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 10:23:27,334 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:23:27,334 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 10:23:27,334 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:23:27,536 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:23:27,536 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:23:27,540 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3803402 (API返回: None)
2025-07-30 10:23:27,841 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:23:27,842 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:23:27,842 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:23:27,842 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:23:27,843 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:23:27,843 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:23:27,844 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:23:27,847 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:23:27,848 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:23:28,349 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:23:28,350 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:23:28,607 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2921 个
2025-07-30 10:23:28,608 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 10:23:28,608 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2919 个
2025-07-30 10:23:28,608 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2919 个 (总计: 3135 个)
2025-07-30 10:23:28,609 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:23:28,609 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:23:28,609 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:23:28,609 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:23:28,610 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2919
2025-07-30 10:23:28,610 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17659905589 (王兴磊)
2025-07-30 10:23:28,610 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:23:35,180 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17659905589
2025-07-30 10:23:35,180 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:23:35,180 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17659905589 执行添加朋友操作...
2025-07-30 10:23:35,181 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:23:35,181 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:23:35,182 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:23:35,183 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:23:35,188 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 10:23:35,190 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:23:35,190 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:23:35,191 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:23:35,192 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:23:35,192 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:23:35,194 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:23:35,194 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:23:35,201 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:23:35,206 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:23:35,208 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:23:35,210 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:23:35,212 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 10:23:35,213 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 10:23:35,214 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:23:35,716 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:23:35,718 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:23:35,781 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差52.02, 边缘比例0.0472
2025-07-30 10:23:35,789 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_102335.png
2025-07-30 10:23:35,793 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:23:35,794 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:23:35,797 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:23:35,798 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:23:35,800 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:23:35,809 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_102335.png
2025-07-30 10:23:35,810 - WeChatAutoAdd - INFO - 底部区域原始检测到 25 个轮廓
2025-07-30 10:23:35,811 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 10:23:35,813 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 10:23:35,814 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:23:35,815 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 10:23:35,818 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 10:23:35,821 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 10:23:35,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:23:35,826 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 10:23:35,829 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:23:35,830 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:23:35,831 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:23:35,832 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:23:35,835 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:23:35,839 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:23:35,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:23:35,842 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,250), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 10:23:35,845 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 10:23:35,847 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=77.6 (阈值:60)
2025-07-30 10:23:35,848 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 10:23:35,850 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=81.5 (阈值:60)
2025-07-30 10:23:35,856 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 10:23:35,858 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=71.7 (阈值:60)
2025-07-30 10:23:35,861 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 10:23:35,862 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 10:23:35,864 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 10:23:35,865 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,241), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:23:35,874 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,237), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:23:35,875 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,230), 尺寸14x13, 长宽比1.08, 面积182
2025-07-30 10:23:35,877 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=83.0 (阈值:60)
2025-07-30 10:23:35,880 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,230), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:23:35,881 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,230), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 10:23:35,888 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸42x15, 长宽比2.80, 面积630
2025-07-30 10:23:35,891 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=90.7 (阈值:60)
2025-07-30 10:23:35,893 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:23:35,896 - WeChatAutoAdd - INFO - 底部区域找到 4 个按钮候选
2025-07-30 10:23:35,897 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 10:23:35,898 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 10:23:35,905 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 10:23:35,918 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_102335.png
2025-07-30 10:23:35,923 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 10:23:35,924 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:23:36,226 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 10:23:36,999 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:23:37,002 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:23:37,003 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:23:37,004 - modules.wechat_auto_add_simple - INFO - ✅ 17659905589 添加朋友操作执行成功
2025-07-30 10:23:37,004 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:23:37,005 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:23:39,006 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:23:39,007 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:23:39,007 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:23:39,008 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:23:39,008 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:23:39,008 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:23:39,009 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:23:39,009 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:23:39,009 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:23:39,010 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17659905589
2025-07-30 10:23:39,010 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:23:39,011 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:23:39,011 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:23:39,011 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:23:39,012 - modules.friend_request_window - INFO -    📱 phone: '17659905589'
2025-07-30 10:23:39,014 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:23:39,015 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:23:39,520 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:23:39,522 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:23:39,522 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:23:39,523 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:23:39,524 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17659905589
2025-07-30 10:23:39,525 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:23:39,525 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:23:39,526 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:23:39,526 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:23:39,526 - modules.friend_request_window - INFO -    📱 手机号码: 17659905589
2025-07-30 10:23:39,527 - modules.friend_request_window - INFO -    🆔 准考证: 014325110137
2025-07-30 10:23:39,528 - modules.friend_request_window - INFO -    👤 姓名: 王兴磊
2025-07-30 10:23:39,528 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:23:39,529 - modules.friend_request_window - INFO -    📝 备注格式: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:39,530 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:23:39,530 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:39,531 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:23:39,532 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3541558, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:23:39,539 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3541558)
2025-07-30 10:23:39,539 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:23:39,540 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:23:39,540 - modules.friend_request_window - INFO - 🔄 激活窗口: 3541558
2025-07-30 10:23:40,243 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:23:40,243 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:23:40,244 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:23:40,244 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:23:40,245 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:23:40,245 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:23:40,245 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:23:40,245 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:23:40,246 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:23:40,246 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:23:40,246 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:23:40,246 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:23:40,247 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:23:40,247 - modules.friend_request_window - INFO -    📝 remark参数: '014325110137-王兴磊-2025-07-30 18:23:39' (类型: <class 'str'>, 长度: 36)
2025-07-30 10:23:40,247 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:23:40,247 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:40,248 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:23:40,248 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:23:40,248 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:23:40,248 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:23:40,249 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:23:40,249 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:23:40,250 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:23:41,171 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:23:46,498 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:23:46,499 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:23:46,499 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:23:46,500 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:23:46,500 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '智能坐标计...' (前50字符)
2025-07-30 10:23:46,809 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:23:46,812 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:23:47,715 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:23:47,725 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:23:47,725 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:23:47,726 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:23:47,727 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:23:47,728 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:23:48,229 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:23:48,230 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:23:48,230 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:23:48,230 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:23:48,230 - modules.friend_request_window - INFO -    📝 内容: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:48,231 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 10:23:48,231 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110137-\xe7\x8e\x8b\xe5\x85\xb4\xe7\xa3\x8a-2025-07-30 18:23:39'
2025-07-30 10:23:48,231 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:23:49,153 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:23:54,429 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:23:54,429 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:23:54,430 - modules.friend_request_window - INFO -    📝 原始文本: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:54,430 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 10:23:54,431 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '智能坐标计...' (前50字符)
2025-07-30 10:23:54,742 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:23:54,742 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:23:55,645 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:23:55,653 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:23:55,654 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:55,655 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:23:55,655 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:55,656 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 10:23:56,157 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110137-王兴磊-2025-07-30 18:23:39'
2025-07-30 10:23:56,157 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:23:56,157 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:23:56,158 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:23:56,158 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:23:56,158 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:23:56,158 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:23:56,959 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:23:56,959 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:23:56,960 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:23:57,570 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:23:57,571 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:23:57,571 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:23:57,571 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:23:58,073 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 10:23:58,075 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 10:23:58,075 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 10:23:58,075 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 10:23:58,075 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 10:23:58,076 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 10:23:58,076 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 10:23:58,076 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 10:23:58,076 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 10:23:58,077 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:23:58,077 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 10:23:58,077 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 10:23:58,078 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 10:23:58,078 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 10:23:58,080 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 10:23:58,081 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 10:23:58,081 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:23:58,082 - modules.friend_request_window - INFO - 📊 窗口 Weixin 错误次数: 1/3
2025-07-30 10:23:58,122 - modules.friend_request_window - INFO - 📊 窗口状态统计: 0/5 个窗口已失败
2025-07-30 10:23:58,123 - modules.friend_request_window - INFO - 🔄 步骤1: 点击错误提示窗口的'确定'按钮...
2025-07-30 10:23:58,123 - modules.friend_request_window - INFO - 🎯 使用固定坐标 (165, 220) 点击确定按钮...
2025-07-30 10:23:58,124 - modules.friend_request_window - INFO - 🎯 强制点击添加朋友窗口确定按钮...
2025-07-30 10:23:58,124 - modules.friend_request_window - INFO - 🔧 调试：开始执行强制点击方法
2025-07-30 10:23:58,126 - modules.friend_request_window - INFO - 🔍 发现错误对话框: Weixin (Qt51514QWindowIcon) 330x194
2025-07-30 10:23:58,127 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 10:23:58,128 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 10:23:58,129 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 10:23:58,130 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 10:23:58,139 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1936x1048
2025-07-30 10:23:58,145 - modules.friend_request_window - INFO - 📊 共发现 6 个可能的错误对话框
2025-07-30 10:23:58,145 - modules.friend_request_window - INFO - 🎯 基于窗口 Weixin 计算确定按钮位置: (1364, 274)
2025-07-30 10:23:58,146 - modules.friend_request_window - INFO - 🔧 调试：准备点击 5 个坐标位置
2025-07-30 10:23:58,146 - modules.friend_request_window - INFO - 🖱️ 尝试点击坐标 1/5: (1364, 274)
2025-07-30 10:23:58,146 - modules.friend_request_window - INFO - 🔧 调试：执行 pyautogui.click(1364, 274)
2025-07-30 10:23:59,071 - modules.friend_request_window - INFO - ✅ pyautogui点击坐标 (1364, 274) 完成
2025-07-30 10:23:59,571 - modules.friend_request_window - INFO - 🔧 调试：执行 win32api 点击(1364, 274)
