2025-07-30 11:09:44,732 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:09:44,732 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:09:44,733 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:09:44,734 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:09:44,734 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 11:09:44,735 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:09:44,735 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:09:44,736 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:09:44,736 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:09:44,737 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:09:44,738 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:09:44,741 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:09:44,743 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_110944.log
2025-07-30 11:09:44,744 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:09:44,745 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:09:44,745 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:09:44,746 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 11:09:44,746 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 11:09:44,747 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 19:09:44
2025-07-30 11:09:44,747 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 11:09:44,748 - __main__ - INFO - 📅 启动时间: 2025-07-30 19:09:44
2025-07-30 11:09:44,748 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 11:09:44,749 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:09:45,285 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:09:45,285 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:09:45,811 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:09:45,811 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:09:45,815 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:09:45,815 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 11:09:45,816 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 11:09:45,816 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 11:09:45,816 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 11:09:46,655 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 11:09:46,655 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 11:09:46,656 - __main__ - INFO - 📋 待处理联系人数: 2905
2025-07-30 11:09:46,657 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 11:09:46,657 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2905
2025-07-30 11:09:46,658 - __main__ - INFO - 
============================================================
2025-07-30 11:09:46,658 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 11:09:46,658 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:09:46,659 - __main__ - INFO - 📊 全局进度：已处理 0/2905 个联系人
2025-07-30 11:09:46,659 - __main__ - INFO - ============================================================
2025-07-30 11:09:46,660 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:09:46,660 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:09:46,663 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:09:46,664 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:09:46,666 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:09:46,988 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:09:46,988 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:09:46,988 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:09:46,989 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:09:46,989 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:09:46,989 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:09:46,990 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:09:46,990 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:09:46,990 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:09:46,991 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:09:47,193 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:09:47,193 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:09:47,193 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:09:47,194 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:09:47,498 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:09:47,499 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:09:47,499 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:09:47,500 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:09:47,500 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:09:47,500 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:09:47,501 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:09:47,501 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:09:47,502 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:09:47,502 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:09:47,704 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:09:47,706 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:09:47,708 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:09:48,009 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:09:48,010 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:09:48,010 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:09:48,010 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:09:48,010 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:09:48,011 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:09:48,011 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:09:48,011 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:09:49,012 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:09:49,012 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:09:49,013 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:09:49,013 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:09:49,013 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:09:49,014 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:09:49,014 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:09:49,014 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:09:49,215 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:09:49,216 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:09:51,599 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:09:51,600 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:09:51,600 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 11:09:53,732 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:09:53,933 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:09:53,934 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:09:56,303 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:09:56,303 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:09:56,304 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 11:09:57,873 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:09:58,073 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:09:58,074 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:10:00,448 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:10:00,449 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:10:00,449 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
