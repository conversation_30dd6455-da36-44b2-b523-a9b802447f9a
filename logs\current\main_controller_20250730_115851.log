2025-07-30 11:58:51,657 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:58:51,658 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 11:58:51,658 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:58:51,659 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:58:51,659 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:58:51,660 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:58:51,662 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 11:58:51,663 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:58:51,664 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:58:51,664 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:58:51,664 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:58:51,665 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:58:51,667 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:58:51,672 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_115851.log
2025-07-30 11:58:51,672 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:58:51,673 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:58:51,673 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:58:51,674 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 11:58:51,674 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 11:58:51,674 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 11:58:51,675 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 19:58:51
2025-07-30 11:58:51,676 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 11:58:51,677 - __main__ - INFO - 📅 启动时间: 2025-07-30 19:58:51
2025-07-30 11:58:51,678 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 11:58:51,679 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:58:52,209 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:58:52,210 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 11:58:52,739 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:58:52,739 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 11:58:52,740 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:58:52,741 - __main__ - INFO - ✅ 找到 2 个可用微信窗口
2025-07-30 11:58:52,741 - __main__ - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 11:58:52,741 - __main__ - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 11:58:52,741 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 11:58:53,639 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 11:58:53,640 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 11:58:53,640 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 11:58:53,641 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 11:58:53,641 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 11:58:53,641 - __main__ - INFO - 
============================================================
2025-07-30 11:58:53,641 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 11:58:53,642 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 11:58:53,642 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 11:58:53,642 - __main__ - INFO - ============================================================
2025-07-30 11:58:53,643 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:58:53,643 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 11:58:53,643 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:58:53,643 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:58:53,644 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 11:58:53,947 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:58:53,947 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:58:53,948 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:58:53,948 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:58:53,948 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:58:53,948 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:58:53,949 - modules.window_manager - INFO - 📏 当前窗口位置: (325, 151), 大小: 726x650
2025-07-30 11:58:53,949 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:58:53,949 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 11:58:54,254 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-30 11:58:54,254 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:58:54,254 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-30 11:58:54,255 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-30 11:58:54,255 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-30 11:58:54,255 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:58:54,458 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:58:54,458 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:58:54,459 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 11:58:54,459 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 11:58:54,763 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:58:54,763 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:58:54,764 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:58:54,764 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:58:54,765 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:58:54,765 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:58:54,765 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:58:54,766 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:58:54,766 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:58:54,766 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:58:54,969 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:58:54,969 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:58:54,973 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 11:58:55,275 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:58:55,275 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:58:55,276 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:58:55,276 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:58:55,276 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:58:55,276 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:58:55,277 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:58:55,277 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:58:56,278 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:58:56,278 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:58:56,279 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:58:56,279 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:58:56,279 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:58:56,279 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:58:56,280 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:58:56,280 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:58:56,482 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:58:56,483 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:58:58,867 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:58:58,868 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:58:58,869 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-30 11:59:00,402 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:59:00,603 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:59:00,604 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:59:02,985 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:59:02,985 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:59:02,985 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 11:59:05,330 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:59:05,531 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:59:05,532 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:59:07,917 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:59:07,918 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:59:07,918 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 11:59:09,542 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:59:09,743 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:59:09,743 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:59:12,111 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:59:12,111 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:59:12,112 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 11:59:13,820 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:59:14,021 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:59:14,022 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:59:16,400 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:59:16,401 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:59:16,401 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:59:16,401 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:59:16,402 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:59:16,403 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:59:16,404 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 11:59:16,405 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 460090, 进程: Weixin.exe)
2025-07-30 11:59:16,406 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:59:16,406 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 11:59:16,409 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:59:16,409 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 460090)
2025-07-30 11:59:16,410 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 460090) - 增强版
2025-07-30 11:59:16,713 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:59:16,714 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:59:16,714 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:59:16,714 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:59:16,715 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:59:16,715 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:59:16,919 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:59:16,919 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:59:17,121 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:59:17,121 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:59:17,123 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:59:17,124 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:59:17,124 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:59:17,124 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:59:17,124 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:59:18,125 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:59:18,125 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:59:18,127 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:59:18,127 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 11:59:18,128 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 460090, 进程: Weixin.exe)
2025-07-30 11:59:18,129 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:59:18,130 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 11:59:18,134 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:59:18,135 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:59:18,135 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:59:18,135 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:59:18,135 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 11:59:18,136 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 11:59:18,444 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:59:18,444 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:59:18,445 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:59:18,445 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:59:18,446 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:59:18,446 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:59:18,446 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:59:18,447 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:59:18,447 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:59:18,447 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:59:18,650 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:59:18,650 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:59:18,652 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 11:59:18,953 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:59:18,953 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:59:18,953 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:59:19,954 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:59:19,955 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 11:59:19,955 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:59:19,957 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_115919.log
2025-07-30 11:59:19,959 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:59:19,959 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:59:19,959 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:59:19,960 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 11:59:19,960 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:59:19,961 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:59:19,964 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 11:59:19,966 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 11:59:19,966 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:59:19,967 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:59:19,968 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 11:59:19,969 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 11:59:19,969 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:59:19,970 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:59:19,971 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 460090
2025-07-30 11:59:19,971 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 460090) - 增强版
2025-07-30 11:59:20,282 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:59:20,283 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:59:20,283 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:59:20,284 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:59:20,284 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:59:20,284 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:59:20,285 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:59:20,285 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:59:20,486 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:59:20,493 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:59:20,496 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 460090 (API返回: None)
2025-07-30 11:59:20,808 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:59:20,809 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:59:20,809 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:59:20,809 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:59:20,810 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:59:20,811 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:59:20,811 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:59:20,816 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:59:20,816 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:59:21,310 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:59:21,310 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:59:21,575 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2902 个
2025-07-30 11:59:21,576 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2902 个 (总计: 3135 个)
2025-07-30 11:59:21,577 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:59:21,577 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:59:21,578 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:59:21,578 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 11:59:21,578 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:59:21,579 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2902
2025-07-30 11:59:21,579 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18944948991 (周燕来)
2025-07-30 11:59:21,580 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:59:21,580 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 11:59:28,165 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18944948991
2025-07-30 11:59:28,165 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:59:28,166 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18944948991 执行添加朋友操作...
2025-07-30 11:59:28,166 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:59:28,166 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:59:28,167 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:59:28,168 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:59:28,172 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:59:28,173 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:59:28,174 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:59:28,175 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:59:28,175 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:59:28,176 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:59:28,176 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:59:28,176 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:59:28,181 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:59:28,183 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:59:28,184 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:59:28,185 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:59:28,192 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 11:59:28,198 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:59:28,701 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:59:28,702 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:59:28,772 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差43.02, 边缘比例0.0518
2025-07-30 11:59:28,780 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_115928.png
2025-07-30 11:59:28,782 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:59:28,783 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:59:28,789 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:59:28,791 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:59:28,792 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:59:28,799 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_115928.png
2025-07-30 11:59:28,801 - WeChatAutoAdd - INFO - 底部区域原始检测到 43 个轮廓
2025-07-30 11:59:28,802 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 11:59:28,803 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 11:59:28,805 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 11:59:28,808 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 11:59:28,809 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 11:59:28,810 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(247,280), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,279), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:59:28,813 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,278), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,813 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,278), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 11:59:28,814 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,275), 尺寸9x4, 长宽比2.25, 面积36
2025-07-30 11:59:28,816 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,271), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:59:28,817 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(234,270), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,818 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 11:59:28,819 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(206,258), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 11:59:28,820 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(204,258), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,821 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,258), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 11:59:28,822 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:59:28,823 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(257,257), 尺寸5x2, 长宽比2.50, 面积10
2025-07-30 11:59:28,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,257), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 11:59:28,826 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,829 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 11:59:28,837 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,838 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:59:28,839 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:59:28,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,842 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(260,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:59:28,843 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,851 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,852 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:59:28,853 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(203,252), 尺寸20x5, 长宽比4.00, 面积100
2025-07-30 11:59:28,854 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,252), 尺寸16x5, 长宽比3.20, 面积80
2025-07-30 11:59:28,855 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,251), 尺寸35x32, 长宽比1.09, 面积1120
2025-07-30 11:59:28,856 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,251), 尺寸35x33, 长宽比1.06, 面积1155
2025-07-30 11:59:28,857 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:59:28,859 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,250), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 11:59:28,860 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,250), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 11:59:28,860 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,250), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 11:59:28,863 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(143,250), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 11:59:28,871 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(137,250), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 11:59:28,872 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:59:28,874 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 11:59:28,875 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 11:59:28,879 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 11:59:28,886 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,241), 尺寸9x3, 长宽比3.00, 面积27
2025-07-30 11:59:28,887 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,230), 尺寸8x13, 长宽比0.62, 面积104
2025-07-30 11:59:28,887 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸66x14, 长宽比4.71, 面积924
2025-07-30 11:59:28,889 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=95.6 (阈值:60)
2025-07-30 11:59:28,890 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:59:28,896 - WeChatAutoAdd - INFO - 底部区域找到 7 个按钮候选
2025-07-30 11:59:28,898 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 11:59:28,899 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 11:59:28,902 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 11:59:28,913 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_115928.png
2025-07-30 11:59:28,916 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 11:59:28,919 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:59:29,221 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 11:59:29,990 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:59:29,991 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:59:29,992 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:59:29,992 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 11:59:29,993 - modules.wechat_auto_add_simple - INFO - ✅ 18944948991 添加朋友操作执行成功
2025-07-30 11:59:29,994 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:59:29,994 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 11:59:29,997 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:59:31,999 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:59:31,999 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:59:32,000 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:59:32,000 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:59:32,000 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:59:32,001 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:59:32,001 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:59:32,001 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:59:32,001 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:59:32,002 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18944948991
2025-07-30 11:59:32,005 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:59:32,006 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:59:32,006 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:59:32,007 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:59:32,008 - modules.friend_request_window - INFO -    📱 phone: '18944948991'
2025-07-30 11:59:32,008 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:59:32,009 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:59:32,517 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:59:32,518 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:59:32,518 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:59:32,519 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:59:32,520 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18944948991
2025-07-30 11:59:32,521 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:59:32,521 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:59:32,522 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:59:32,523 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:59:32,523 - modules.friend_request_window - INFO -    📱 手机号码: 18944948991
2025-07-30 11:59:32,526 - modules.friend_request_window - INFO -    🆔 准考证: 014325110147
2025-07-30 11:59:32,526 - modules.friend_request_window - INFO -    👤 姓名: 周燕来
2025-07-30 11:59:32,527 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:59:32,528 - modules.friend_request_window - INFO -    📝 备注格式: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:32,529 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:59:32,530 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:32,531 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:59:32,537 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1769694, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:59:32,539 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1769694)
2025-07-30 11:59:32,540 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:59:32,540 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:59:32,541 - modules.friend_request_window - INFO - 🔄 激活窗口: 1769694
2025-07-30 11:59:33,242 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:59:33,243 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:59:33,243 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:59:33,244 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:59:33,244 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:59:33,244 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:59:33,246 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:59:33,246 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:59:33,247 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:59:33,247 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:59:33,248 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:59:33,249 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:59:33,249 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:59:33,250 - modules.friend_request_window - INFO -    📝 remark参数: '014325110147-周燕来-2025-07-30 19:59:32' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:59:33,250 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:59:33,250 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:33,251 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:59:33,251 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:59:33,251 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:59:33,251 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:59:33,252 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:59:33,252 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:59:33,252 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:59:34,165 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:59:39,411 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:59:39,412 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:59:39,412 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:59:39,412 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:59:39,413 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '...' (前50字符)
2025-07-30 11:59:39,724 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:59:39,725 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:59:40,627 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:59:40,628 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:59:40,628 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:59:40,629 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:59:40,629 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:59:41,130 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:59:41,130 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:59:41,131 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:59:41,131 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:59:41,131 - modules.friend_request_window - INFO -    📝 内容: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:41,131 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:59:41,132 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110147-\xe5\x91\xa8\xe7\x87\x95\xe6\x9d\xa5-2025-07-30 19:59:32'
2025-07-30 11:59:41,132 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:59:42,049 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:59:47,295 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:59:47,295 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:59:47,295 - modules.friend_request_window - INFO -    📝 原始文本: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:47,296 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:59:47,296 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:59:47,605 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:59:47,605 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:59:48,508 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:59:48,518 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:59:48,519 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:48,519 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:59:48,520 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:48,520 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:59:49,021 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 11:59:49,021 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:59:49,022 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:59:49,022 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:59:49,022 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:59:49,022 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:59:49,023 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:59:49,824 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:59:49,824 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:59:49,824 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:59:50,432 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:59:50,433 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 11:59:50,433 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:59:50,433 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:59:50,434 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:59:50,935 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 11:59:50,938 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 11:59:50,938 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 11:59:50,938 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 11:59:50,939 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 11:59:50,939 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 11:59:50,939 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 11:59:50,939 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 11:59:50,940 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 11:59:50,940 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:59:50,940 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 11:59:50,940 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 11:59:50,941 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 11:59:50,942 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 11:59:50,942 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 11:59:50,943 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 11:59:50,944 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:59:50,944 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 11:59:50,949 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 11:59:50,951 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 11:59:50,952 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 11:59:51,455 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 11:59:51,455 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 11:59:51,455 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 11:59:51,456 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 11:59:51,456 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 11:59:51,456 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 11:59:51,457 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 11:59:51,457 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 11:59:52,366 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 11:59:52,367 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 11:59:52,367 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 11:59:52,368 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 11:59:52,444 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 11:59:52,449 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:59:53,250 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:59:53,251 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 11:59:53,251 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 11:59:53,251 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 11:59:53,252 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 11:59:53,252 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:59:54,053 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:59:54,054 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 11:59:54,054 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 11:59:56,055 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 11:59:56,056 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:59:56,056 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 11:59:56,057 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:59:56,058 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:59:56,059 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 11:59:56,061 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 11:59:56,062 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 11:59:56,064 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 11:59:56,085 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 11:59:56,085 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 11:59:56,086 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 11:59:56,086 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 263568
2025-07-30 11:59:56,087 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:59:56,088 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 11:59:56,089 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 11:59:56,616 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:59:56,617 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:59:56,617 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:59:56,618 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:59:56,618 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:59:56,618 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:59:56,619 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:59:56,619 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:59:56,619 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:59:56,620 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:59:56,822 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:59:56,823 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:59:56,823 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 11:59:56,823 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 11:59:56,824 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 11:59:56,824 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 11:59:56,824 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 11:59:58,825 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 11:59:58,826 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:59:58,826 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:59:58,827 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 11:59:58,827 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 11:59:58,827 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 11:59:58,827 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:59:58,828 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:59:58,828 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:59:58,828 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:59:58,829 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:59:58,829 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:59:59,030 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:59:59,030 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:00:01,414 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:00:01,415 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:00:01,415 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 12:00:03,504 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:00:03,705 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:00:03,706 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:00:06,075 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:00:06,075 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:00:06,076 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 12:00:08,056 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:00:08,257 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:00:08,257 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:00:10,630 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:00:10,630 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:00:10,631 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-30 12:00:13,590 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:00:13,791 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:00:13,791 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:00:16,160 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:00:16,160 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:00:16,161 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 12:00:18,195 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:00:18,396 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:00:18,397 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:00:20,779 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:00:20,780 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:00:20,780 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:00:20,781 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:00:20,781 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:00:20,782 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:00:20,783 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:00:20,784 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:00:20,785 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 525626, 进程: Weixin.exe)
2025-07-30 12:00:20,785 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:00:20,787 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:00:20,792 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:00:20,793 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 525626)
2025-07-30 12:00:20,794 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525626) - 增强版
2025-07-30 12:00:21,099 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:00:21,099 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:00:21,100 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:00:21,100 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:00:21,101 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 12:00:21,101 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:00:21,305 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 12:00:21,305 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:00:21,507 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:00:21,508 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:00:21,508 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 12:00:21,508 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 12:00:21,508 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 12:00:21,509 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:00:21,509 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 12:00:22,510 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 12:00:22,511 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:00:22,511 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:00:22,513 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:00:22,513 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:00:22,514 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 525626, 进程: Weixin.exe)
2025-07-30 12:00:22,515 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:00:22,515 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:00:22,519 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:00:22,520 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 12:00:22,520 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 12:00:22,520 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 12:00:22,521 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 12:00:22,521 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 12:00:22,522 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 12:00:23,525 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 12:00:23,526 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 12:00:23,526 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 12:00:23,527 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 12:00:23,527 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 12:00:23,527 - modules.friend_request_window - INFO - 🔧 开始移动窗口 525626 到目标位置...
2025-07-30 12:00:24,332 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 12:00:24,332 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 12:00:24,332 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 12:00:24,333 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 12:00:24,333 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 12:00:24,333 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 12:00:24,334 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 12:00:24,334 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 12:00:24,334 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 12:00:24,334 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 12:00:24,335 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 12:00:24,335 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 12:00:24,335 - modules.friend_request_window - INFO -    📝 备注信息: '014325110147-周燕来-2025-07-30 19:59:32'
2025-07-30 12:00:24,836 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 12:00:24,838 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:00:24,838 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:00:24,838 - modules.wechat_auto_add_simple - INFO - ✅ 18944948991 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 12:00:24,838 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18944948991
2025-07-30 12:00:24,840 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:00:24,840 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:00:28,813 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2902
2025-07-30 12:00:28,814 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13580982528 (钟建强)
2025-07-30 12:00:28,815 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:00:28,815 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:00:35,391 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13580982528
2025-07-30 12:00:35,391 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 12:00:35,391 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13580982528 执行添加朋友操作...
2025-07-30 12:00:35,392 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 12:00:35,392 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 12:00:35,393 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:00:35,394 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 12:00:35,399 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 12:00:35,401 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 12:00:35,402 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 12:00:35,402 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 12:00:35,402 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 12:00:35,403 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 12:00:35,404 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 12:00:35,404 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 12:00:35,415 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:00:35,420 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 12:00:35,423 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:00:35,427 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 12:00:35,433 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 12:00:35,436 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 12:00:35,939 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 12:00:35,941 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 12:00:36,022 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差46.21, 边缘比例0.0618
2025-07-30 12:00:36,032 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_120036.png
2025-07-30 12:00:36,034 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 12:00:36,036 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 12:00:36,038 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 12:00:36,042 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 12:00:36,048 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 12:00:36,054 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_120036.png
2025-07-30 12:00:36,060 - WeChatAutoAdd - INFO - 底部区域原始检测到 51 个轮廓
2025-07-30 12:00:36,064 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,321), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 12:00:36,066 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=321 (距底部154像素区域)
2025-07-30 12:00:36,068 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 12:00:36,069 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,321), 尺寸128x30
2025-07-30 12:00:36,071 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,321), 尺寸128x30
2025-07-30 12:00:36,072 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,273), 尺寸6x6, 长宽比1.00, 面积36
2025-07-30 12:00:36,074 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,272), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,077 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,270), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 12:00:36,083 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,258), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 12:00:36,085 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,257), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:00:36,087 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,256), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 12:00:36,088 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,256), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,090 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,255), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:00:36,094 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 12:00:36,098 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,254), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 12:00:36,100 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,101 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,103 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,252), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,106 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,252), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:00:36,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,120 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,123 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 12:00:36,131 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,250), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:00:36,133 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,248), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 12:00:36,137 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,248), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 12:00:36,139 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,248), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 12:00:36,140 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:00:36,148 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:00:36,150 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=65.3 (阈值:60)
2025-07-30 12:00:36,151 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:00:36,153 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=66.7 (阈值:60)
2025-07-30 12:00:36,155 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,247), 尺寸34x35, 长宽比0.97, 面积1190
2025-07-30 12:00:36,157 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,240), 尺寸11x3, 长宽比3.67, 面积33
2025-07-30 12:00:36,160 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:00:36,165 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:00:36,168 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,239), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:00:36,169 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:00:36,170 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,239), 尺寸5x2, 长宽比2.50, 面积10
2025-07-30 12:00:36,172 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,173 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,176 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,238), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:00:36,181 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,235), 尺寸1x7, 长宽比0.14, 面积7
2025-07-30 12:00:36,182 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,235), 尺寸3x8, 长宽比0.38, 面积24
2025-07-30 12:00:36,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,234), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:00:36,185 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,233), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:00:36,186 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,233), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:00:36,189 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,232), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 12:00:36,190 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,231), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 12:00:36,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(223,230), 尺寸12x12, 长宽比1.00, 面积144
2025-07-30 12:00:36,202 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,230), 尺寸11x11, 长宽比1.00, 面积121
2025-07-30 12:00:36,204 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=77.7 (阈值:60)
2025-07-30 12:00:36,206 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(265,229), 尺寸14x14, 长宽比1.00, 面积196
2025-07-30 12:00:36,212 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,229), 尺寸28x15, 长宽比1.87, 面积420
2025-07-30 12:00:36,215 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,229), 尺寸26x14, 长宽比1.86, 面积364
2025-07-30 12:00:36,219 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=98.7 (阈值:60)
2025-07-30 12:00:36,221 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,229), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 12:00:36,222 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,229), 尺寸5x5, 长宽比1.00, 面积25
2025-07-30 12:00:36,223 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 12:00:36,226 - WeChatAutoAdd - INFO - 底部区域找到 7 个按钮候选
2025-07-30 12:00:36,231 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=321, 很可能是'添加到通讯录'按钮
2025-07-30 12:00:36,232 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 336), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 12:00:36,233 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 12:00:36,242 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_120036.png
2025-07-30 12:00:36,247 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 12:00:36,249 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 12:00:36,552 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 336) -> 屏幕坐标(1364, 336)
2025-07-30 12:00:37,321 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 12:00:37,322 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 12:00:37,324 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:00:37,326 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:00:37,326 - modules.wechat_auto_add_simple - INFO - ✅ 13580982528 添加朋友操作执行成功
2025-07-30 12:00:37,328 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:00:37,329 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:00:37,329 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 12:00:39,331 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 申请添加朋友
2025-07-30 12:00:39,332 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 12:00:39,332 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 12:00:39,333 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:00:39,333 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:00:39,333 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:00:39,334 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:00:39,334 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:00:39,335 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 12:00:39,335 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13580982528
2025-07-30 12:00:39,336 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 12:00:39,337 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:00:39,338 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:00:39,339 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 12:00:39,341 - modules.friend_request_window - INFO -    📱 phone: '13580982528'
2025-07-30 12:00:39,343 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 12:00:39,346 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 12:00:40,059 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 12:00:40,059 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 12:00:40,060 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 12:00:40,060 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:00:40,062 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13580982528
2025-07-30 12:00:40,063 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 12:00:40,064 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:00:40,064 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 12:00:40,067 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 12:00:40,068 - modules.friend_request_window - INFO -    📱 手机号码: 13580982528
2025-07-30 12:00:40,071 - modules.friend_request_window - INFO -    🆔 准考证: 014325110149
2025-07-30 12:00:40,077 - modules.friend_request_window - INFO -    👤 姓名: 钟建强
2025-07-30 12:00:40,078 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 12:00:40,079 - modules.friend_request_window - INFO -    📝 备注格式: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:40,079 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:00:40,080 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:40,081 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 12:00:40,082 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 460042, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 12:00:40,085 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 460042)
2025-07-30 12:00:40,087 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 12:00:40,087 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 12:00:40,088 - modules.friend_request_window - INFO - 🔄 激活窗口: 460042
2025-07-30 12:00:40,790 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 12:00:40,791 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 12:00:40,792 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 12:00:40,792 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 12:00:40,792 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 12:00:40,793 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:00:40,793 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 12:00:40,793 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:00:40,794 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 12:00:40,794 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 12:00:40,794 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 12:00:40,795 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 12:00:40,795 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 12:00:40,795 - modules.friend_request_window - INFO -    📝 remark参数: '014325110149-钟建强-2025-07-30 20:00:40' (类型: <class 'str'>, 长度: 36)
2025-07-30 12:00:40,796 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 12:00:40,796 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:40,798 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 12:00:40,799 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 12:00:40,799 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 12:00:40,801 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 12:00:40,802 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 12:00:40,805 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 12:00:40,806 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 12:00:41,730 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 12:00:46,973 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 12:00:46,973 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 12:00:46,974 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 12:00:46,974 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 12:00:46,975 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 12:00:47,284 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:00:47,284 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:00:48,187 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:00:48,196 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:00:48,197 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 12:00:48,197 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 12:00:48,197 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 12:00:48,198 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 12:00:48,699 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 12:00:48,699 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 12:00:48,699 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 12:00:48,700 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 12:00:48,700 - modules.friend_request_window - INFO -    📝 内容: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:48,700 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 12:00:48,701 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110149-\xe9\x92\x9f\xe5\xbb\xba\xe5\xbc\xba-2025-07-30 20:00:40'
2025-07-30 12:00:48,701 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 12:00:49,610 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 12:00:54,858 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 12:00:54,859 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 12:00:54,859 - modules.friend_request_window - INFO -    📝 原始文本: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:54,859 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 12:00:54,860 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 12:00:55,170 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:00:55,170 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:00:56,073 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:00:56,082 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:00:56,082 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:56,083 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 12:00:56,083 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:56,084 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 12:00:56,585 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110149-钟建强-2025-07-30 20:00:40'
2025-07-30 12:00:56,585 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 12:00:56,585 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 12:00:56,586 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 12:00:56,586 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 12:00:56,586 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 12:00:56,587 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 12:00:57,387 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 12:00:57,388 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 12:00:57,388 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 12:00:57,993 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:00:57,994 - modules.friend_request_window - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:00:57,994 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 12:00:57,994 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 12:00:57,995 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 12:00:58,496 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 12:00:58,499 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 12:00:58,499 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 12:00:58,499 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 12:00:58,500 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 12:00:58,500 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 12:00:58,500 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 12:00:58,500 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 12:00:58,501 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 12:00:58,501 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 12:00:58,501 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 12:00:58,502 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 12:00:58,502 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 12:00:58,503 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 12:00:58,503 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 12:00:58,504 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 12:00:58,504 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 12:00:58,505 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 12:00:58,513 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 12:00:58,513 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 12:00:58,513 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 12:00:59,015 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 12:00:59,016 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 12:00:59,016 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 12:00:59,016 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 12:00:59,017 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 12:00:59,017 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 12:00:59,017 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 12:00:59,017 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 12:00:59,927 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 12:00:59,928 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 12:00:59,928 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 12:00:59,929 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 12:00:59,946 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 12:00:59,946 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 12:01:00,747 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 12:01:00,747 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 12:01:00,749 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 12:01:00,749 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 12:01:00,750 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 12:01:00,750 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 12:01:01,551 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 12:01:01,551 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 12:01:01,552 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 12:01:03,552 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 12:01:03,554 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:01:03,554 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 12:01:03,554 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:01:03,556 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:01:03,557 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:01:03,559 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 12:01:03,559 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 12:01:03,559 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 12:01:03,580 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 12:01:03,580 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 12:01:03,581 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 12:01:03,581 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 263568
2025-07-30 12:01:03,582 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:01:03,583 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:01:03,904 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:01:03,905 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:01:03,905 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:01:03,907 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:01:03,908 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:01:03,908 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:01:03,909 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:01:03,909 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:01:03,909 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:01:03,909 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:01:04,111 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:01:04,112 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:01:04,112 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 12:01:04,112 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 12:01:04,113 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 12:01:04,113 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 12:01:04,113 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 12:01:06,114 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 12:01:06,115 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:01:06,115 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:01:06,115 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 12:01:06,116 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 12:01:06,116 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 12:01:06,116 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:01:06,117 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:01:06,117 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:01:06,117 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:01:06,118 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:01:06,118 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:01:06,319 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:01:06,319 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:01:08,693 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:01:08,694 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:01:08,694 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 12:01:11,662 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:01:12,137 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
