2025-07-30 11:28:43,623 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:28:43,624 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:28:43,624 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:28:43,625 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:28:43,626 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 11:28:43,626 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:28:43,627 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:28:43,628 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:28:43,628 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:28:43,629 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:28:43,629 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:28:43,631 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:28:43,633 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_112843.log
2025-07-30 11:28:43,634 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:28:43,635 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:28:43,635 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:28:43,636 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 11:28:43,636 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 11:28:43,636 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 19:28:43
2025-07-30 11:28:43,636 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 11:28:43,637 - __main__ - INFO - 📅 启动时间: 2025-07-30 19:28:43
2025-07-30 11:28:43,637 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 11:28:43,637 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:28:44,185 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:28:44,185 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:28:44,729 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:28:44,729 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:28:44,731 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:28:44,732 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 11:28:44,732 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 11:28:44,732 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 11:28:44,733 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 11:28:45,603 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 11:28:45,603 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 11:28:45,603 - __main__ - INFO - 📋 待处理联系人数: 2905
2025-07-30 11:28:45,604 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 11:28:45,604 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2905
2025-07-30 11:28:45,604 - __main__ - INFO - 
============================================================
2025-07-30 11:28:45,605 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 11:28:45,605 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:28:45,605 - __main__ - INFO - 📊 全局进度：已处理 0/2905 个联系人
2025-07-30 11:28:45,605 - __main__ - INFO - ============================================================
2025-07-30 11:28:45,606 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:28:45,606 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:28:45,606 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:28:45,607 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:28:45,607 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:28:45,911 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:28:45,912 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:28:45,912 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:28:45,912 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:28:45,913 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:28:45,913 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:28:45,913 - modules.window_manager - INFO - 📏 当前窗口位置: (374, 122), 大小: 726x650
2025-07-30 11:28:45,914 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:28:45,914 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 11:28:46,217 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-30 11:28:46,218 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:28:46,218 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-30 11:28:46,218 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-30 11:28:46,218 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-30 11:28:46,219 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:28:46,420 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:28:46,421 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:28:46,421 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:28:46,421 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:28:46,725 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:28:46,725 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:28:46,726 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:28:46,726 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:28:46,726 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:28:46,727 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:28:46,727 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:28:46,727 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:28:46,728 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:28:46,728 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:28:46,930 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:28:46,930 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:28:46,932 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:28:47,233 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:28:47,234 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:28:47,234 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:28:47,234 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:28:47,234 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:28:47,235 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:28:47,235 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:28:47,235 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:28:48,236 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:28:48,236 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:28:48,237 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:28:48,237 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:28:48,237 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:28:48,237 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:28:48,238 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:28:48,238 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:28:48,439 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:28:48,440 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:28:50,812 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:28:50,813 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:28:50,813 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 11:28:52,462 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:28:52,663 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:28:52,663 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:28:55,046 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:28:55,046 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:28:55,047 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 11:28:57,226 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:28:57,427 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:28:57,427 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:28:59,811 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:28:59,812 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:28:59,812 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 11:29:02,666 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:29:02,867 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:29:02,867 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:29:05,245 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:29:05,245 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:29:05,245 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-30 11:29:06,784 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:29:06,985 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:29:06,986 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:29:09,363 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:29:09,363 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:29:09,364 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:29:09,364 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:29:09,364 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:29:09,366 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:29:09,366 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:29:09,367 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:29:09,367 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:29:09,368 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:29:09,370 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:29:09,371 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1900602)
2025-07-30 11:29:09,371 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1900602) - 增强版
2025-07-30 11:29:09,675 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:29:09,676 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:29:09,676 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:29:09,677 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:29:09,677 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:29:09,677 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:29:09,881 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:29:09,881 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:29:10,083 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:29:10,083 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:29:10,084 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:29:10,084 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:29:10,084 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:29:10,084 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:29:10,085 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:29:11,086 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:29:11,086 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:29:11,087 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:29:11,088 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:29:11,088 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:29:11,089 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:29:11,090 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:29:11,093 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:29:11,094 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:29:11,094 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:29:11,094 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:29:11,095 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:29:11,095 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:29:11,402 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:29:11,402 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:29:11,403 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:29:11,403 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:29:11,403 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:29:11,404 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:29:11,404 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:29:11,404 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:29:11,405 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:29:11,405 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:29:11,606 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:29:11,606 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:29:11,608 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:29:11,908 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:29:11,909 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:29:11,909 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:29:12,910 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:29:12,910 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 11:29:12,910 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:29:12,913 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_112912.log
2025-07-30 11:29:12,914 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:29:12,914 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:29:12,914 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:29:12,915 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 11:29:12,915 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:29:12,916 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:29:12,918 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 11:29:12,918 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 11:29:12,918 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:29:12,919 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:29:12,919 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 11:29:12,919 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 11:29:12,919 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:29:12,921 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:29:12,921 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1900602
2025-07-30 11:29:12,921 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1900602) - 增强版
2025-07-30 11:29:13,229 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:29:13,230 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:29:13,230 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:29:13,230 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:29:13,231 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:29:13,231 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:29:13,231 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:29:13,232 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:29:13,434 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:29:13,434 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:29:13,437 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1900602 (API返回: None)
2025-07-30 11:29:13,738 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:29:13,739 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:29:13,739 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:29:13,739 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:29:13,741 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:29:13,741 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:29:13,742 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:29:13,746 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:29:13,746 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:29:14,234 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:29:14,235 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:29:14,483 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2912 个
2025-07-30 11:29:14,484 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2912 个 (总计: 3135 个)
2025-07-30 11:29:14,485 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:29:14,485 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:29:14,485 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:29:14,486 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:29:14,486 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2912
2025-07-30 11:29:14,486 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18263399923 (成宇鑫)
2025-07-30 11:29:14,486 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:29:21,055 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18263399923
2025-07-30 11:29:21,056 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:29:21,056 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18263399923 执行添加朋友操作...
2025-07-30 11:29:21,056 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:29:21,057 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:29:21,057 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:29:21,058 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:29:21,062 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:29:21,063 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:29:21,064 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:29:21,065 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:29:21,065 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:29:21,066 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:29:21,067 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:29:21,067 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:29:21,072 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:29:21,077 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:29:21,080 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:29:21,083 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:29:21,086 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 11:29:21,087 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:29:21,589 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:29:21,590 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:29:21,665 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差34.56, 边缘比例0.0376
2025-07-30 11:29:21,672 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_112921.png
2025-07-30 11:29:21,674 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:29:21,676 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:29:21,678 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:29:21,680 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:29:21,681 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:29:21,685 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_112921.png
2025-07-30 11:29:21,690 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:29:21,692 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:29:21,694 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:29:21,696 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:29:21,698 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:29:21,699 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:29:21,701 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:29:21,703 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:29:21,712 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_112921.png
2025-07-30 11:29:21,715 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:29:21,716 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:29:21,723 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_112921.png
2025-07-30 11:29:21,783 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:29:21,784 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:29:21,785 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:29:21,786 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:29:22,087 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:29:22,860 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:29:22,861 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:29:22,863 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:29:22,863 - modules.wechat_auto_add_simple - INFO - ✅ 18263399923 添加朋友操作执行成功
2025-07-30 11:29:22,864 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:29:22,864 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:29:24,866 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:29:24,866 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:29:24,867 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:29:24,867 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:29:24,868 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:29:24,868 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:29:24,868 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:29:24,869 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:29:24,869 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:29:24,869 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18263399923
2025-07-30 11:29:24,873 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:29:24,873 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:29:24,874 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:29:24,874 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:29:24,874 - modules.friend_request_window - INFO -    📱 phone: '18263399923'
2025-07-30 11:29:24,875 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:29:24,876 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:29:25,372 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:29:25,373 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:29:25,373 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:29:25,374 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:29:25,375 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18263399923
2025-07-30 11:29:25,376 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:29:25,376 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:29:25,377 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:29:25,378 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:29:25,378 - modules.friend_request_window - INFO -    📱 手机号码: 18263399923
2025-07-30 11:29:25,379 - modules.friend_request_window - INFO -    🆔 准考证: 014325110144
2025-07-30 11:29:25,380 - modules.friend_request_window - INFO -    👤 姓名: 成宇鑫
2025-07-30 11:29:25,380 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:29:25,381 - modules.friend_request_window - INFO -    📝 备注格式: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:25,381 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:29:25,381 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:25,382 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:29:25,384 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 6425348, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:29:25,389 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 6425348)
2025-07-30 11:29:25,390 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:29:25,391 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:29:25,392 - modules.friend_request_window - INFO - 🔄 激活窗口: 6425348
2025-07-30 11:29:26,096 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:29:26,096 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:29:26,097 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:29:26,097 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:29:26,098 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:29:26,098 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:29:26,098 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:29:26,098 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:29:26,099 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:29:26,099 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:29:26,099 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:29:26,099 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:29:26,100 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:29:26,100 - modules.friend_request_window - INFO -    📝 remark参数: '014325110144-成宇鑫-2025-07-30 19:29:25' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:29:26,100 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:29:26,100 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:26,101 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:29:26,101 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:29:26,101 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:29:26,101 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:29:26,102 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:29:26,102 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:29:26,103 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:29:27,015 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:29:32,259 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:29:32,259 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:29:32,260 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:29:32,260 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:29:32,261 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '...' (前50字符)
2025-07-30 11:29:32,573 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:29:32,573 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:29:33,476 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:29:33,477 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:29:33,477 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:29:33,477 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:29:33,478 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:29:33,979 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:29:33,979 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:29:33,979 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:29:33,980 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:29:33,980 - modules.friend_request_window - INFO -    📝 内容: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:33,980 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:29:33,980 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110144-\xe6\x88\x90\xe5\xae\x87\xe9\x91\xab-2025-07-30 19:29:25'
2025-07-30 11:29:33,981 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:29:34,894 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:29:40,135 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:29:40,136 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:29:40,136 - modules.friend_request_window - INFO -    📝 原始文本: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:40,139 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:29:40,140 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:29:40,451 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:29:40,452 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:29:41,355 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:29:41,364 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:29:41,365 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:41,365 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:29:41,366 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:41,366 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:29:41,867 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:41,868 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:29:41,868 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:29:41,868 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:29:41,868 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:29:41,869 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:29:41,869 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:29:42,670 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:29:42,670 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:29:42,670 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:29:43,280 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:29:43,281 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:29:43,281 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:29:43,282 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:29:43,802 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:44,034 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:44,267 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:44,499 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:44,730 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:44,962 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:45,196 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:45,434 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:45,666 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:45,901 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:46,132 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:46,364 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:46,595 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:46,829 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:47,063 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:47,295 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:47,528 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:47,759 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:47,991 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:48,222 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:29:48,437 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:29:48,438 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:29:49,438 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:29:49,440 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:29:49,440 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:29:49,441 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:29:49,441 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:29:49,441 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:29:49,441 - modules.friend_request_window - INFO -    📝 备注信息: '014325110144-成宇鑫-2025-07-30 19:29:25'
2025-07-30 11:29:49,942 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:29:49,943 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:29:49,943 - modules.wechat_auto_add_simple - INFO - ✅ 18263399923 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:29:49,944 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18263399923
2025-07-30 11:29:49,945 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:29:53,652 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2912
2025-07-30 11:29:53,653 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13688506431 (麻耀峰)
2025-07-30 11:29:53,654 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:30:00,233 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13688506431
2025-07-30 11:30:00,234 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:30:00,235 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13688506431 执行添加朋友操作...
2025-07-30 11:30:00,235 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:30:00,235 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:30:00,236 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:30:00,237 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:30:00,242 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:30:00,243 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:30:00,244 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:30:00,245 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:30:00,245 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:30:00,246 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:30:00,246 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:30:00,247 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:30:00,255 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:30:00,259 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:30:00,261 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:30:00,268 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 11:30:00,270 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:30:00,775 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:30:00,776 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:30:00,842 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差35.23, 边缘比例0.0390
2025-07-30 11:30:00,851 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113000.png
2025-07-30 11:30:00,855 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:30:00,862 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:30:00,863 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:30:00,865 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:30:00,868 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:30:00,873 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113000.png
2025-07-30 11:30:00,877 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:30:00,879 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:30:00,880 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:30:00,882 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:30:00,883 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:30:00,884 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:30:00,885 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:30:00,887 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:30:00,900 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113000.png
2025-07-30 11:30:00,902 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:30:00,903 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:30:00,911 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113000.png
2025-07-30 11:30:00,935 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:30:00,945 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:30:00,949 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:30:00,952 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:30:01,254 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:30:02,024 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:30:02,025 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:30:02,026 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:30:02,026 - modules.wechat_auto_add_simple - INFO - ✅ 13688506431 添加朋友操作执行成功
2025-07-30 11:30:02,026 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:30:02,027 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:30:04,029 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:30:04,029 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:30:04,030 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:30:04,030 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:30:04,030 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:30:04,030 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:30:04,031 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:30:04,031 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:30:04,031 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:30:04,031 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13688506431
2025-07-30 11:30:04,032 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:30:04,032 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:30:04,033 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:30:04,033 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:30:04,033 - modules.friend_request_window - INFO -    📱 phone: '13688506431'
2025-07-30 11:30:04,033 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:30:04,034 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:30:04,646 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:30:04,647 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:30:04,647 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:30:04,648 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:30:04,649 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13688506431
2025-07-30 11:30:04,649 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:30:04,650 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:30:04,650 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:30:04,651 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:30:04,651 - modules.friend_request_window - INFO -    📱 手机号码: 13688506431
2025-07-30 11:30:04,651 - modules.friend_request_window - INFO -    🆔 准考证: 014325110145
2025-07-30 11:30:04,652 - modules.friend_request_window - INFO -    👤 姓名: 麻耀峰
2025-07-30 11:30:04,652 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:30:04,653 - modules.friend_request_window - INFO -    📝 备注格式: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:04,654 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:30:04,655 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:04,655 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:30:04,656 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4130366, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:30:04,658 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4130366)
2025-07-30 11:30:04,659 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:30:04,659 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:30:04,660 - modules.friend_request_window - INFO - 🔄 激活窗口: 4130366
2025-07-30 11:30:05,363 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:30:05,363 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:30:05,364 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:30:05,364 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:30:05,364 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:30:05,364 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:30:05,365 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:30:05,365 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:30:05,365 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:30:05,365 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:30:05,366 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:30:05,366 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:30:05,366 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:30:05,367 - modules.friend_request_window - INFO -    📝 remark参数: '014325110145-麻耀峰-2025-07-30 19:30:04' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:30:05,367 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:30:05,367 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:05,367 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:30:05,368 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:30:05,368 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:30:05,369 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:30:05,369 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:30:05,369 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:30:05,370 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:30:06,292 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:30:11,536 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:30:11,536 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:30:11,537 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:30:11,537 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:30:11,538 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:30:11,846 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:30:11,847 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:30:12,749 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:30:12,759 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:30:12,759 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:30:12,760 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:30:12,760 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:30:12,761 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:30:13,261 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:30:13,262 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:30:13,262 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:30:13,262 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:30:13,263 - modules.friend_request_window - INFO -    📝 内容: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:13,263 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:30:13,263 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110145-\xe9\xba\xbb\xe8\x80\x80\xe5\xb3\xb0-2025-07-30 19:30:04'
2025-07-30 11:30:13,263 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:30:14,174 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:30:19,415 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:30:19,415 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:30:19,415 - modules.friend_request_window - INFO -    📝 原始文本: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:19,416 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:30:19,416 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:30:19,726 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:30:19,727 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:30:20,629 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:30:20,639 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:30:20,639 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:20,640 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:30:20,641 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:20,641 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:30:21,142 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:21,142 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:30:21,143 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:30:21,143 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:30:21,143 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:30:21,143 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:30:21,144 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:30:21,945 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:30:21,946 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:30:21,946 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:30:22,558 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:30:22,558 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:30:22,558 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:30:22,559 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:30:23,076 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:23,308 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:23,541 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:23,772 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:24,006 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:24,237 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:24,468 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:24,700 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:24,937 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:25,173 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:25,403 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:25,639 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:25,874 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:26,108 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:26,344 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:26,577 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:26,807 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:27,040 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:27,273 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:27,508 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:30:27,761 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:30:27,761 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:30:28,762 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:30:28,764 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:30:28,765 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:30:28,765 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:30:28,765 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:30:28,766 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:30:28,766 - modules.friend_request_window - INFO -    📝 备注信息: '014325110145-麻耀峰-2025-07-30 19:30:04'
2025-07-30 11:30:29,267 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:30:29,268 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:30:29,268 - modules.wechat_auto_add_simple - INFO - ✅ 13688506431 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:30:29,268 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13688506431
2025-07-30 11:30:29,269 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:30:30,577 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 11:30:30,578 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 11:30:30,578 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 11:30:30,579 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 11:30:30,580 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 11:30:30,580 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 11:30:30,580 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 11:30:30,580 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 11:30:30,581 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 11:30:30,581 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 11:30:30,581 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 11:30:30,582 - __main__ - INFO - � 更新全局进度：已处理 2/2905 个联系人
2025-07-30 11:30:30,582 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 11:30:33,583 - __main__ - INFO - 
============================================================
2025-07-30 11:30:33,583 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 11:30:33,584 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:30:33,584 - __main__ - INFO - 📊 全局进度：已处理 2/2905 个联系人
2025-07-30 11:30:33,584 - __main__ - INFO - ============================================================
2025-07-30 11:30:33,585 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 11:30:33,585 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:30:33,585 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:30:33,586 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 11:30:33,586 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:30:33,586 - modules.window_manager - INFO - 🔄 窗口被最小化，正在还原...
2025-07-30 11:30:34,238 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:30:34,239 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:30:34,239 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:30:34,240 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:30:34,240 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:30:34,240 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:30:34,241 - modules.window_manager - INFO - 📏 当前窗口位置: (504, 196), 大小: 726x650
2025-07-30 11:30:34,241 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:30:34,241 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 11:30:34,545 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 726x650
2025-07-30 11:30:34,545 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:30:34,546 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(3, 0)
2025-07-30 11:30:34,546 - modules.window_manager - INFO - ✅ 窗口大小和位置验证通过
2025-07-30 11:30:34,547 - modules.window_manager - INFO - ✅ 虽然API可能报告失败，但窗口实际已调整到目标大小和位置
2025-07-30 11:30:34,547 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:30:34,751 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:30:34,751 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:30:34,752 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:30:34,752 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:30:35,057 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:30:35,057 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:30:35,057 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:30:35,058 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:30:35,058 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:30:35,058 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:30:35,058 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:30:35,059 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:30:35,059 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:30:35,059 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:30:35,262 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:30:35,262 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:30:35,264 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:30:35,565 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:30:35,566 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:30:35,566 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:30:35,566 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:30:35,566 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:30:35,567 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:30:35,567 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:30:35,567 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:30:36,568 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:30:36,569 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 11:30:36,569 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:30:36,570 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:30:36,570 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:30:36,571 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:30:36,571 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:30:36,571 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:30:36,772 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:30:36,773 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:30:39,156 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:30:39,157 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:30:39,157 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 11:30:41,744 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:30:41,946 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:30:41,946 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:30:44,321 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:30:44,322 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:30:44,322 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 11:30:46,279 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:30:46,480 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:30:46,480 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:30:48,849 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:30:48,849 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:30:48,850 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 11:30:51,357 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:30:51,558 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:30:51,559 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:30:53,938 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:30:53,939 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:30:53,939 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 11:30:55,780 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:30:55,981 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:30:55,981 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:30:58,354 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:30:58,355 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:30:58,355 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:30:58,355 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:30:58,355 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:30:58,357 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:30:58,357 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:30:58,358 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:30:58,359 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:30:58,359 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:30:58,360 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6359068, 进程: Weixin.exe)
2025-07-30 11:30:58,362 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:30:58,362 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1900602)
2025-07-30 11:30:58,363 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1900602) - 增强版
2025-07-30 11:30:58,688 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:30:58,688 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:30:58,689 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:30:58,689 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:30:58,689 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 11:30:58,690 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:30:58,894 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:30:58,894 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:30:59,096 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:30:59,096 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:30:59,096 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:30:59,097 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:30:59,097 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:30:59,097 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:30:59,097 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:31:00,098 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:31:00,098 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:31:00,100 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:31:00,101 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:31:00,102 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:31:00,103 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:31:00,103 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:31:00,105 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6359068, 进程: Weixin.exe)
2025-07-30 11:31:00,108 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:31:00,108 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:31:00,109 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:31:00,109 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:31:00,109 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:31:00,110 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:31:00,436 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:31:00,436 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:31:00,437 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:31:00,437 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:31:00,437 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:31:00,438 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:31:00,438 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:31:00,438 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:31:00,439 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:31:00,439 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:31:00,640 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:31:00,641 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:31:00,643 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:31:00,944 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:31:00,944 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:31:00,944 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:31:01,945 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:31:01,945 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 11:31:01,945 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:31:01,948 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_113101.log
2025-07-30 11:31:01,949 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:31:01,949 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:31:01,949 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:31:01,950 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 11:31:01,951 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:31:01,952 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:31:01,954 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 11:31:01,954 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 11:31:01,954 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:31:01,955 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:31:01,955 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 11:31:01,956 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 11:31:01,956 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 11:31:01,956 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:31:01,957 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:31:01,958 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6359068
2025-07-30 11:31:01,958 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6359068) - 增强版
2025-07-30 11:31:02,266 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:31:02,267 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:31:02,268 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:31:02,268 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:31:02,269 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:31:02,270 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:31:02,473 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:31:02,474 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:31:02,675 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:31:02,676 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:31:02,679 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6359068 (API返回: None)
2025-07-30 11:31:02,980 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:31:02,980 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:31:02,981 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:31:02,981 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:31:02,982 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:31:02,982 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:31:02,982 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:31:02,986 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:31:02,987 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:31:03,483 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:31:03,484 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:31:03,740 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2910 个
2025-07-30 11:31:03,741 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 11:31:03,742 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2908 个
2025-07-30 11:31:03,742 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2908 个 (总计: 3135 个)
2025-07-30 11:31:03,743 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:31:03,744 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:31:03,744 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:03,744 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:31:03,745 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2908
2025-07-30 11:31:03,745 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15814063678 (郭恩昌)
2025-07-30 11:31:03,745 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:10,384 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15814063678
2025-07-30 11:31:10,431 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:31:10,491 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15814063678 执行添加朋友操作...
2025-07-30 11:31:10,550 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:31:10,597 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:31:10,652 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:31:11,070 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:31:11,118 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:31:11,355 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:31:11,423 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:31:11,459 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:31:11,562 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:31:11,630 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:31:11,677 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:31:11,727 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:31:11,903 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:31:12,038 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:31:12,234 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:31:12,616 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:31:12,961 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:31:13,241 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 11:31:13,568 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:31:14,427 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:31:14,597 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:31:14,867 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.79, 边缘比例0.0350
2025-07-30 11:31:15,039 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113114.png
2025-07-30 11:31:15,393 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:31:15,744 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:31:16,108 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:31:16,258 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:31:16,437 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:31:16,553 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113116.png
2025-07-30 11:31:16,606 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 11:31:16,638 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:31:16,641 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:31:16,651 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:31:16,670 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 11:31:16,688 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 11:31:16,695 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:31:16,698 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 11:31:16,719 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:31:16,731 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 11:31:16,738 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 11:31:16,754 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 11:31:16,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:31:16,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 11:31:16,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:31:16,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:31:16,781 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:31:16,787 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:31:16,789 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:31:16,791 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:31:16,794 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 11:31:16,796 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 11:31:16,800 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 11:31:16,806 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:31:16,807 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 11:31:16,809 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 11:31:16,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 11:31:17,342 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 11:31:17,552 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 11:31:17,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 11:31:18,158 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 11:31:18,471 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 11:31:18,579 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 11:31:18,673 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 11:31:18,892 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 11:31:18,987 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 11:31:19,205 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 11:31:19,259 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 11:31:19,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:31:19,404 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 11:31:19,471 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 11:31:19,490 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:31:19,498 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:31:19,564 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113119.png
2025-07-30 11:31:19,594 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:31:19,608 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 11:31:19,624 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113119.png
2025-07-30 11:31:19,673 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:31:19,683 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 11:31:19,713 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:31:19,781 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:31:20,100 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 11:31:20,918 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:31:20,922 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:31:20,937 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:20,939 - modules.wechat_auto_add_simple - INFO - ✅ 15814063678 添加朋友操作执行成功
2025-07-30 11:31:20,945 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:20,954 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:31:22,962 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:31:22,962 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:31:22,962 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:31:22,963 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:31:22,963 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:31:22,963 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:31:22,964 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:31:22,964 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:31:22,964 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:31:22,964 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15814063678
2025-07-30 11:31:22,965 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:31:22,965 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:31:22,966 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:31:22,966 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:31:22,967 - modules.friend_request_window - INFO -    📱 phone: '15814063678'
2025-07-30 11:31:22,967 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:31:22,968 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:31:23,466 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:31:23,466 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:31:23,467 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:31:23,467 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:31:23,468 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15814063678
2025-07-30 11:31:23,469 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:31:23,469 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:31:23,470 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:31:23,470 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:31:23,470 - modules.friend_request_window - INFO -    📱 手机号码: 15814063678
2025-07-30 11:31:23,470 - modules.friend_request_window - INFO -    🆔 准考证: 014325110150
2025-07-30 11:31:23,471 - modules.friend_request_window - INFO -    👤 姓名: 郭恩昌
2025-07-30 11:31:23,471 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:31:23,472 - modules.friend_request_window - INFO -    📝 备注格式: '014325110150-郭恩昌-2025-07-30 19:31:23'
2025-07-30 11:31:23,472 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:31:23,473 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110150-郭恩昌-2025-07-30 19:31:23'
2025-07-30 11:31:23,473 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:31:23,476 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:31:23,476 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 11:31:23,478 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:23,479 - modules.wechat_auto_add_simple - INFO - ✅ 15814063678 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 11:31:23,479 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15814063678
2025-07-30 11:31:23,480 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:27,024 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2908
2025-07-30 11:31:27,025 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15336500606 (杨帅)
2025-07-30 11:31:27,025 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:33,591 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15336500606
2025-07-30 11:31:33,591 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:31:33,591 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15336500606 执行添加朋友操作...
2025-07-30 11:31:33,592 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:31:33,592 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:31:33,593 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:31:33,594 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:31:33,599 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:31:33,602 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:31:33,602 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:31:33,603 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:31:33,604 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:31:33,605 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:31:33,606 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:31:33,607 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:31:33,612 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:31:33,613 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:31:33,616 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:31:33,621 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:31:33,624 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:31:33,628 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 11:31:33,632 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:31:34,136 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:31:34,138 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:31:34,196 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 11:31:34,197 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 11:31:34,206 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113134.png
2025-07-30 11:31:34,208 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:31:34,209 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:31:34,211 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:31:34,212 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:31:34,214 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:31:34,220 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113134.png
2025-07-30 11:31:34,222 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:31:34,225 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:31:34,227 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:31:34,228 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:31:34,230 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:31:34,231 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:31:34,236 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:31:34,238 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:31:34,246 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113134.png
2025-07-30 11:31:34,250 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:31:34,254 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:31:34,260 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113134.png
2025-07-30 11:31:34,283 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:31:34,288 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:31:34,293 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:31:34,295 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:31:34,600 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:31:35,369 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:31:35,371 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:31:35,372 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:35,372 - modules.wechat_auto_add_simple - INFO - ✅ 15336500606 添加朋友操作执行成功
2025-07-30 11:31:35,373 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:35,373 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:31:37,374 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:31:37,375 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:31:37,375 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:31:37,375 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:31:37,375 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:31:37,376 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:31:37,376 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:31:37,376 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:31:37,377 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:31:37,377 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15336500606
2025-07-30 11:31:37,377 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:31:37,378 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:31:37,378 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:31:37,378 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:31:37,379 - modules.friend_request_window - INFO -    📱 phone: '15336500606'
2025-07-30 11:31:37,379 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:31:37,379 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:31:37,892 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:31:37,893 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:31:37,893 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:31:37,893 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:31:37,894 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15336500606
2025-07-30 11:31:37,895 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:31:37,895 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:31:37,896 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:31:37,896 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:31:37,896 - modules.friend_request_window - INFO -    📱 手机号码: 15336500606
2025-07-30 11:31:37,897 - modules.friend_request_window - INFO -    🆔 准考证: 014325110151
2025-07-30 11:31:37,897 - modules.friend_request_window - INFO -    👤 姓名: 杨帅
2025-07-30 11:31:37,897 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:31:37,897 - modules.friend_request_window - INFO -    📝 备注格式: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:37,898 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:31:37,899 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:37,899 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:31:37,901 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 6491108, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:31:37,902 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 6491108)
2025-07-30 11:31:37,902 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:31:37,903 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:31:37,905 - modules.friend_request_window - INFO - 🔄 激活窗口: 6491108
2025-07-30 11:31:38,608 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:31:38,609 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:31:38,609 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:31:38,610 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:31:38,610 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:31:38,610 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:31:38,610 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:31:38,611 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:31:38,611 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:31:38,611 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:31:38,611 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:31:38,612 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:31:38,612 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:31:38,612 - modules.friend_request_window - INFO -    📝 remark参数: '014325110151-杨帅-2025-07-30 19:31:37' (类型: <class 'str'>, 长度: 35)
2025-07-30 11:31:38,612 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:31:38,612 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:38,613 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:31:38,613 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:31:38,613 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:31:38,613 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:31:38,614 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:31:38,614 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:31:38,615 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:31:39,535 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:31:44,778 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:31:44,778 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:31:44,778 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:31:44,779 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:31:44,779 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:31:45,089 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:31:45,089 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:31:45,992 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:31:46,000 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:31:46,001 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:31:46,001 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:31:46,002 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:31:46,003 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:31:46,504 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:31:46,504 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:31:46,505 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:31:46,505 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:31:46,505 - modules.friend_request_window - INFO -    📝 内容: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:46,506 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 11:31:46,506 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110151-\xe6\x9d\xa8\xe5\xb8\x85-2025-07-30 19:31:37'
2025-07-30 11:31:46,506 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:31:47,417 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:31:52,666 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:31:52,666 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:31:52,667 - modules.friend_request_window - INFO -    📝 原始文本: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:52,667 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 11:31:52,668 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:31:52,977 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:31:52,977 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:31:53,879 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:31:53,888 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:31:53,890 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:53,890 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:31:53,891 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:53,892 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 11:31:54,392 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:31:54,393 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:31:54,393 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:31:54,393 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:31:54,393 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:31:54,394 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:31:54,394 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:31:55,195 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:31:55,195 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:31:55,196 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:31:55,800 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:31:55,801 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:31:55,801 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:31:55,801 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:31:56,319 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:56,319 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:56,550 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:56,550 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:56,796 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:56,796 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,040 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,040 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,272 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,272 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,505 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,506 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,738 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,738 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,972 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:57,973 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,206 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,207 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,439 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,440 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,670 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,670 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,902 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:58,903 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,134 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,134 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,368 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,369 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,608 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,608 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,841 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:31:59,842 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,074 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,074 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,308 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,309 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,545 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,546 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,782 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:00,782 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:32:01,001 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:32:01,002 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:32:02,003 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:32:02,006 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:32:02,007 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:32:02,007 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:32:02,007 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:32:02,007 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:32:02,008 - modules.friend_request_window - INFO -    📝 备注信息: '014325110151-杨帅-2025-07-30 19:31:37'
2025-07-30 11:32:02,509 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:32:02,509 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:02,510 - modules.wechat_auto_add_simple - INFO - ✅ 15336500606 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:32:02,510 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15336500606
2025-07-30 11:32:02,511 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:04,197 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 11:32:04,198 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 11:32:04,198 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 11:32:04,199 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 11:32:04,200 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 11:32:04,200 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 11:32:04,200 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 11:32:04,201 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 11:32:04,201 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 11:32:04,201 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-30 11:32:04,202 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 11:32:04,203 - __main__ - INFO - � 更新全局进度：已处理 4/2905 个联系人
2025-07-30 11:32:04,203 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 11:32:07,204 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 11:32:07,204 - __main__ - INFO - 📊 当前进度：已处理 4/2905 个联系人
2025-07-30 11:32:07,204 - __main__ - INFO - 
============================================================
2025-07-30 11:32:07,205 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 2 轮)
2025-07-30 11:32:07,205 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:32:07,205 - __main__ - INFO - 📊 全局进度：已处理 4/2905 个联系人
2025-07-30 11:32:07,206 - __main__ - INFO - ============================================================
2025-07-30 11:32:07,206 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:32:07,206 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:32:07,207 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:32:07,207 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:32:07,207 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:32:07,535 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:32:07,535 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:32:07,536 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:32:07,536 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:32:07,536 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:32:07,537 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:32:07,537 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:32:07,537 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:32:07,538 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:32:07,538 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:32:07,740 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:32:07,741 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:32:07,741 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:32:07,741 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:32:08,045 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:32:08,045 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:32:08,046 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:32:08,047 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:32:08,047 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:32:08,048 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:32:08,048 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:32:08,049 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:32:08,049 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:32:08,050 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:32:08,251 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:32:08,258 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:32:08,260 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:32:08,572 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:32:08,573 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:32:08,573 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:32:08,573 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:32:08,574 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:32:08,574 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:32:08,574 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:32:08,575 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:32:09,575 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:32:09,576 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:32:09,576 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:32:09,577 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:32:09,577 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:32:09,577 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:32:09,577 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:32:09,578 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:32:09,779 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:32:09,780 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:32:12,150 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:32:12,151 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:32:12,151 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 11:32:14,997 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:32:15,198 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:32:15,198 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:32:17,566 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:32:17,566 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:32:17,566 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 11:32:19,447 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:32:19,648 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:32:19,648 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:32:22,032 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:32:22,032 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:32:22,033 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 11:32:24,643 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:32:24,844 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:32:24,845 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:32:27,215 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:32:27,215 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:32:27,216 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 11:32:29,217 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:32:29,418 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:32:29,419 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:32:31,798 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:32:31,799 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:32:31,799 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:32:31,799 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:32:31,799 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:32:31,801 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:32:31,801 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:32:31,802 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:32:31,802 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6359068, 进程: Weixin.exe)
2025-07-30 11:32:31,803 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:32:31,803 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:32:31,806 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:32:31,807 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1900602)
2025-07-30 11:32:31,808 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1900602) - 增强版
2025-07-30 11:32:32,111 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:32:32,111 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:32:32,112 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:32:32,112 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:32:32,112 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:32:32,113 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:32:32,113 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:32:32,113 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:32:32,315 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:32:32,315 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:32:32,316 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:32:32,316 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:32:32,316 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:32:32,316 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:32:32,316 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:32:33,317 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:32:33,318 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:32:33,319 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:32:33,320 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:32:33,321 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:32:33,321 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6359068, 进程: Weixin.exe)
2025-07-30 11:32:33,322 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:32:33,322 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:32:33,325 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:32:33,326 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:32:33,327 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:32:33,328 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:32:33,328 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:32:33,329 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:32:33,638 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:32:33,638 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:32:33,639 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:32:33,639 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:32:33,639 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:32:33,639 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:32:33,640 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:32:33,640 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:32:33,640 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:32:33,641 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:32:33,842 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:32:33,843 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:32:33,845 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:32:34,146 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:32:34,146 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:32:34,146 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:32:35,147 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:32:35,148 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 11:32:35,148 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:32:35,151 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_113235.log
2025-07-30 11:32:35,152 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:32:35,152 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:32:35,153 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:32:35,153 - __main__ - INFO - 🔄 传递全局联系人索引: 4
2025-07-30 11:32:35,153 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:32:35,154 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:32:35,156 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 11:32:35,156 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 11:32:35,157 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:32:35,158 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:32:35,159 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 11:32:35,159 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 11:32:35,160 - modules.wechat_auto_add_simple - INFO -   4. 微信 (726x650) - main
2025-07-30 11:32:35,160 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:32:35,162 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:32:35,164 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6359068
2025-07-30 11:32:35,164 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6359068) - 增强版
2025-07-30 11:32:35,492 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:32:35,493 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:32:35,493 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:32:35,493 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:32:35,494 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 11:32:35,494 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:32:35,698 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:32:35,698 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:32:35,900 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:32:35,900 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:32:35,902 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6359068 (API返回: None)
2025-07-30 11:32:36,202 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:32:36,203 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:32:36,203 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:32:36,203 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:32:36,204 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:32:36,204 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:32:36,205 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:32:36,208 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:32:36,210 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:32:36,773 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:32:36,774 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:32:37,068 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2908 个
2025-07-30 11:32:37,070 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 5 个联系人开始处理
2025-07-30 11:32:37,071 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2904 个
2025-07-30 11:32:37,071 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2904 个 (总计: 3135 个)
2025-07-30 11:32:37,072 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:32:37,073 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:32:37,074 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:37,075 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:32:37,076 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2904
2025-07-30 11:32:37,076 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18717117093 (张红宇)
2025-07-30 11:32:37,077 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:43,657 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18717117093
2025-07-30 11:32:43,658 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:32:43,658 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18717117093 执行添加朋友操作...
2025-07-30 11:32:43,658 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:32:43,658 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:32:43,659 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:32:43,661 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:32:43,667 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:32:43,670 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:32:43,671 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:32:43,672 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:32:43,673 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:32:43,675 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:32:43,676 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:32:43,676 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:32:43,686 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:32:43,691 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:32:43,694 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:32:43,709 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:32:43,716 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:32:43,719 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 11:32:43,722 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:32:44,225 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:32:44,228 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:32:44,305 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.61, 边缘比例0.0346
2025-07-30 11:32:44,312 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113244.png
2025-07-30 11:32:44,316 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:32:44,318 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:32:44,321 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:32:44,323 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:32:44,325 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:32:44,333 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113244.png
2025-07-30 11:32:44,336 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 11:32:44,339 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:32:44,341 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:32:44,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:32:44,347 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 11:32:44,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 11:32:44,355 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:32:44,357 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 11:32:44,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:32:44,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 11:32:44,369 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 11:32:44,372 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 11:32:44,374 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:32:44,376 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 11:32:44,381 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:32:44,385 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:32:44,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:32:44,390 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:32:44,392 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:32:44,404 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:32:44,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 11:32:44,418 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 11:32:44,422 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 11:32:44,426 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:32:44,432 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 11:32:44,436 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 11:32:44,439 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 11:32:44,441 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 11:32:44,446 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 11:32:44,451 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 11:32:44,454 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 11:32:44,457 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 11:32:44,459 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 11:32:44,462 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 11:32:44,468 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 11:32:44,470 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 11:32:44,474 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 11:32:44,475 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 11:32:44,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:32:44,485 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 11:32:44,487 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 11:32:44,492 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:32:44,496 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:32:44,509 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113244.png
2025-07-30 11:32:44,512 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:32:44,517 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 11:32:44,522 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113244.png
2025-07-30 11:32:44,550 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:32:44,553 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 11:32:44,556 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:32:44,558 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:32:44,860 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 11:32:45,647 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:32:45,649 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:32:45,650 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:45,651 - modules.wechat_auto_add_simple - INFO - ✅ 18717117093 添加朋友操作执行成功
2025-07-30 11:32:45,651 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:45,651 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:32:47,653 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:32:47,653 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:32:47,653 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:32:47,654 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:32:47,654 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:32:47,654 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:32:47,654 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:32:47,655 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:32:47,655 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:32:47,655 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18717117093
2025-07-30 11:32:47,656 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:32:47,656 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:32:47,656 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:32:47,657 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:32:47,657 - modules.friend_request_window - INFO -    📱 phone: '18717117093'
2025-07-30 11:32:47,657 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:32:47,657 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:32:48,211 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:32:48,212 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:32:48,212 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:32:48,212 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:32:48,214 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18717117093
2025-07-30 11:32:48,214 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:32:48,215 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:32:48,215 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:32:48,215 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:32:48,216 - modules.friend_request_window - INFO -    📱 手机号码: 18717117093
2025-07-30 11:32:48,216 - modules.friend_request_window - INFO -    🆔 准考证: 014325110154
2025-07-30 11:32:48,216 - modules.friend_request_window - INFO -    👤 姓名: 张红宇
2025-07-30 11:32:48,217 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:32:48,217 - modules.friend_request_window - INFO -    📝 备注格式: '014325110154-张红宇-2025-07-30 19:32:48'
2025-07-30 11:32:48,218 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:32:48,219 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110154-张红宇-2025-07-30 19:32:48'
2025-07-30 11:32:48,219 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:32:48,221 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:32:48,221 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 11:32:48,222 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:48,223 - modules.wechat_auto_add_simple - INFO - ✅ 18717117093 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 11:32:48,223 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18717117093
2025-07-30 11:32:48,224 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:51,875 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2904
2025-07-30 11:32:51,875 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15867534671 (陈明珠)
2025-07-30 11:32:51,876 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:32:58,437 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15867534671
2025-07-30 11:32:58,437 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:32:58,437 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15867534671 执行添加朋友操作...
2025-07-30 11:32:58,438 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:32:58,438 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:32:58,439 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:32:58,441 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:32:58,445 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:32:58,448 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:32:58,451 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:32:58,452 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:32:58,453 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:32:58,454 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:32:58,454 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:32:58,455 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:32:58,461 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:32:58,467 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:32:58,472 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:32:58,477 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:32:58,483 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:32:58,487 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 11:32:58,490 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:32:58,995 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:32:58,997 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:32:59,064 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.77, 边缘比例0.0383
2025-07-30 11:32:59,073 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113259.png
2025-07-30 11:32:59,079 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:32:59,084 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:32:59,086 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:32:59,088 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:32:59,090 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:32:59,099 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113259.png
2025-07-30 11:32:59,101 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:32:59,103 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:32:59,107 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:32:59,109 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:32:59,118 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:32:59,120 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:32:59,122 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:32:59,125 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:32:59,140 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113259.png
2025-07-30 11:32:59,142 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:32:59,151 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:32:59,159 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113259.png
2025-07-30 11:32:59,195 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:32:59,203 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:32:59,207 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:32:59,215 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:32:59,519 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:33:00,317 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:33:00,319 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:33:00,322 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:33:00,322 - modules.wechat_auto_add_simple - INFO - ✅ 15867534671 添加朋友操作执行成功
2025-07-30 11:33:00,323 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:33:00,325 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:33:02,327 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:33:02,328 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:33:02,328 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:33:02,328 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:33:02,329 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:33:02,329 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:33:02,329 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:33:02,329 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:33:02,330 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:33:02,330 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15867534671
2025-07-30 11:33:02,331 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:33:02,331 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:33:02,331 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:33:02,331 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:33:02,332 - modules.friend_request_window - INFO -    📱 phone: '15867534671'
2025-07-30 11:33:02,332 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:33:02,332 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:33:02,847 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:33:02,847 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:33:02,848 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:33:02,848 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:33:02,850 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15867534671
2025-07-30 11:33:02,850 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:33:02,851 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:33:02,852 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:33:02,853 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:33:02,853 - modules.friend_request_window - INFO -    📱 手机号码: 15867534671
2025-07-30 11:33:02,854 - modules.friend_request_window - INFO -    🆔 准考证: 014325110155
2025-07-30 11:33:02,854 - modules.friend_request_window - INFO -    👤 姓名: 陈明珠
2025-07-30 11:33:02,854 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:33:02,855 - modules.friend_request_window - INFO -    📝 备注格式: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:02,855 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:33:02,856 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:02,856 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:33:02,858 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1575924, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:33:02,860 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1575924)
2025-07-30 11:33:02,861 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:33:02,861 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:33:02,863 - modules.friend_request_window - INFO - 🔄 激活窗口: 1575924
2025-07-30 11:33:03,568 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:33:03,568 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:33:03,569 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:33:03,569 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:33:03,569 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:33:03,570 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:33:03,570 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:33:03,570 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:33:03,570 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:33:03,571 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:33:03,571 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:33:03,571 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:33:03,571 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:33:03,572 - modules.friend_request_window - INFO -    📝 remark参数: '014325110155-陈明珠-2025-07-30 19:33:02' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:33:03,572 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:33:03,572 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:03,573 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:33:03,573 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:33:03,573 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:33:03,573 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:33:03,573 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:33:03,574 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:33:03,574 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:33:04,479 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:33:09,721 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:33:09,722 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:33:09,722 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:33:09,722 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:33:09,723 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:33:10,032 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:33:10,033 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:33:10,935 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:33:10,944 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:33:10,945 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:33:10,945 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:33:10,946 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:33:10,947 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:33:11,447 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:33:11,448 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:33:11,448 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:33:11,448 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:33:11,449 - modules.friend_request_window - INFO -    📝 内容: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:11,449 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:33:11,449 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110155-\xe9\x99\x88\xe6\x98\x8e\xe7\x8f\xa0-2025-07-30 19:33:02'
2025-07-30 11:33:11,450 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:33:12,362 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:33:17,612 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:33:17,612 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:33:17,613 - modules.friend_request_window - INFO -    📝 原始文本: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:17,613 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:33:17,614 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:33:17,923 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:33:17,923 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:33:18,826 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:33:18,834 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:33:18,834 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:18,835 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:33:18,835 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:18,836 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:33:19,336 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:19,337 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:33:19,337 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:33:19,337 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:33:19,338 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:33:19,338 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:33:19,338 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:33:20,139 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:33:20,139 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:33:20,139 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:33:20,745 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:33:20,745 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:33:20,746 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:33:20,746 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:33:21,263 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:21,264 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:21,496 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:21,496 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:21,731 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:21,732 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:21,965 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:21,966 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,199 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,200 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,438 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,439 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,670 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,671 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,907 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:22,908 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,139 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,140 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,371 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,372 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,605 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,605 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,839 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:23,839 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,072 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,073 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,307 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,308 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,540 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,541 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,774 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:24,774 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,008 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,009 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,242 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,243 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,480 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,481 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,712 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,712 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:33:25,930 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:33:25,930 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:33:26,931 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:33:26,934 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:33:26,934 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:33:26,935 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:33:26,935 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:33:26,935 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:33:26,936 - modules.friend_request_window - INFO -    📝 备注信息: '014325110155-陈明珠-2025-07-30 19:33:02'
2025-07-30 11:33:27,436 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:33:27,438 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:33:27,438 - modules.wechat_auto_add_simple - INFO - ✅ 15867534671 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:33:27,438 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15867534671
2025-07-30 11:33:27,439 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:33:28,793 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 11:33:28,794 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 11:33:28,794 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 11:33:28,795 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 11:33:28,796 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 11:33:28,796 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 11:33:28,796 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 11:33:28,796 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 11:33:28,797 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 11:33:28,797 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 11:33:28,797 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 11:33:28,798 - __main__ - INFO - � 更新全局进度：已处理 6/2905 个联系人
2025-07-30 11:33:28,798 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 11:33:31,799 - __main__ - INFO - 
============================================================
2025-07-30 11:33:31,799 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 2 轮)
2025-07-30 11:33:31,800 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:33:31,800 - __main__ - INFO - 📊 全局进度：已处理 6/2905 个联系人
2025-07-30 11:33:31,800 - __main__ - INFO - ============================================================
2025-07-30 11:33:31,800 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 11:33:31,801 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:33:31,801 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:33:31,801 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 11:33:31,801 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:33:32,112 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:33:32,112 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:33:32,112 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:33:32,113 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:33:32,113 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:33:32,113 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:33:32,114 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:33:32,114 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:33:32,114 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:33:32,114 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:33:32,316 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:33:32,316 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:33:32,317 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:33:32,317 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:33:32,620 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:33:32,620 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:33:32,621 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:33:32,621 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:33:32,621 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:33:32,622 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:33:32,622 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:33:32,622 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:33:32,623 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:33:32,623 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:33:32,826 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:33:32,827 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:33:32,829 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:33:33,130 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:33:33,131 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:33:33,131 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:33:33,131 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:33:33,132 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:33:33,132 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:33:33,132 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:33:33,132 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:33:34,133 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:33:34,133 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 11:33:34,134 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:33:34,134 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:33:34,134 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:33:34,135 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:33:34,135 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:33:34,135 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:33:34,336 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:33:34,336 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:33:36,712 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:33:36,712 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:33:36,712 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 11:33:38,532 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:33:38,733 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:33:38,733 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:33:41,101 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:33:41,102 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:33:41,102 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 11:33:42,828 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:33:43,029 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:33:43,030 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:33:45,410 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:33:45,411 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:33:45,411 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 11:33:47,251 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:33:47,452 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:33:47,453 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:33:49,827 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:33:49,827 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:33:49,827 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 11:33:52,159 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:33:52,360 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:33:52,361 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:33:54,744 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:33:54,745 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:33:54,746 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:33:54,748 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:33:54,750 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:33:54,752 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6359068, 进程: Weixin.exe)
2025-07-30 11:33:54,755 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:33:54,757 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:33:54,760 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:33:54,761 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:33:54,762 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:33:54,766 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:33:54,768 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1900602)
2025-07-30 11:33:54,769 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1900602) - 增强版
2025-07-30 11:33:55,093 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:33:55,093 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:33:55,094 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:33:55,094 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:33:55,095 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:33:55,095 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:33:55,095 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:33:55,096 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:33:55,298 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:33:55,298 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:33:55,298 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:33:55,299 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:33:55,299 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:33:55,299 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:33:55,299 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:33:56,300 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:33:56,300 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:33:56,302 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1900602, 进程: Weixin.exe)
2025-07-30 11:33:56,302 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6359068, 进程: Weixin.exe)
2025-07-30 11:33:56,303 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:33:56,303 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:33:56,304 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:33:56,304 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:33:56,307 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:33:56,307 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:33:56,308 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:33:56,308 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:33:56,309 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:33:56,309 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:33:56,632 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:33:56,633 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:33:56,633 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:33:56,633 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:33:56,633 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:33:56,634 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:33:56,634 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:33:56,634 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:33:56,635 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:33:56,635 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:33:56,837 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:33:56,838 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:33:56,840 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:33:57,141 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:33:57,142 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:33:57,142 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:33:58,142 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:33:58,143 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 11:33:58,143 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:33:58,147 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_113358.log
2025-07-30 11:33:58,147 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:33:58,148 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:33:58,148 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:33:58,149 - __main__ - INFO - 🔄 传递全局联系人索引: 6
2025-07-30 11:33:58,149 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:33:58,150 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:33:58,152 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 11:33:58,153 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 11:33:58,153 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:33:58,154 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:33:58,154 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 11:33:58,155 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 11:33:58,156 - modules.wechat_auto_add_simple - INFO -   4. 微信 (726x650) - main
2025-07-30 11:33:58,157 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:33:58,158 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:33:58,160 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6359068
2025-07-30 11:33:58,160 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6359068) - 增强版
2025-07-30 11:33:58,471 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:33:58,472 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:33:58,472 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:33:58,473 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:33:58,473 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 11:33:58,473 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:33:58,677 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:33:58,678 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:33:58,879 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:33:58,880 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:33:58,882 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6359068 (API返回: None)
2025-07-30 11:33:59,183 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:33:59,183 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:33:59,184 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:33:59,184 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:33:59,185 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:33:59,185 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:33:59,185 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:33:59,189 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:33:59,190 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:33:59,676 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:33:59,677 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:33:59,932 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2906 个
2025-07-30 11:33:59,933 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 7 个联系人开始处理
2025-07-30 11:33:59,934 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2900 个
2025-07-30 11:33:59,934 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2900 个 (总计: 3135 个)
2025-07-30 11:33:59,934 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:33:59,935 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:33:59,935 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:33:59,935 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:33:59,936 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2900
2025-07-30 11:33:59,936 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13556651555 (王银林)
2025-07-30 11:33:59,936 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:34:06,486 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13556651555
2025-07-30 11:34:06,486 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:34:06,487 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13556651555 执行添加朋友操作...
2025-07-30 11:34:06,487 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:34:06,487 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:34:06,488 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:34:06,490 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:34:06,494 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:34:06,498 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:34:06,502 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:34:06,502 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:34:06,503 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:34:06,504 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:34:06,504 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:34:06,505 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:34:06,512 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:34:06,516 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:34:06,519 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:34:06,521 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:34:06,529 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:34:06,535 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 11:34:06,538 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:34:07,044 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:34:07,047 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:34:07,108 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差46.50, 边缘比例0.0513
2025-07-30 11:34:07,119 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113407.png
2025-07-30 11:34:07,123 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:34:07,128 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:34:07,131 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:34:07,133 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:34:07,135 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:34:07,143 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113407.png
2025-07-30 11:34:07,146 - WeChatAutoAdd - INFO - 底部区域原始检测到 33 个轮廓
2025-07-30 11:34:07,148 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 11:34:07,151 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 11:34:07,153 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 11:34:07,159 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 11:34:07,164 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 11:34:07,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,270), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 11:34:07,168 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,268), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:34:07,171 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(225,264), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 11:34:07,179 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 11:34:07,181 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:34:07,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 11:34:07,186 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:34:07,235 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,256), 尺寸3x6, 长宽比0.50, 面积18
2025-07-30 11:34:07,293 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:34:07,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:34:07,363 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(224,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:34:07,382 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:34:07,384 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:34:07,391 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:34:07,395 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(216,252), 尺寸9x3, 长宽比3.00, 面积27
2025-07-30 11:34:07,396 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:34:07,400 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:34:07,402 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:34:07,409 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=68.2 (阈值:60)
2025-07-30 11:34:07,413 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:34:07,415 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:34:07,418 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=73.6 (阈值:60)
2025-07-30 11:34:07,421 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 11:34:07,428 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 11:34:07,431 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 11:34:07,433 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(181,240), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 11:34:07,435 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,238), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 11:34:07,440 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,237), 尺寸5x6, 长宽比0.83, 面积30
2025-07-30 11:34:07,445 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(171,231), 尺寸8x11, 长宽比0.73, 面积88
2025-07-30 11:34:07,447 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,230), 尺寸7x13, 长宽比0.54, 面积91
2025-07-30 11:34:07,450 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,230), 尺寸9x13, 长宽比0.69, 面积117
2025-07-30 11:34:07,453 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,230), 尺寸34x14, 长宽比2.43, 面积476
2025-07-30 11:34:07,456 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=86.3 (阈值:60)
2025-07-30 11:34:07,461 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸25x14, 长宽比1.79, 面积350
2025-07-30 11:34:07,463 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=93.8 (阈值:60)
2025-07-30 11:34:07,466 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:34:07,469 - WeChatAutoAdd - INFO - 底部区域找到 5 个按钮候选
2025-07-30 11:34:07,475 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 11:34:07,478 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 11:34:07,480 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 11:34:07,492 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113407.png
2025-07-30 11:34:07,496 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 11:34:07,501 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:34:07,803 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 11:34:08,591 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:34:08,593 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:34:08,596 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:34:08,597 - modules.wechat_auto_add_simple - INFO - ✅ 13556651555 添加朋友操作执行成功
2025-07-30 11:34:08,598 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:34:08,598 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:34:10,600 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:34:10,601 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:34:10,601 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:34:10,602 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:34:10,602 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:34:10,602 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:34:10,602 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:34:10,603 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:34:10,603 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:34:10,603 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13556651555
2025-07-30 11:34:10,604 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:34:10,604 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:34:10,604 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:34:10,605 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:34:10,605 - modules.friend_request_window - INFO -    📱 phone: '13556651555'
2025-07-30 11:34:10,605 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:34:10,606 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:34:11,099 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:34:11,100 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:34:11,100 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:34:11,100 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:34:11,101 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13556651555
2025-07-30 11:34:11,102 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:34:11,102 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:34:11,103 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:34:11,103 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:34:11,103 - modules.friend_request_window - INFO -    📱 手机号码: 13556651555
2025-07-30 11:34:11,103 - modules.friend_request_window - INFO -    🆔 准考证: 014325110160
2025-07-30 11:34:11,104 - modules.friend_request_window - INFO -    👤 姓名: 王银林
2025-07-30 11:34:11,104 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:34:11,104 - modules.friend_request_window - INFO -    📝 备注格式: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:11,104 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:34:11,104 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:11,105 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:34:11,108 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 15992268, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:34:11,110 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 15992268)
2025-07-30 11:34:11,110 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:34:11,111 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:34:11,112 - modules.friend_request_window - INFO - 🔄 激活窗口: 15992268
2025-07-30 11:34:11,815 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:34:11,815 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:34:11,816 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:34:11,816 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:34:11,817 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:34:11,817 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:34:11,817 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:34:11,817 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:34:11,818 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:34:11,818 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:34:11,818 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:34:11,818 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:34:11,819 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:34:11,819 - modules.friend_request_window - INFO -    📝 remark参数: '014325110160-王银林-2025-07-30 19:34:11' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:34:11,819 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:34:11,819 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:11,820 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:34:11,820 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:34:11,820 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:34:11,820 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:34:11,821 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:34:11,822 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:34:11,823 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:34:12,743 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:34:17,987 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:34:17,987 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:34:17,988 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:34:17,988 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:34:17,989 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:34:18,298 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:34:18,299 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:34:19,202 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:34:19,211 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:34:19,212 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:34:19,213 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:34:19,213 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:34:19,213 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:34:19,714 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:34:19,714 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:34:19,715 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:34:19,715 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:34:19,715 - modules.friend_request_window - INFO -    📝 内容: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:19,715 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:34:19,716 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110160-\xe7\x8e\x8b\xe9\x93\xb6\xe6\x9e\x97-2025-07-30 19:34:11'
2025-07-30 11:34:19,716 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:34:20,624 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:34:25,867 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:34:25,867 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:34:25,868 - modules.friend_request_window - INFO -    📝 原始文本: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:25,868 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:34:25,869 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:34:26,178 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:34:26,178 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:34:27,080 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:34:27,091 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:34:27,092 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:27,093 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:34:27,093 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:27,094 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:34:27,594 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:34:27,595 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:34:27,595 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:34:27,595 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:34:27,596 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:34:27,596 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:34:27,596 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:34:28,397 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:34:28,397 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:34:28,398 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:34:29,007 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:34:29,007 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:34:29,008 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:34:29,008 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:34:29,510 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 11:34:29,512 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 11:34:29,512 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 11:34:29,512 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 11:34:29,513 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 11:34:29,513 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 11:34:29,513 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 11:34:29,514 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 11:34:29,514 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 11:34:29,514 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:34:29,514 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 11:34:29,515 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 11:34:29,515 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 11:34:29,515 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 11:34:29,515 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 11:34:29,516 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 11:34:29,516 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:34:29,518 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 11:34:29,518 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 11:34:30,021 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 11:34:30,021 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 11:34:30,022 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 11:34:30,022 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 11:34:30,022 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 11:34:30,022 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 11:34:30,023 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 11:34:30,023 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 11:34:30,942 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 11:34:30,951 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 11:34:30,954 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 11:34:30,963 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 11:34:30,987 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 11:34:30,989 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:34:31,792 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:34:31,792 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 11:34:31,792 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 11:34:31,793 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:34:32,594 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:34:32,594 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 11:34:32,594 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 2/2
2025-07-30 11:34:32,595 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 11:34:32,595 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 11:34:32,595 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:34:33,396 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:34:33,397 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 11:34:33,397 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 11:34:35,397 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 11:34:35,399 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:34:35,399 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 11:34:35,399 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:34:35,401 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:34:35,401 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:34:35,403 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 11:34:35,404 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 11:34:35,404 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 11:34:35,427 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 11:34:35,429 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 11:34:35,429 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 11:34:35,429 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 197994
2025-07-30 11:34:35,430 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:34:35,431 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:34:35,734 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:34:35,735 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:34:35,735 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:34:35,735 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:34:35,736 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:34:35,736 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:34:35,737 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:34:35,737 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:34:35,737 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:34:35,738 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:34:35,940 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:34:35,941 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:34:35,941 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 11:34:35,941 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 11:34:35,942 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 11:34:35,942 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 11:34:35,942 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 11:34:37,943 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 11:34:37,944 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:34:37,944 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:34:37,944 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 11:34:37,945 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 11:34:37,945 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 11:34:37,945 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:34:37,946 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:34:37,946 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:34:37,946 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:34:37,946 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:34:37,947 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:34:38,147 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:34:38,148 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:34:40,524 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:34:40,525 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:34:40,525 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 11:34:42,917 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:34:43,119 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:34:43,119 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:34:45,490 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:34:45,490 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:34:45,490 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 11:34:48,031 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:34:48,232 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:34:48,233 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:34:50,601 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:34:50,601 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:34:50,602 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 11:34:52,283 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:34:52,484 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:34:52,484 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:34:54,857 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:34:54,857 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:34:54,858 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 11:34:56,412 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:34:56,613 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:34:56,614 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:34:58,989 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:34:58,990 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:34:58,990 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:34:58,991 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:34:58,991 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:34:58,992 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:34:58,993 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:34:58,994 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:34:58,995 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4590626, 进程: Weixin.exe)
2025-07-30 11:34:58,998 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:34:59,000 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4590626)
2025-07-30 11:34:59,001 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4590626) - 增强版
2025-07-30 11:34:59,305 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:34:59,305 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:34:59,306 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:34:59,306 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:34:59,306 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:34:59,307 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:34:59,511 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:34:59,511 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:34:59,713 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:34:59,714 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:34:59,714 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:34:59,714 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:34:59,715 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:34:59,715 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:34:59,715 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:35:00,716 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:35:00,717 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:35:00,718 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:35:00,719 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:35:00,719 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:35:00,720 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4590626, 进程: Weixin.exe)
2025-07-30 11:35:00,723 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:35:00,723 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:35:00,724 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:35:00,724 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:35:00,725 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 11:35:00,725 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 11:35:00,726 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 11:35:01,728 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 11:35:01,729 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 11:35:01,729 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 11:35:01,729 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 11:35:01,729 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 11:35:01,730 - modules.friend_request_window - INFO - 🔧 开始移动窗口 4590626 到目标位置...
2025-07-30 11:35:02,534 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 11:35:02,535 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 11:35:02,535 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 11:35:02,536 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 11:35:02,536 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 11:35:02,536 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 11:35:02,536 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 11:35:02,537 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 11:35:02,537 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 11:35:02,537 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:35:02,537 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:35:02,538 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:35:02,538 - modules.friend_request_window - INFO -    📝 备注信息: '014325110160-王银林-2025-07-30 19:34:11'
2025-07-30 11:35:03,038 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:35:03,039 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:03,040 - modules.wechat_auto_add_simple - INFO - ✅ 13556651555 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:35:03,040 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13556651555
2025-07-30 11:35:03,041 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:06,661 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2900
2025-07-30 11:35:06,662 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15018500157 (曾迪琛)
2025-07-30 11:35:06,662 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:13,231 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15018500157
2025-07-30 11:35:13,232 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:35:13,232 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15018500157 执行添加朋友操作...
2025-07-30 11:35:13,232 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:35:13,233 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:35:13,233 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:35:13,236 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:35:13,240 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 11:35:13,245 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:35:13,245 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:35:13,246 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:35:13,247 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:35:13,248 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:35:13,248 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:35:13,249 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:35:13,255 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:35:13,260 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:35:13,263 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:35:13,266 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 11:35:13,272 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:35:13,777 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:35:13,779 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:35:13,836 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差53.08, 边缘比例0.0464
2025-07-30 11:35:13,846 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113513.png
2025-07-30 11:35:13,849 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:35:13,851 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:35:13,857 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:35:13,860 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:35:13,863 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:35:13,869 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113513.png
2025-07-30 11:35:13,874 - WeChatAutoAdd - INFO - 底部区域原始检测到 34 个轮廓
2025-07-30 11:35:13,878 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 11:35:13,881 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 11:35:13,884 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 11:35:13,890 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 11:35:13,893 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 11:35:13,896 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 11:35:13,898 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:35:13,900 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 11:35:13,906 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:35:13,910 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:35:13,913 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:35:13,917 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:35:13,924 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:35:13,931 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:35:13,941 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:35:13,947 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:35:13,950 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=65.1 (阈值:60)
2025-07-30 11:35:13,956 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:35:13,961 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=73.5 (阈值:60)
2025-07-30 11:35:13,963 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:35:13,966 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.8 (阈值:60)
2025-07-30 11:35:13,970 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 11:35:13,977 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=81.4 (阈值:60)
2025-07-30 11:35:13,980 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 11:35:13,982 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 11:35:13,985 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 11:35:13,992 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(153,241), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:35:13,994 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,240), 尺寸4x3, 长宽比1.33, 面积12
2025-07-30 11:35:13,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,238), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 11:35:13,998 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,236), 尺寸6x1, 长宽比6.00, 面积6
2025-07-30 11:35:14,001 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,236), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 11:35:14,007 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(159,234), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 11:35:14,012 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(156,234), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:35:14,015 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,233), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 11:35:14,025 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,233), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 11:35:14,027 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,232), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 11:35:14,031 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=95.6 (阈值:60)
2025-07-30 11:35:14,032 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(140,231), 尺寸9x8, 长宽比1.12, 面积72
2025-07-30 11:35:14,039 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(136,231), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 11:35:14,042 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(135,231), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:35:14,046 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,231), 尺寸13x12, 长宽比1.08, 面积156
2025-07-30 11:35:14,049 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=104.6 (阈值:60)
2025-07-30 11:35:14,056 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,230), 尺寸14x3, 长宽比4.67, 面积42
2025-07-30 11:35:14,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:35:14,061 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-07-30 11:35:14,063 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 11:35:14,065 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 11:35:14,069 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 11:35:14,081 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113514.png
2025-07-30 11:35:14,084 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 11:35:14,087 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:35:14,392 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 11:35:15,163 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:35:15,166 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:35:15,168 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:15,169 - modules.wechat_auto_add_simple - INFO - ✅ 15018500157 添加朋友操作执行成功
2025-07-30 11:35:15,171 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:15,173 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:35:17,175 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 申请添加朋友
2025-07-30 11:35:17,175 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:35:17,175 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:35:17,176 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:35:17,176 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:35:17,176 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:35:17,177 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:35:17,177 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:35:17,177 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:35:17,177 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15018500157
2025-07-30 11:35:17,178 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:35:17,178 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:35:17,178 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:35:17,179 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:35:17,179 - modules.friend_request_window - INFO -    📱 phone: '15018500157'
2025-07-30 11:35:17,179 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:35:17,180 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:35:17,679 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:35:17,679 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:35:17,680 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:35:17,680 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:35:17,681 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15018500157
2025-07-30 11:35:17,681 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:35:17,682 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:35:17,682 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:35:17,683 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:35:17,683 - modules.friend_request_window - INFO -    📱 手机号码: 15018500157
2025-07-30 11:35:17,683 - modules.friend_request_window - INFO -    🆔 准考证: 014325110162
2025-07-30 11:35:17,684 - modules.friend_request_window - INFO -    👤 姓名: 曾迪琛
2025-07-30 11:35:17,684 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:35:17,685 - modules.friend_request_window - INFO -    📝 备注格式: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:17,685 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:35:17,686 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:17,688 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:35:17,690 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2097210, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:35:17,693 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2097210)
2025-07-30 11:35:17,694 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:35:17,694 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:35:17,694 - modules.friend_request_window - INFO - 🔄 激活窗口: 2097210
2025-07-30 11:35:18,397 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:35:18,398 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:35:18,398 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:35:18,399 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:35:18,399 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:35:18,399 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:35:18,399 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:35:18,400 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:35:18,400 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:35:18,400 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:35:18,400 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:35:18,401 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:35:18,401 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:35:18,401 - modules.friend_request_window - INFO -    📝 remark参数: '014325110162-曾迪琛-2025-07-30 19:35:17' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:35:18,401 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:35:18,402 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:18,402 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:35:18,402 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:35:18,403 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:35:18,403 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:35:18,403 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:35:18,404 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:35:18,404 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:35:19,322 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:35:24,569 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:35:24,569 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:35:24,569 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:35:24,570 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:35:24,570 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:35:24,879 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:35:24,879 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:35:25,783 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:35:25,791 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:35:25,792 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:35:25,792 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:35:25,792 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:35:25,792 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:35:26,294 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:35:26,294 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:35:26,294 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:35:26,295 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:35:26,295 - modules.friend_request_window - INFO -    📝 内容: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:26,295 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:35:26,296 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110162-\xe6\x9b\xbe\xe8\xbf\xaa\xe7\x90\x9b-2025-07-30 19:35:17'
2025-07-30 11:35:26,296 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:35:27,204 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:35:32,452 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:35:32,453 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:35:32,453 - modules.friend_request_window - INFO -    📝 原始文本: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:32,453 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:35:32,454 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:35:32,764 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:35:32,764 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:35:33,667 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:35:33,675 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:35:33,676 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:33,676 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:35:33,677 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:33,677 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:35:34,178 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:34,179 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:35:34,179 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:35:34,179 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:35:34,180 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:35:34,180 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:35:34,180 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:35:34,981 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:35:34,981 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:35:34,982 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:35:35,586 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:35,587 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:35:35,587 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:35:35,587 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:35:36,104 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:36,333 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:36,564 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:36,796 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:37,030 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:37,266 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:37,498 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:37,732 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:37,967 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:38,202 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:38,437 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:38,674 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:38,904 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:39,138 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:39,373 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:39,609 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:39,840 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:40,074 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:40,306 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:40,543 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:35:40,760 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:35:40,760 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:35:41,761 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:35:41,764 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:35:41,764 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:35:41,765 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:35:41,765 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:35:41,765 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:35:41,766 - modules.friend_request_window - INFO -    📝 备注信息: '014325110162-曾迪琛-2025-07-30 19:35:17'
2025-07-30 11:35:42,266 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:35:42,267 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:42,268 - modules.wechat_auto_add_simple - INFO - ✅ 15018500157 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:35:42,268 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15018500157
2025-07-30 11:35:42,269 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:35:43,564 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 11:35:43,564 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 11:35:43,564 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 11:35:43,566 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 11:35:43,566 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 11:35:43,567 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 11:35:43,567 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 11:35:43,567 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 11:35:43,567 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 11:35:43,568 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-30 11:35:43,568 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 11:35:43,569 - __main__ - INFO - � 更新全局进度：已处理 8/2905 个联系人
2025-07-30 11:35:43,569 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 11:35:46,570 - __main__ - INFO - 🔄 完成第 2 轮窗口循环，重新开始下一轮
2025-07-30 11:35:46,570 - __main__ - INFO - 📊 当前进度：已处理 8/2905 个联系人
2025-07-30 11:35:46,571 - __main__ - INFO - 
============================================================
2025-07-30 11:35:46,571 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 3 轮)
2025-07-30 11:35:46,571 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:35:46,572 - __main__ - INFO - 📊 全局进度：已处理 8/2905 个联系人
2025-07-30 11:35:46,572 - __main__ - INFO - ============================================================
2025-07-30 11:35:46,572 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:35:46,572 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:35:46,573 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:35:46,573 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:35:46,573 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:35:46,880 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:35:46,881 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:35:46,882 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:35:46,882 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:35:46,882 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:35:46,883 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:35:46,883 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:35:46,883 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:35:46,884 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:35:46,884 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:35:47,086 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:35:47,086 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:35:47,086 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:35:47,086 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:35:47,390 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:35:47,390 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:35:47,391 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:35:47,391 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:35:47,391 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:35:47,392 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:35:47,392 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:35:47,392 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:35:47,393 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:35:47,393 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:35:47,594 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:35:47,594 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:35:47,596 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:35:47,897 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:35:47,897 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:35:47,898 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:35:47,898 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:35:47,898 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:35:47,898 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:35:47,899 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:35:47,899 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:35:48,900 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:35:48,900 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:35:48,901 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:35:48,901 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:35:48,902 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:35:48,902 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:35:48,902 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:35:48,902 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:35:49,103 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:35:49,104 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:35:51,487 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:35:51,487 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:35:51,487 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 11:35:54,209 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:35:54,410 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:35:54,410 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:35:56,779 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:35:56,780 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:35:56,780 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-30 11:35:59,781 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:35:59,982 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:35:59,983 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:36:02,368 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:36:02,369 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:36:02,369 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 11:36:04,218 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:36:04,420 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:36:04,420 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:36:06,802 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:36:06,802 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:36:06,802 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 11:36:09,105 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:36:09,308 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:36:09,309 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:36:11,685 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:36:11,686 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:36:11,686 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:36:11,686 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:36:11,686 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:36:11,688 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:36:11,688 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:36:11,690 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4590626, 进程: Weixin.exe)
2025-07-30 11:36:11,692 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:36:11,692 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4590626)
2025-07-30 11:36:11,692 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4590626) - 增强版
2025-07-30 11:36:11,996 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:36:11,996 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:36:11,997 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:36:11,997 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:36:11,997 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 11:36:11,997 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:36:12,201 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:36:12,201 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:36:12,403 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:36:12,404 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:36:12,404 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:36:12,404 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:36:12,405 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:36:12,405 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:36:12,405 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:36:13,405 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:36:13,406 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:36:13,408 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:36:13,408 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:36:13,409 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4590626, 进程: Weixin.exe)
2025-07-30 11:36:13,411 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:36:13,411 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:36:13,412 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:36:13,413 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:36:13,413 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:36:13,414 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:36:13,723 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:36:13,724 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:36:13,724 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:36:13,724 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:36:13,725 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:36:13,725 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:36:13,725 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:36:13,726 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:36:13,726 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:36:13,726 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:36:13,928 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:36:13,929 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:36:13,931 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:36:14,232 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:36:14,232 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:36:14,232 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:36:15,233 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:36:15,234 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 11:36:15,234 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:36:15,237 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_113615.log
2025-07-30 11:36:15,238 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:36:15,238 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:36:15,239 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:36:15,239 - __main__ - INFO - 🔄 传递全局联系人索引: 8
2025-07-30 11:36:15,241 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:36:15,241 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:36:15,244 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 11:36:15,245 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 11:36:15,246 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 11:36:15,247 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:36:15,249 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 11:36:15,249 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:36:15,252 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:36:15,253 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4590626
2025-07-30 11:36:15,253 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4590626) - 增强版
2025-07-30 11:36:15,562 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:36:15,563 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:36:15,563 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:36:15,564 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:36:15,564 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:36:15,564 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:36:15,565 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:36:15,565 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:36:15,767 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:36:15,768 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:36:15,770 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4590626 (API返回: None)
2025-07-30 11:36:16,071 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:36:16,072 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:36:16,072 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:36:16,072 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:36:16,073 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:36:16,074 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:36:16,074 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:36:16,078 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:36:16,078 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:36:16,570 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:36:16,570 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:36:16,817 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2904 个
2025-07-30 11:36:16,818 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 9 个联系人开始处理
2025-07-30 11:36:16,818 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2896 个
2025-07-30 11:36:16,819 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2896 个 (总计: 3135 个)
2025-07-30 11:36:16,819 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:36:16,819 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:36:16,820 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:16,820 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:36:16,820 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2896
2025-07-30 11:36:16,820 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17519190726 (赵晓圆)
2025-07-30 11:36:16,821 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:23,393 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17519190726
2025-07-30 11:36:23,394 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:36:23,394 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17519190726 执行添加朋友操作...
2025-07-30 11:36:23,394 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:36:23,394 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:36:23,395 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:36:23,398 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:36:23,402 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 11:36:23,409 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:36:23,409 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:36:23,410 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:36:23,410 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:36:23,411 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:36:23,412 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:36:23,412 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:36:23,421 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:36:23,425 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:36:23,431 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:36:23,440 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 11:36:23,443 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:36:23,946 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:36:23,950 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:36:24,014 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.73, 边缘比例0.0349
2025-07-30 11:36:24,022 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113624.png
2025-07-30 11:36:24,028 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:36:24,032 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:36:24,039 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:36:24,043 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:36:24,046 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:36:24,059 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113624.png
2025-07-30 11:36:24,062 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 11:36:24,068 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:36:24,071 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:36:24,074 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:36:24,076 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 11:36:24,079 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 11:36:24,086 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:36:24,088 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 11:36:24,091 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:36:24,094 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 11:36:24,098 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 11:36:24,108 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 11:36:24,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:36:24,123 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 11:36:24,127 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:36:24,131 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:36:24,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:36:24,271 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:36:24,276 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:36:24,281 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:36:24,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 11:36:24,311 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 11:36:24,321 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 11:36:24,330 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:36:24,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 11:36:24,342 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 11:36:24,345 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 11:36:24,349 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 11:36:24,355 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 11:36:24,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 11:36:24,361 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 11:36:24,364 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 11:36:24,372 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 11:36:24,379 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 11:36:24,387 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 11:36:24,391 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 11:36:24,394 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 11:36:24,398 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 11:36:24,407 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:36:24,410 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 11:36:24,412 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 11:36:24,419 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:36:24,422 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:36:24,433 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113624.png
2025-07-30 11:36:24,438 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:36:24,441 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 11:36:24,446 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113624.png
2025-07-30 11:36:24,479 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:36:24,488 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 11:36:24,491 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:36:24,495 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:36:24,802 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 11:36:25,583 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:36:25,586 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:36:25,588 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:25,589 - modules.wechat_auto_add_simple - INFO - ✅ 17519190726 添加朋友操作执行成功
2025-07-30 11:36:25,589 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:25,590 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:36:27,592 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:36:27,592 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:36:27,592 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:36:27,593 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:36:27,593 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:36:27,594 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:36:27,594 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:36:27,594 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:36:27,595 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:36:27,595 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17519190726
2025-07-30 11:36:27,595 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:36:27,596 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:36:27,596 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:36:27,597 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:36:27,597 - modules.friend_request_window - INFO -    📱 phone: '17519190726'
2025-07-30 11:36:27,598 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:36:27,599 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:36:28,122 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:36:28,122 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:36:28,122 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:36:28,123 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:36:28,124 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17519190726
2025-07-30 11:36:28,124 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:36:28,125 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:36:28,125 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:36:28,126 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:36:28,126 - modules.friend_request_window - INFO -    📱 手机号码: 17519190726
2025-07-30 11:36:28,126 - modules.friend_request_window - INFO -    🆔 准考证: 014325110234
2025-07-30 11:36:28,126 - modules.friend_request_window - INFO -    👤 姓名: 赵晓圆
2025-07-30 11:36:28,127 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:36:28,127 - modules.friend_request_window - INFO -    📝 备注格式: '014325110234-赵晓圆-2025-07-30 19:36:28'
2025-07-30 11:36:28,128 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:36:28,128 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110234-赵晓圆-2025-07-30 19:36:28'
2025-07-30 11:36:28,129 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:36:28,133 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:36:28,134 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 11:36:28,136 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:28,137 - modules.wechat_auto_add_simple - INFO - ✅ 17519190726 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 11:36:28,137 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17519190726
2025-07-30 11:36:28,139 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:31,882 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2896
2025-07-30 11:36:31,882 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17569150612 (闫政阳)
2025-07-30 11:36:31,882 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:38,439 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17569150612
2025-07-30 11:36:38,440 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:36:38,440 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17569150612 执行添加朋友操作...
2025-07-30 11:36:38,440 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:36:38,441 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:36:38,441 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:36:38,444 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:36:38,448 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:36:38,452 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:36:38,453 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:36:38,453 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:36:38,453 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:36:38,454 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:36:38,457 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:36:38,458 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:36:38,472 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:36:38,478 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:36:38,485 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:36:38,491 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 11:36:38,494 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:36:38,999 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:36:39,002 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:36:39,071 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 11:36:39,074 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 11:36:39,089 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113639.png
2025-07-30 11:36:39,094 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:36:39,102 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:36:39,105 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:36:39,108 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:36:39,111 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:36:39,123 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113639.png
2025-07-30 11:36:39,127 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:36:39,135 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:36:39,139 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:36:39,142 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:36:39,145 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:36:39,153 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:36:39,155 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:36:39,159 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:36:39,177 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113639.png
2025-07-30 11:36:39,187 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:36:39,189 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:36:39,195 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113639.png
2025-07-30 11:36:39,223 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:36:39,228 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:36:39,236 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:36:39,239 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:36:39,543 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:36:40,312 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:36:40,315 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:36:40,318 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:40,319 - modules.wechat_auto_add_simple - INFO - ✅ 17569150612 添加朋友操作执行成功
2025-07-30 11:36:40,320 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:36:40,321 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:36:42,323 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:36:42,323 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:36:42,324 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:36:42,324 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:36:42,324 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:36:42,324 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:36:42,325 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:36:42,325 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:36:42,325 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:36:42,325 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17569150612
2025-07-30 11:36:42,326 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:36:42,326 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:36:42,326 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:36:42,327 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:36:42,327 - modules.friend_request_window - INFO -    📱 phone: '17569150612'
2025-07-30 11:36:42,327 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:36:42,327 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:36:42,830 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:36:42,830 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:36:42,831 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:36:42,831 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:36:42,832 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17569150612
2025-07-30 11:36:42,832 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:36:42,833 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:36:42,833 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:36:42,834 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:36:42,834 - modules.friend_request_window - INFO -    📱 手机号码: 17569150612
2025-07-30 11:36:42,834 - modules.friend_request_window - INFO -    🆔 准考证: 014325110235
2025-07-30 11:36:42,834 - modules.friend_request_window - INFO -    👤 姓名: 闫政阳
2025-07-30 11:36:42,835 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:36:42,835 - modules.friend_request_window - INFO -    📝 备注格式: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:42,841 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:36:42,843 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:42,843 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:36:42,845 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 7080708, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:36:42,846 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 7080708)
2025-07-30 11:36:42,848 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:36:42,848 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:36:42,851 - modules.friend_request_window - INFO - 🔄 激活窗口: 7080708
2025-07-30 11:36:43,554 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:36:43,555 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:36:43,555 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:36:43,556 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:36:43,556 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:36:43,556 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:36:43,556 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:36:43,557 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:36:43,557 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:36:43,557 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:36:43,557 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:36:43,558 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:36:43,558 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:36:43,558 - modules.friend_request_window - INFO -    📝 remark参数: '014325110235-闫政阳-2025-07-30 19:36:42' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:36:43,559 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:36:43,559 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:43,559 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:36:43,560 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:36:43,560 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:36:43,560 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:36:43,561 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:36:43,561 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:36:43,562 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:36:44,485 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:36:49,728 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:36:49,729 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:36:49,729 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:36:49,729 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:36:49,730 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:36:50,039 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:36:50,039 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:36:50,941 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:36:50,950 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:36:50,951 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:36:50,951 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:36:50,951 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:36:50,952 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:36:51,453 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:36:51,453 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:36:51,453 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:36:51,454 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:36:51,454 - modules.friend_request_window - INFO -    📝 内容: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:51,454 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:36:51,454 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110235-\xe9\x97\xab\xe6\x94\xbf\xe9\x98\xb3-2025-07-30 19:36:42'
2025-07-30 11:36:51,455 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:36:52,365 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:36:57,608 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:36:57,608 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:36:57,608 - modules.friend_request_window - INFO -    📝 原始文本: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:57,609 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:36:57,609 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:36:57,921 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:36:57,921 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:36:58,824 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:36:58,833 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:36:58,834 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:58,835 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:36:58,835 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:58,836 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:36:59,336 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:36:59,337 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:36:59,337 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:36:59,337 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:36:59,337 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:36:59,338 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:36:59,338 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:37:00,139 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:37:00,139 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:37:00,139 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:37:00,749 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:37:00,749 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:37:00,749 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:37:00,749 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:37:01,267 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:01,499 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:01,730 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:01,966 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:02,200 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:02,434 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:02,669 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:02,902 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:03,138 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:03,373 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:03,606 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:03,840 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:04,071 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:04,304 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:04,537 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:04,769 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:05,002 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:05,233 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:05,464 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:05,698 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:37:05,915 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:37:05,916 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:37:06,916 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:37:06,919 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:37:06,919 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:37:06,920 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:37:06,920 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:37:06,920 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:37:06,921 - modules.friend_request_window - INFO -    📝 备注信息: '014325110235-闫政阳-2025-07-30 19:36:42'
2025-07-30 11:37:07,421 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:37:07,422 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:37:07,423 - modules.wechat_auto_add_simple - INFO - ✅ 17569150612 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:37:07,423 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17569150612
2025-07-30 11:37:07,424 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:37:08,754 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 11:37:08,755 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 11:37:08,755 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 11:37:08,757 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 11:37:08,757 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 11:37:08,757 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 11:37:08,758 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 11:37:08,758 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 11:37:08,758 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 11:37:08,758 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 11:37:08,759 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 11:37:08,759 - __main__ - INFO - � 更新全局进度：已处理 10/2905 个联系人
2025-07-30 11:37:08,760 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 11:37:11,760 - __main__ - INFO - 
============================================================
2025-07-30 11:37:11,761 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 3 轮)
2025-07-30 11:37:11,761 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:37:11,762 - __main__ - INFO - 📊 全局进度：已处理 10/2905 个联系人
2025-07-30 11:37:11,762 - __main__ - INFO - ============================================================
2025-07-30 11:37:11,762 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 11:37:11,762 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:37:11,763 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:37:11,763 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 11:37:11,763 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:37:11,764 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 11:37:12,291 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:37:12,291 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:37:12,292 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:37:12,292 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:37:12,292 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:37:12,293 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:37:12,293 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:37:12,293 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:37:12,293 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:37:12,294 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:37:12,496 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:37:12,497 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:37:12,497 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:37:12,497 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:37:12,801 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:37:12,802 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:37:12,802 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:37:12,802 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:37:12,803 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:37:12,803 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:37:12,803 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:37:12,804 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:37:12,804 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:37:12,804 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:37:13,006 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:37:13,006 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:37:13,008 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:37:13,309 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:37:13,309 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:37:13,310 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:37:13,310 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:37:13,311 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:37:13,311 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:37:13,311 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:37:13,311 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:37:14,312 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:37:14,312 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 11:37:14,313 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:37:14,313 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:37:14,313 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:37:14,313 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:37:14,314 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:37:14,314 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:37:14,515 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:37:14,515 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:37:16,898 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:37:16,898 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:37:16,899 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 11:37:18,503 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:37:18,704 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:37:18,705 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:37:21,072 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:37:21,072 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:37:21,073 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 11:37:23,040 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:37:23,241 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:37:23,242 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:37:25,612 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:37:25,613 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:37:25,613 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 11:37:28,232 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:37:28,433 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:37:28,433 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:37:30,801 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:37:30,802 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:37:30,802 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 11:37:32,490 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:37:32,691 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:37:32,691 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:37:35,063 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:37:35,063 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:37:35,064 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:37:35,064 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:37:35,064 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:37:35,066 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:37:35,067 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:37:35,068 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4590626, 进程: Weixin.exe)
2025-07-30 11:37:35,068 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:37:35,068 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:37:35,069 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7146244, 进程: Weixin.exe)
2025-07-30 11:37:35,072 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:37:35,073 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4590626)
2025-07-30 11:37:35,074 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4590626) - 增强版
2025-07-30 11:37:35,398 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:37:35,399 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:37:35,399 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:37:35,400 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:37:35,400 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 11:37:35,400 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:37:35,603 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:37:35,604 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:37:35,806 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:37:35,806 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:37:35,807 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:37:35,807 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:37:35,807 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:37:35,807 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:37:35,807 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:37:36,808 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:37:36,809 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:37:36,810 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4590626, 进程: Weixin.exe)
2025-07-30 11:37:36,811 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:37:36,811 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:37:36,812 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:37:36,812 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:37:36,813 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7146244, 进程: Weixin.exe)
2025-07-30 11:37:36,815 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:37:36,815 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:37:36,816 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:37:36,817 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:37:36,817 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:37:36,817 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:37:37,140 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:37:37,141 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:37:37,141 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:37:37,142 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:37:37,142 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:37:37,142 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:37:37,143 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:37:37,143 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:37:37,143 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:37:37,143 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:37:37,345 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:37:37,345 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:37:37,347 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:37:37,648 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:37:37,649 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:37:37,649 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:37:38,649 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:37:38,650 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 11:37:38,650 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:37:38,652 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_113738.log
2025-07-30 11:37:38,653 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:37:38,654 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:37:38,654 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:37:38,654 - __main__ - INFO - 🔄 传递全局联系人索引: 10
2025-07-30 11:37:38,655 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:37:38,655 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:37:38,657 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 11:37:38,657 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 11:37:38,658 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:37:38,658 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:37:38,658 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 11:37:38,659 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 11:37:38,660 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 11:37:38,662 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:37:38,664 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:37:38,665 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 7146244
2025-07-30 11:37:38,665 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 7146244) - 增强版
2025-07-30 11:37:38,974 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:37:38,975 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:37:38,975 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:37:38,975 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:37:38,976 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:37:38,976 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:37:39,179 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:37:39,180 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:37:39,382 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:37:39,382 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:37:39,386 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 7146244 (API返回: None)
2025-07-30 11:37:39,687 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:37:39,687 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:37:39,688 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:37:39,688 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:37:39,689 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:37:39,689 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:37:39,689 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:37:39,694 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:37:39,695 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:37:40,182 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:37:40,183 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:37:40,431 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2902 个
2025-07-30 11:37:40,432 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 11 个联系人开始处理
2025-07-30 11:37:40,433 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2892 个
2025-07-30 11:37:40,433 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2892 个 (总计: 3135 个)
2025-07-30 11:37:40,433 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:37:40,434 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:37:40,434 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:37:40,434 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:37:40,435 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2892
2025-07-30 11:37:40,435 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18053818871 (许子怡)
2025-07-30 11:37:40,435 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:37:47,005 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18053818871
2025-07-30 11:37:47,005 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:37:47,006 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18053818871 执行添加朋友操作...
2025-07-30 11:37:47,006 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:37:47,006 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:37:47,007 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:37:47,010 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:37:47,014 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:37:47,023 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:37:47,023 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:37:47,024 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:37:47,024 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:37:47,025 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:37:47,025 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:37:47,026 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:37:47,034 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:37:47,038 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:37:47,042 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:37:47,052 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:37:47,056 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:37:47,062 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 11:37:47,066 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:37:47,569 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:37:47,572 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:37:47,643 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.58, 边缘比例0.0383
2025-07-30 11:37:47,652 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_113747.png
2025-07-30 11:37:47,658 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:37:47,664 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:37:47,668 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:37:47,671 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:37:47,673 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:37:47,684 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_113747.png
2025-07-30 11:37:47,688 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:37:47,692 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:37:47,699 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:37:47,702 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:37:47,705 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:37:47,707 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:37:47,715 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:37:47,717 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:37:47,728 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_113747.png
2025-07-30 11:37:47,736 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:37:47,739 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:37:47,750 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_113747.png
2025-07-30 11:37:47,774 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:37:47,784 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:37:47,787 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:37:47,790 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:37:48,097 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:37:48,878 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:37:48,881 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:37:48,886 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:37:48,886 - modules.wechat_auto_add_simple - INFO - ✅ 18053818871 添加朋友操作执行成功
2025-07-30 11:37:48,887 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:37:48,888 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:37:50,889 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:37:50,890 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:37:50,890 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:37:50,890 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:37:50,891 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:37:50,891 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:37:50,891 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:37:50,891 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:37:50,892 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:37:50,892 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18053818871
2025-07-30 11:37:50,893 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:37:50,893 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:37:50,893 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:37:50,893 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:37:50,894 - modules.friend_request_window - INFO -    📱 phone: '18053818871'
2025-07-30 11:37:50,894 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:37:50,894 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:37:51,392 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:37:51,393 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:37:51,393 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:37:51,394 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:37:51,395 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18053818871
2025-07-30 11:37:51,395 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:37:51,396 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:37:51,397 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:37:51,397 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:37:51,397 - modules.friend_request_window - INFO -    📱 手机号码: 18053818871
2025-07-30 11:37:51,398 - modules.friend_request_window - INFO -    🆔 准考证: 014325110238
2025-07-30 11:37:51,400 - modules.friend_request_window - INFO -    👤 姓名: 许子怡
2025-07-30 11:37:51,400 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:37:51,404 - modules.friend_request_window - INFO -    📝 备注格式: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:37:51,406 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:37:51,406 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:37:51,407 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:37:51,408 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4720190, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:37:51,410 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4720190)
2025-07-30 11:37:51,413 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:37:51,413 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:37:51,414 - modules.friend_request_window - INFO - 🔄 激活窗口: 4720190
2025-07-30 11:37:52,116 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:37:52,117 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:37:52,118 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:37:52,118 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:37:52,118 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:37:52,119 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:37:52,119 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:37:52,119 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:37:52,119 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:37:52,119 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:37:52,120 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:37:52,120 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:37:52,120 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:37:52,120 - modules.friend_request_window - INFO -    📝 remark参数: '014325110238-许子怡-2025-07-30 19:37:51' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:37:52,121 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:37:52,121 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:37:52,121 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:37:52,121 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:37:52,121 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:37:52,122 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:37:52,122 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:37:52,122 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:37:52,123 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:37:53,028 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:37:58,270 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:37:58,270 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:37:58,270 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:37:58,271 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:37:58,271 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:37:58,579 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:37:58,580 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:37:59,482 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:37:59,491 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:37:59,492 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:37:59,492 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:37:59,493 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:37:59,493 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:37:59,994 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:37:59,995 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:37:59,995 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:37:59,995 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:37:59,996 - modules.friend_request_window - INFO -    📝 内容: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:37:59,996 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:37:59,996 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110238-\xe8\xae\xb8\xe5\xad\x90\xe6\x80\xa1-2025-07-30 19:37:51'
2025-07-30 11:37:59,996 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:38:00,915 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:38:06,156 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:38:06,157 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:38:06,157 - modules.friend_request_window - INFO -    📝 原始文本: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:38:06,158 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:38:06,158 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:38:06,467 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:38:06,467 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:38:07,370 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:38:07,379 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:38:07,380 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:38:07,380 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:38:07,381 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:38:07,382 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:38:07,882 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110238-许子怡-2025-07-30 19:37:51'
2025-07-30 11:38:07,883 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:38:07,883 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:38:07,883 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:38:07,884 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:38:07,884 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:38:07,884 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:38:08,685 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:38:08,685 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:38:08,685 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:38:09,294 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:38:09,294 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:38:09,295 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:38:09,295 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:38:09,798 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 11:38:09,801 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 11:38:09,802 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 11:38:09,802 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 11:38:09,802 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 11:38:09,803 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 11:38:09,803 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 11:38:09,803 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 11:38:09,804 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 11:38:09,805 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:38:09,805 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 11:38:09,806 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 11:38:09,808 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 11:38:09,810 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 11:38:09,811 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 11:38:09,811 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 11:38:09,812 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:38:09,812 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 11:38:09,812 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 11:38:10,315 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 11:38:10,315 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 11:38:10,315 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 11:38:10,316 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 11:38:10,316 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 11:38:10,316 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 11:38:10,316 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 11:38:10,317 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 11:38:11,228 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 11:38:11,229 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 11:38:11,229 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 11:38:11,229 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 11:38:11,249 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 11:38:11,249 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:38:12,050 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:38:12,050 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 11:38:12,051 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 11:38:12,051 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:38:12,852 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:38:12,853 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 11:38:12,853 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 2/2
2025-07-30 11:38:12,853 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 11:38:12,854 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 11:38:12,854 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:38:13,655 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:38:13,655 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 11:38:13,655 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 11:38:15,656 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 11:38:15,658 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:38:15,659 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 11:38:15,659 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:38:15,661 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:38:15,661 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:38:15,663 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 11:38:15,664 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 11:38:15,664 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 11:38:15,683 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 11:38:15,683 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 11:38:15,683 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 11:38:15,684 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 197994
2025-07-30 11:38:15,685 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:38:15,685 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:38:15,990 - modules.window_manager - INFO - ✅ 激活方法 1 部分成功（窗口可见）
2025-07-30 11:38:15,993 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:38:15,996 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:38:16,006 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:38:16,009 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:38:16,009 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:38:16,012 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:38:16,013 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:38:16,014 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:38:16,014 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:38:16,216 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 11:38:16,229 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:38:16,229 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 11:38:16,230 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 11:38:16,230 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 11:38:16,230 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 11:38:16,231 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
