#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调用模板-微信自动化系统 - "操作过于频繁"错误检测和处理模块
功能：专门检测和处理微信系统的频率限制错误，实现智能窗口切换
作者：AI助手
创建时间：2025-07-26
版本：v1.0.0
"""

import time
import logging
import win32gui
import win32con
import win32api
import pyautogui
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import threading
import traceback
import sys
import os

# 添加modules目录到Python路径，确保导入正常工作
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)


@dataclass
class WindowInfo:
    """窗口信息数据类"""
    hwnd: int
    title: str
    class_name: str
    rect: Tuple[int, int, int, int]
    is_visible: bool
    is_enabled: bool


@dataclass
class ErrorDetectionResult:
    """错误检测结果数据类"""
    has_error: bool
    error_type: str
    error_message: str
    window_info: Optional[WindowInfo]
    detection_time: float
    confidence: float  # 检测置信度 0.0-1.0


class FrequencyErrorHandler:
    """微信"操作过于频繁"错误检测和处理器
    
    核心功能：
    1. 智能检测"操作过于频繁"错误提示
    2. 自动处理错误窗口（点击确定、关闭窗口）
    3. 智能切换到备用微信窗口
    4. 提供详细的错误报告和日志记录
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """初始化频率错误处理器
        
        Args:
            logger: 日志记录器，如果为None则创建新的
        """
        self.logger = logger or self._setup_logger()
        
        # 错误检测配置
        self.error_patterns = {
            "too_frequent": [
                "操作过于频繁",
                "操作太频繁", 
                "请稍后再试",
                "添加频繁",
                "操作频繁",
                "频繁操作",
                "稍后再试"
            ],
            "network_error": [
                "网络连接失败",
                "网络异常", 
                "连接超时",
                "网络错误"
            ],
            "permission_denied": [
                "权限不足",
                "无权限操作",
                "没有权限"
            ]
        }
        
        # 窗口检测配置
        self.target_windows = {
            "add_friend": {
                "class_names": ["Qt51514QWindowIcon", "WeChatMainWndForPC"],
                "title_patterns": ["添加朋友", "微信", "WeChat", "Weixin"],
                "size_range": {"min_width": 300, "max_width": 800, "min_height": 150, "max_height": 600},
                "exclude_sizes": [(330, 194)]  # 排除错误提示窗口大小
            },
            "main_wechat": {
                "class_names": ["Qt51514QWindowIcon", "WeChatMainWndForPC"],
                "title_patterns": ["微信", "Weixin"],
                "size_range": {"min_width": 300, "max_width": 1000, "min_height": 150, "max_height": 800}
            },
            "error_dialog": {
                "class_names": ["Qt51514QWindowIcon", "#32770", "Dialog"],
                "title_patterns": ["微信", "提示", "错误", "警告"],
                "size_range": {"min_width": 250, "max_width": 500, "min_height": 100, "max_height": 300}
            }
        }
        
        # 检测参数
        self.detection_timeout = 10.0  # 检测超时时间（秒）
        self.retry_attempts = 3  # 重试次数
        self.click_delay = 1.0  # 点击操作间延迟
        self.window_switch_delay = 2.0  # 窗口切换延迟
        
        # 状态跟踪
        self.last_detection_time = 0
        self.detection_history: List[ErrorDetectionResult] = []
        self.max_history_size = 50

        # 🆕 窗口错误状态跟踪
        self.window_error_status: Dict[int, Dict[str, Any]] = {}
        self.terminate_required = False

        # 🆕 重新开始标志管理
        self._restart_required = False

        # 线程安全锁
        self._lock = threading.Lock()

        self.logger.info("✅ 频率错误处理器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.FrequencyErrorHandler")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def detect_frequency_error_after_click(self, timeout: Optional[float] = None) -> ErrorDetectionResult:
        """在点击确定按钮后检测"操作过于频繁"错误

        这是核心检测方法，在点击"确定"按钮后立即调用

        Args:
            timeout: 检测超时时间，默认使用配置值

        Returns:
            ErrorDetectionResult: 检测结果
        """
        timeout = timeout or self.detection_timeout
        start_time = time.time()

        self.logger.info("🔍 开始检测'操作过于频繁'错误...")
        self.logger.info(f"⏱️ 检测超时时间: {timeout}秒")

        try:
            # 等待窗口响应
            time.sleep(0.5)

            while time.time() - start_time < timeout:
                # 🆕 优先检测错误对话框（基于截图分析）
                dialog_result = self._check_error_dialog_enhanced()
                if dialog_result.has_error:
                    self.logger.info("⚠️ 检测到错误对话框")
                    return dialog_result

                # 检测添加朋友窗口
                add_friend_result = self._check_add_friend_window()
                if add_friend_result.has_error:
                    self.logger.info("📱 在添加朋友窗口中检测到错误")
                    return add_friend_result

                # 检测主微信窗口
                main_window_result = self._check_main_wechat_window()
                if main_window_result.has_error:
                    self.logger.info("🏠 在主微信窗口中检测到错误")
                    return main_window_result

                # 短暂等待后继续检测
                time.sleep(0.2)

            # 超时未检测到错误
            self.logger.info("✅ 检测完成，未发现'操作过于频繁'错误")
            return ErrorDetectionResult(
                has_error=False,
                error_type="none",
                error_message="未检测到错误",
                window_info=None,
                detection_time=time.time() - start_time,
                confidence=1.0
            )

        except Exception as e:
            self.logger.error(f"❌ 错误检测异常: {e}")
            self.logger.error(traceback.format_exc())
            return ErrorDetectionResult(
                has_error=False,
                error_type="detection_error",
                error_message=f"检测异常: {str(e)}",
                window_info=None,
                detection_time=time.time() - start_time,
                confidence=0.0
            )
    
    def _check_add_friend_window(self) -> ErrorDetectionResult:
        """检查添加朋友窗口中的错误信息"""
        try:
            windows = self._find_windows_by_criteria("add_friend")
            
            for window_info in windows:
                # 获取窗口文本内容
                text_content = self._extract_window_text(window_info.hwnd)
                
                # 检查是否包含错误信息
                error_result = self._analyze_text_for_errors(text_content, window_info)
                if error_result.has_error:
                    return error_result
            
            return self._create_no_error_result()
            
        except Exception as e:
            self.logger.error(f"❌ 检查添加朋友窗口异常: {e}")
            return self._create_no_error_result()
    
    def _check_main_wechat_window(self) -> ErrorDetectionResult:
        """检查主微信窗口中的错误信息"""
        try:
            windows = self._find_windows_by_criteria("main_wechat")
            
            for window_info in windows:
                # 特别检查指定大小的微信窗口 (330x194)
                width = window_info.rect[2] - window_info.rect[0]
                height = window_info.rect[3] - window_info.rect[1]
                
                if width == 330 and height == 194:
                    self.logger.info(f"🎯 检测到目标微信窗口 (330x194): {window_info.title}")
                    
                    # 获取窗口文本内容
                    text_content = self._extract_window_text(window_info.hwnd)
                    
                    # 检查是否包含错误信息
                    error_result = self._analyze_text_for_errors(text_content, window_info)
                    if error_result.has_error:
                        return error_result
            
            return self._create_no_error_result()
            
        except Exception as e:
            self.logger.error(f"❌ 检查主微信窗口异常: {e}")
            return self._create_no_error_result()
    
    def _check_error_dialog(self) -> ErrorDetectionResult:
        """检查错误对话框"""
        try:
            windows = self._find_windows_by_criteria("error_dialog")

            for window_info in windows:
                # 获取窗口文本内容
                text_content = self._extract_window_text(window_info.hwnd)

                # 检查是否包含错误信息
                error_result = self._analyze_text_for_errors(text_content, window_info)
                if error_result.has_error:
                    return error_result

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 检查错误对话框异常: {e}")
            return self._create_no_error_result()

    def _check_error_dialog_enhanced(self) -> ErrorDetectionResult:
        """增强的错误对话框检测 - 基于窗口特征识别微信错误提示"""
        try:
            found_windows = []

            def enum_windows_callback(hwnd, _):
                try:
                    if not win32gui.IsWindowVisible(hwnd):
                        return True

                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)

                    # 检查窗口特征
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]

                    # 🎯 基于实际测试的精确窗口特征匹配
                    is_wechat_error_dialog = (
                        # 精确匹配微信错误提示窗口特征
                        title == "Weixin" and
                        class_name == "Qt51514QWindowIcon" and
                        width == 330 and height == 194
                    )

                    if is_wechat_error_dialog:
                        self.logger.info(f"🎯 发现疑似微信错误提示窗口: {title} ({width}x{height})")

                        window_info = WindowInfo(
                            hwnd=hwnd,
                            title=title or "",
                            class_name=class_name or "",
                            rect=rect,
                            is_visible=True,
                            is_enabled=bool(win32gui.IsWindowEnabled(hwnd))
                        )
                        found_windows.append(window_info)

                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)

            # 检查找到的窗口
            for window_info in found_windows:
                self.logger.info(f"🔍 分析微信错误窗口: {window_info.title}")

                # 🆕 基于窗口特征的错误检测（不依赖文本提取）
                confidence = self._calculate_error_probability_by_window_features(window_info)

                if confidence > 0.7:  # 如果置信度超过70%
                    self.logger.info(f"🚨 基于窗口特征检测到错误，置信度: {confidence:.2f}")

                    return ErrorDetectionResult(
                        has_error=True,
                        error_type="too_frequent",
                        error_message="基于窗口特征检测到'操作过于频繁'错误",
                        window_info=window_info,
                        detection_time=time.time(),
                        confidence=confidence
                    )

                # 备用：尝试文本提取
                text_content = self._extract_window_text_enhanced(window_info.hwnd)
                if text_content:
                    error_result = self._analyze_text_for_errors(text_content, window_info)
                    if error_result.has_error:
                        self.logger.info(f"🎯 通过文本分析发现错误: {error_result.error_message}")
                        return error_result

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 增强错误对话框检测异常: {e}")
            return self._create_no_error_result()

    def _calculate_error_probability_by_window_features(self, window_info: WindowInfo) -> float:
        """基于窗口特征计算错误概率"""
        try:
            confidence = 0.0

            # 特征1: 精确的窗口大小匹配 (330x194)
            width = window_info.rect[2] - window_info.rect[0]
            height = window_info.rect[3] - window_info.rect[1]

            if width == 330 and height == 194:
                confidence += 0.4  # 40%权重
                self.logger.info(f"✅ 窗口大小匹配 (330x194): +0.4")

            # 特征2: 窗口标题匹配
            if window_info.title == "Weixin":
                confidence += 0.3  # 30%权重
                self.logger.info(f"✅ 窗口标题匹配 (Weixin): +0.3")

            # 特征3: 窗口类名匹配
            if window_info.class_name == "Qt51514QWindowIcon":
                confidence += 0.2  # 20%权重
                self.logger.info(f"✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2")

            # 特征4: 窗口位置特征（错误提示通常在屏幕中央或特定位置）
            screen_width = 1920  # 假设的屏幕宽度，可以动态获取
            screen_height = 1080  # 假设的屏幕高度

            window_center_x = (window_info.rect[0] + window_info.rect[2]) // 2
            window_center_y = (window_info.rect[1] + window_info.rect[3]) // 2

            # 如果窗口在屏幕中央附近
            if abs(window_center_x - screen_width // 2) < 200 and \
               abs(window_center_y - screen_height // 2) < 200:
                confidence += 0.1  # 10%权重
                self.logger.info(f"✅ 窗口位置合理 (中央附近): +0.1")

            self.logger.info(f"📊 窗口特征分析完成，总置信度: {confidence:.2f}")
            return confidence

        except Exception as e:
            self.logger.error(f"❌ 计算窗口特征置信度异常: {e}")
            return 0.0

    def _extract_window_text_enhanced(self, hwnd: int) -> str:
        """增强的窗口文本提取 - 专门针对错误对话框"""
        try:
            all_texts = []

            # 获取窗口标题
            title = win32gui.GetWindowText(hwnd)
            if title.strip():
                all_texts.append(title.strip())

            # 深度遍历所有子控件
            def enum_all_children(parent_hwnd, depth=0):
                if depth > 5:  # 限制递归深度
                    return

                def enum_callback(child_hwnd, _):
                    try:
                        child_text = win32gui.GetWindowText(child_hwnd)
                        child_class = win32gui.GetClassName(child_hwnd)

                        if child_text.strip():
                            all_texts.append(child_text.strip())

                            # 特别关注包含关键词的文本
                            keywords = ["操作", "频繁", "稍后", "再试", "请", "添加"]
                            if any(keyword in child_text for keyword in keywords):
                                self.logger.info(f"🎯 发现关键文本: [{child_class}] {child_text}")

                        # 递归检查子控件
                        enum_all_children(child_hwnd, depth + 1)

                    except:
                        pass
                    return True

                try:
                    win32gui.EnumChildWindows(parent_hwnd, enum_callback, 0)
                except:
                    pass

            enum_all_children(hwnd)

            combined_text = " ".join(all_texts)

            if combined_text.strip():
                self.logger.info(f"🔍 增强提取到文本: {combined_text[:300]}...")

            return combined_text

        except Exception as e:
            self.logger.error(f"❌ 增强文本提取异常: {e}")
            return ""

    def handle_frequency_error(self, detection_result: ErrorDetectionResult) -> bool:
        """处理检测到的频率错误

        执行顺序：
        1. 点击错误提示窗口的"确定"按钮
        2. 关闭当前的添加朋友窗口
        3. 关闭当前的微信主窗口
        4. 切换到微信2窗口

        Args:
            detection_result: 错误检测结果

        Returns:
            bool: 处理是否成功
        """
        if not detection_result.has_error:
            self.logger.warning("⚠️ 未检测到错误，无需处理")
            return True

        self.logger.info("🚨 开始处理'操作过于频繁'错误...")
        self.logger.info(f"📋 错误类型: {detection_result.error_type}")
        self.logger.info(f"📝 错误信息: {detection_result.error_message}")

        try:
            # 记录处理历史
            with self._lock:
                self.detection_history.append(detection_result)
                if len(self.detection_history) > self.max_history_size:
                    self.detection_history.pop(0)

            # 步骤1: 点击错误提示窗口的"确定"按钮
            if not self._click_error_dialog_ok_button(detection_result):
                self.logger.error("❌ 点击错误提示确定按钮失败")
                return False

            # 步骤2: 关闭添加朋友窗口
            if not self._close_add_friend_windows():
                self.logger.warning("⚠️ 关闭添加朋友窗口失败，继续执行")

            # 步骤3: 关闭当前微信主窗口
            if not self._close_current_wechat_window():
                self.logger.warning("⚠️ 关闭微信主窗口失败，继续执行")

            # 步骤4: 切换到微信2窗口
            if not self._switch_to_wechat2():
                self.logger.error("❌ 切换到微信2窗口失败")
                return False

            # 步骤5: 执行主界面操作流程（新增关键步骤）
            if not self._execute_main_interface_operations_after_switch():
                self.logger.error("❌ 切换窗口后主界面操作失败")
                return False

            # 🆕 步骤6: 设置重新开始标志
            self._set_restart_flag()
            self.logger.info("🔄 频率错误处理完成，已设置重新开始标志")

            self.logger.info("✅ 频率错误处理完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理频率错误异常: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _click_error_dialog_ok_button(self, detection_result: ErrorDetectionResult) -> bool:
        """点击错误提示窗口的确定按钮 - 增强版：支持多种点击方式和验证"""
        try:
            if not detection_result.window_info:
                self.logger.error("❌ 无窗口信息，无法点击确定按钮")
                return False

            window_info = detection_result.window_info
            self.logger.info(f"🖱️ 准备点击错误窗口确定按钮: {window_info.title}")
            self.logger.info(f"📊 窗口大小: {window_info.rect[2] - window_info.rect[0]}x{window_info.rect[3] - window_info.rect[1]}")

            # 激活窗口
            if not self._activate_window(window_info.hwnd):
                self.logger.error("❌ 激活错误窗口失败")
                return False

            # 查找确定按钮
            ok_button_pos = self._find_ok_button_in_window(window_info.hwnd)
            if not ok_button_pos:
                self.logger.error("❌ 未找到确定按钮")
                return False

            # 🔧 增强的点击操作：多种方式确保成功
            success = self._enhanced_click_ok_button(ok_button_pos, window_info.hwnd)

            if success:
                self.logger.info("✅ 成功点击错误提示确定按钮")
                return True
            else:
                self.logger.error("❌ 所有点击方式都失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 点击确定按钮异常: {e}")
            import traceback
            self.logger.error(f"详细异常信息: {traceback.format_exc()}")
            return False

    def _enhanced_click_ok_button(self, button_pos: Tuple[int, int], hwnd: int) -> bool:
        """增强的确定按钮点击方法 - 支持多种点击方式和验证"""
        try:
            button_x, button_y = button_pos
            self.logger.info(f"🎯 开始增强点击操作，目标坐标: ({button_x}, {button_y})")

            # 记录点击前的窗口状态
            window_exists_before = win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)
            self.logger.info(f"📊 点击前窗口状态: {'存在且可见' if window_exists_before else '不存在或不可见'}")

            # 方法1: pyautogui单击
            self.logger.info("🖱️ 方法1: pyautogui单击...")
            try:
                pyautogui.click(button_x, button_y)
                time.sleep(0.8)  # 等待响应

                # 验证点击效果
                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法1成功：pyautogui单击")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法1失败: {e}")

            # 方法2: pyautogui双击
            self.logger.info("🖱️ 方法2: pyautogui双击...")
            try:
                pyautogui.doubleClick(button_x, button_y)
                time.sleep(0.8)

                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法2成功：pyautogui双击")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法2失败: {e}")

            # 方法3: win32api点击
            self.logger.info("🖱️ 方法3: win32api点击...")
            try:
                # 移动鼠标到目标位置
                win32api.SetCursorPos((button_x, button_y))
                time.sleep(0.2)

                # 执行点击
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                time.sleep(0.1)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                time.sleep(0.8)

                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法3成功：win32api点击")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法3失败: {e}")

            # 方法4: 多次连续点击
            self.logger.info("🖱️ 方法4: 多次连续点击...")
            try:
                for i in range(3):
                    self.logger.info(f"   第{i+1}次点击...")
                    pyautogui.click(button_x, button_y)
                    time.sleep(0.3)

                    if self._verify_click_success(hwnd):
                        self.logger.info(f"✅ 方法4成功：第{i+1}次点击")
                        return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法4失败: {e}")

            # 方法5: 发送回车键（作为备选）
            self.logger.info("⌨️ 方法5: 发送回车键...")
            try:
                # 确保窗口激活
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 发送回车键
                win32api.keybd_event(13, 0, 0, 0)  # Enter按下
                win32api.keybd_event(13, 0, win32con.KEYEVENTF_KEYUP, 0)  # Enter释放
                time.sleep(0.8)

                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法5成功：回车键")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法5失败: {e}")

            self.logger.error("❌ 所有点击方法都失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 增强点击操作异常: {e}")
            return False

    def _verify_click_success(self, hwnd: int) -> bool:
        """验证点击是否成功（通过检查窗口是否关闭）"""
        try:
            # 检查窗口是否仍然存在且可见
            window_still_exists = win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)

            if not window_still_exists:
                self.logger.info("✅ 验证成功：错误提示窗口已关闭")
                return True
            else:
                self.logger.info("⚠️ 验证失败：错误提示窗口仍然存在")
                return False

        except Exception as e:
            self.logger.warning(f"⚠️ 验证点击效果异常: {e}")
            # 异常情况下假设成功，继续流程
            return True

    def _close_add_friend_windows(self) -> bool:
        """关闭所有添加朋友窗口 - 增强版，专门处理顽固窗口"""
        try:
            self.logger.info("🔄 开始关闭添加朋友窗口...")

            windows = self._find_windows_by_criteria("add_friend")
            closed_count = 0

            for window_info in windows:
                # 先尝试标准关闭方法
                if self._close_window(window_info.hwnd):
                    closed_count += 1
                    self.logger.info(f"✅ 已关闭添加朋友窗口: {window_info.title}")
                else:
                    # 标准方法失败，尝试特殊处理
                    self.logger.info(f"🔄 标准方法失败，尝试特殊处理: {window_info.title}")
                    if self._close_add_friend_window_special(window_info):
                        closed_count += 1
                        self.logger.info(f"✅ 特殊方法成功关闭窗口: {window_info.title}")
                    else:
                        self.logger.warning(f"⚠️ 所有方法都失败，跳过窗口: {window_info.title}")

            self.logger.info(f"📊 关闭添加朋友窗口统计: {closed_count}/{len(windows)}")

            # 即使部分窗口关闭失败，也认为操作基本成功
            # 因为主要目标是处理错误并切换窗口
            return True

        except Exception as e:
            self.logger.error(f"❌ 关闭添加朋友窗口异常: {e}")
            return True  # 返回True以继续后续流程

    def _close_add_friend_window_special(self, window_info: WindowInfo) -> bool:
        """特殊方法关闭添加朋友窗口 - 使用精确坐标点击关闭按钮"""
        try:
            hwnd = window_info.hwnd

            # 方法1: 使用精确坐标点击添加朋友窗口的关闭按钮 (305, 15)
            self.logger.info("   特殊方法1: 点击添加朋友窗口关闭按钮 (305, 15)...")
            try:
                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)

                # 使用精确坐标 (305, 15)
                close_x = 305
                close_y = 15

                self.logger.info(f"   点击添加朋友窗口关闭按钮位置: ({close_x}, {close_y})")

                # 多重点击确保成功
                for attempt in range(3):
                    self.logger.info(f"   点击尝试 {attempt + 1}/3...")

                    # 移动鼠标到位置
                    win32api.SetCursorPos((close_x, close_y))
                    time.sleep(0.2)

                    # 执行点击
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                    time.sleep(0.1)
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                    time.sleep(0.8)

                    # 检查是否关闭
                    if self._is_window_closed(hwnd):
                        self.logger.info(f"   ✅ 第{attempt + 1}次点击成功关闭添加朋友窗口")
                        return True

                # 如果win32api失败，尝试pyautogui
                self.logger.info("   尝试pyautogui点击关闭按钮...")
                pyautogui.click(close_x, close_y)
                time.sleep(0.5)
                pyautogui.click(close_x, close_y)  # 双击确保
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ pyautogui点击成功关闭添加朋友窗口")
                    return True

            except Exception as e:
                self.logger.debug(f"   精确坐标点击失败: {e}")

            # 方法2: 尝试发送Alt+F4
            self.logger.info("   特殊方法2: 发送Alt+F4...")
            try:
                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 发送Alt+F4
                win32api.keybd_event(18, 0, 0, 0)  # Alt按下
                win32api.keybd_event(115, 0, 0, 0)  # F4按下
                win32api.keybd_event(115, 0, win32con.KEYEVENTF_KEYUP, 0)  # F4释放
                win32api.keybd_event(18, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放
                time.sleep(1.0)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ Alt+F4成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   Alt+F4方法失败: {e}")

            # 方法3: 尝试ESC键
            self.logger.info("   特殊方法3: 按ESC键...")
            try:
                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 按ESC键
                win32api.keybd_event(27, 0, 0, 0)  # ESC按下
                win32api.keybd_event(27, 0, win32con.KEYEVENTF_KEYUP, 0)  # ESC释放
                time.sleep(1.0)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ ESC键成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   ESC键方法失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"❌ 特殊关闭方法异常: {e}")
            return False

    def _close_current_wechat_window(self) -> bool:
        """关闭当前微信主窗口"""
        try:
            self.logger.info("🔄 开始关闭当前微信主窗口...")

            # 获取当前前台窗口
            current_hwnd = win32gui.GetForegroundWindow()
            current_window_info = self._get_window_info(current_hwnd)

            # 检查是否是微信窗口
            if self._is_wechat_window(current_window_info):
                if self._close_window(current_hwnd):
                    self.logger.info(f"✅ 已关闭当前微信窗口: {current_window_info.title}")
                    return True
                else:
                    self.logger.warning(f"⚠️ 关闭当前微信窗口失败: {current_window_info.title}")

            # 如果当前窗口不是微信窗口，查找并关闭微信主窗口
            windows = self._find_windows_by_criteria("main_wechat")
            for window_info in windows:
                if self._close_window(window_info.hwnd):
                    self.logger.info(f"✅ 已关闭微信主窗口: {window_info.title}")
                    return True

            self.logger.warning("⚠️ 未找到可关闭的微信主窗口")
            return False

        except Exception as e:
            self.logger.error(f"❌ 关闭微信主窗口异常: {e}")
            return False

    def _switch_to_wechat2(self) -> bool:
        """切换到微信2窗口（优化版：使用window_manager.py）"""
        try:
            self.logger.info("🔄 开始切换到微信2窗口...")

            # 等待窗口关闭完成
            time.sleep(self.window_switch_delay)

            # 🔧 使用现有的window_manager.py进行窗口切换
            try:
                # 尝试多种导入方式
                try:
                    from modules.window_manager import WeChatWindowManager
                except ImportError:
                    from window_manager import WeChatWindowManager
                self.logger.info("📦 使用WeChatWindowManager进行窗口切换")

                # 创建窗口管理器实例
                window_manager = WeChatWindowManager()

                # 查找所有微信窗口
                self.logger.info("🔍 查找所有微信窗口...")
                all_windows = window_manager.find_all_wechat_windows()

                if len(all_windows) <= 1:
                    self.logger.warning("⚠️ 只找到一个或没有微信窗口，尝试使用备用方法")
                    return self._enhanced_fallback_switch_to_wechat2()

                # 使用window_manager的切换功能
                self.logger.info("🔄 使用window_manager切换到下一个微信窗口...")
                next_window = window_manager.switch_to_next_window()

                if next_window:
                    self.logger.info(f"✅ 成功切换到微信窗口: {next_window['title']}")
                    self.logger.info(f"   窗口句柄: {next_window['hwnd']}")
                    self.logger.info(f"   窗口类名: {next_window.get('class_name', '未知')}")

                    # 🆕 在窗口切换后立即检查并同步添加朋友窗口位置
                    self.logger.info("🔧 窗口切换完成，检查添加朋友窗口位置同步...")
                    try:
                        # 短暂等待确保窗口切换完全完成
                        time.sleep(1.0)
                        position_sync_success = self._sync_add_friend_window_position()
                        if position_sync_success:
                            self.logger.info("✅ 窗口切换后添加朋友窗口位置同步成功")
                        else:
                            self.logger.warning("⚠️ 窗口切换后添加朋友窗口位置同步失败，但继续执行")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 窗口切换后位置同步异常: {e}")

                    return True
                else:
                    self.logger.error("❌ window_manager切换窗口失败")
                    return self._enhanced_fallback_switch_to_wechat2()

            except ImportError as e:
                self.logger.error(f"❌ 导入WeChatWindowManager失败: {e}")
                return self._enhanced_fallback_switch_to_wechat2()
            except Exception as e:
                self.logger.error(f"❌ 使用window_manager切换窗口异常: {e}")
                return self._enhanced_fallback_switch_to_wechat2()

        except Exception as e:
            self.logger.error(f"❌ 切换到微信2窗口异常: {e}")
            return False

    def _execute_main_interface_operations_after_switch(self) -> bool:
        """在切换窗口后执行主界面操作流程（修复工作流程问题）"""
        try:
            self.logger.info("🚀 开始执行切换窗口后的主界面操作流程...")
            self.logger.info("📋 目标：在新的微信窗口中执行主界面点击操作")
            self.logger.info("💡 这是确保正确工作流程的关键步骤，不能跳过")

            # 等待窗口切换完成
            time.sleep(2)

            # 导入主界面操作模块
            try:
                # 尝试多种导入方式
                try:
                    from modules.main_interface import WeChatMainInterface
                except ImportError:
                    from main_interface import WeChatMainInterface
                self.logger.info("📦 成功导入主界面操作模块")
            except ImportError as e:
                self.logger.error(f"❌ 导入主界面操作模块失败: {e}")
                return False

            # 创建主界面操作实例
            try:
                main_interface = WeChatMainInterface("config.json")
                self.logger.info("🔧 主界面操作模块初始化成功")
            except Exception as e:
                self.logger.error(f"❌ 主界面操作模块初始化失败: {e}")
                return False

            # 执行主界面操作流程
            self.logger.info("🎯 开始执行主界面操作流程...")
            self.logger.info("📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友")

            try:
                success = main_interface.execute_main_interface_flow()

                if success:
                    self.logger.info("✅ 切换窗口后主界面操作流程执行成功")

                    # 🆕 关键修复：确保"添加朋友"窗口位置同步到 (1200, 0)
                    self.logger.info("🔧 开始执行添加朋友窗口位置同步...")
                    position_sync_success = self._sync_add_friend_window_position()

                    if position_sync_success:
                        self.logger.info("✅ 添加朋友窗口位置同步成功")
                    else:
                        self.logger.warning("⚠️ 添加朋友窗口位置同步失败，但继续执行")

                    self.logger.info("✅ 微信主界面点击操作已完成，可以继续后续操作")
                    return True
                else:
                    self.logger.error("❌ 切换窗口后主界面操作流程执行失败")
                    return False

            except Exception as e:
                self.logger.error(f"❌ 执行主界面操作流程异常: {e}")
                import traceback
                self.logger.error(f"详细异常信息: {traceback.format_exc()}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 切换窗口后主界面操作异常: {e}")
            import traceback
            self.logger.error(f"详细异常信息: {traceback.format_exc()}")
            return False

    def _sync_add_friend_window_position(self) -> bool:
        """同步"添加朋友"窗口位置到标准坐标 (1200, 0)

        这是频率错误处理后的关键步骤，确保"添加朋友"窗口位置与微信1窗口保持一致

        Returns:
            bool: 位置同步是否成功
        """
        try:
            self.logger.info("🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...")

            # 目标位置坐标
            target_x, target_y = 1200, 0

            # 等待添加朋友窗口出现
            time.sleep(1.0)

            # 查找添加朋友窗口
            add_friend_windows = self._find_add_friend_windows()

            if not add_friend_windows:
                self.logger.warning("⚠️ 未找到添加朋友窗口，尝试等待窗口出现...")

                # 等待更长时间后重试
                time.sleep(2.0)
                add_friend_windows = self._find_add_friend_windows()

                if not add_friend_windows:
                    self.logger.error("❌ 仍未找到添加朋友窗口，位置同步失败")
                    return False

            # 对所有找到的添加朋友窗口执行位置同步
            sync_success_count = 0
            for window_info in add_friend_windows:
                hwnd = window_info.hwnd
                current_rect = window_info.rect
                current_x, current_y = current_rect[0], current_rect[1]
                window_width = current_rect[2] - current_rect[0]
                window_height = current_rect[3] - current_rect[1]

                self.logger.info(f"📍 处理添加朋友窗口: {window_info.title}")
                self.logger.info(f"   当前位置: ({current_x}, {current_y})")
                self.logger.info(f"   窗口大小: {window_width}x{window_height}")
                self.logger.info(f"   目标位置: ({target_x}, {target_y})")

                # 检查是否已经在目标位置
                if abs(current_x - target_x) <= 10 and abs(current_y - target_y) <= 10:
                    self.logger.info("✅ 窗口已经在目标位置，无需移动")
                    sync_success_count += 1
                    continue

                # 执行窗口移动 - 增强版本，多种方法尝试
                try:
                    self.logger.info(f"🔧 开始移动窗口 {hwnd} 到目标位置...")

                    # 方法1: 标准移动方法
                    success_method1 = self._try_move_window_method1(hwnd, target_x, target_y, window_width, window_height)

                    if success_method1:
                        sync_success_count += 1
                        continue

                    # 方法2: 增强移动方法（如果方法1失败）
                    self.logger.warning("⚠️ 标准移动方法失败，尝试增强移动方法...")
                    success_method2 = self._try_move_window_method2(hwnd, target_x, target_y, window_width, window_height)

                    if success_method2:
                        sync_success_count += 1
                        continue

                    # 方法3: 强制移动方法（如果前两种方法都失败）
                    self.logger.warning("⚠️ 增强移动方法失败，尝试强制移动方法...")
                    success_method3 = self._try_move_window_method3(hwnd, target_x, target_y, window_width, window_height)

                    if success_method3:
                        sync_success_count += 1
                    else:
                        self.logger.error(f"❌ 所有移动方法都失败，窗口 {hwnd} 无法移动")

                except Exception as e:
                    self.logger.error(f"❌ 移动添加朋友窗口异常: {e}")
                    # 继续处理其他窗口
                    continue

            # 评估同步结果
            total_windows = len(add_friend_windows)
            if sync_success_count > 0:
                self.logger.info(f"📊 位置同步统计: {sync_success_count}/{total_windows} 个窗口同步成功")
                return True
            else:
                self.logger.error(f"❌ 所有添加朋友窗口位置同步都失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 同步添加朋友窗口位置异常: {e}")
            import traceback
            self.logger.error(f"详细异常信息: {traceback.format_exc()}")
            return False

    def _find_add_friend_windows(self) -> List[WindowInfo]:
        """查找所有添加朋友窗口

        Returns:
            List[WindowInfo]: 添加朋友窗口信息列表
        """
        try:
            add_friend_windows = []

            def enum_windows_callback(hwnd, _):
                try:
                    if not win32gui.IsWindowVisible(hwnd):
                        return True

                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)

                    # 检查是否为添加朋友窗口
                    is_add_friend_window = (
                        title in ['添加朋友', 'Add Friends'] or
                        (class_name in ["Qt51514QWindowIcon", "WeChatMainWndForPC"] and
                         any(keyword in title for keyword in ["添加", "朋友", "Add", "Friend"]))
                    )

                    if is_add_friend_window:
                        window_info = WindowInfo(
                            hwnd=hwnd,
                            title=title or "",
                            class_name=class_name or "",
                            rect=rect,
                            is_visible=True,
                            is_enabled=bool(win32gui.IsWindowEnabled(hwnd))
                        )
                        add_friend_windows.append(window_info)
                        self.logger.debug(f"🔍 找到添加朋友窗口: {title} (句柄: {hwnd})")

                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)

            self.logger.info(f"🔍 共找到 {len(add_friend_windows)} 个添加朋友窗口")
            return add_friend_windows

        except Exception as e:
            self.logger.error(f"❌ 查找添加朋友窗口异常: {e}")
            return []

    def _try_move_window_method1(self, hwnd: int, target_x: int, target_y: int,
                                window_width: int, window_height: int) -> bool:
        """方法1: 标准窗口移动方法"""
        try:
            self.logger.debug(f"🔧 方法1: 标准移动窗口 {hwnd}")

            # 激活窗口
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.3)

            # 移动窗口到目标位置
            win32gui.MoveWindow(hwnd, target_x, target_y, window_width, window_height, True)
            time.sleep(0.5)

            # 验证移动结果
            final_rect = win32gui.GetWindowRect(hwnd)
            final_x, final_y = final_rect[0], final_rect[1]

            if abs(final_x - target_x) <= 10 and abs(final_y - target_y) <= 10:
                self.logger.info(f"✅ 方法1成功: 窗口移动到位置 ({final_x}, {final_y})")
                return True
            else:
                self.logger.warning(f"⚠️ 方法1失败: 窗口位置 ({final_x}, {final_y})，目标 ({target_x}, {target_y})")
                return False

        except Exception as e:
            self.logger.error(f"❌ 方法1异常: {e}")
            return False

    def _try_move_window_method2(self, hwnd: int, target_x: int, target_y: int,
                                window_width: int, window_height: int) -> bool:
        """方法2: 增强窗口移动方法（多次尝试 + 强制激活）"""
        try:
            self.logger.debug(f"🔧 方法2: 增强移动窗口 {hwnd}")

            # 强制激活窗口（多种方法）
            try:
                # 尝试多种激活方法
                win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE
                time.sleep(0.2)
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.2)
                win32gui.BringWindowToTop(hwnd)
                time.sleep(0.3)
            except:
                pass

            # 多次尝试移动
            for attempt in range(3):
                try:
                    self.logger.debug(f"   尝试 {attempt + 1}/3")

                    # 移动窗口
                    win32gui.MoveWindow(hwnd, target_x, target_y, window_width, window_height, True)
                    time.sleep(0.5)

                    # 验证结果
                    final_rect = win32gui.GetWindowRect(hwnd)
                    final_x, final_y = final_rect[0], final_rect[1]

                    if abs(final_x - target_x) <= 10 and abs(final_y - target_y) <= 10:
                        self.logger.info(f"✅ 方法2成功: 第{attempt + 1}次尝试，窗口移动到位置 ({final_x}, {final_y})")
                        return True
                    else:
                        self.logger.debug(f"   第{attempt + 1}次尝试失败: ({final_x}, {final_y})")
                        time.sleep(0.3)  # 短暂等待后重试

                except Exception as e:
                    self.logger.debug(f"   第{attempt + 1}次尝试异常: {e}")
                    time.sleep(0.3)

            self.logger.warning("⚠️ 方法2失败: 3次尝试都未成功")
            return False

        except Exception as e:
            self.logger.error(f"❌ 方法2异常: {e}")
            return False

    def _try_move_window_method3(self, hwnd: int, target_x: int, target_y: int,
                                window_width: int, window_height: int) -> bool:
        """方法3: 强制窗口移动方法（使用SetWindowPos）"""
        try:
            self.logger.debug(f"🔧 方法3: 强制移动窗口 {hwnd}")

            # 导入额外的Windows API
            import win32con

            # 强制显示和激活窗口
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.2)
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)
            except:
                pass

            # 使用SetWindowPos进行强制移动
            try:
                # HWND_TOP = 0, SWP_SHOWWINDOW = 0x0040
                win32gui.SetWindowPos(
                    hwnd, 0, target_x, target_y, window_width, window_height,
                    0x0040  # SWP_SHOWWINDOW
                )
                time.sleep(0.8)

                # 验证结果
                final_rect = win32gui.GetWindowRect(hwnd)
                final_x, final_y = final_rect[0], final_rect[1]

                if abs(final_x - target_x) <= 15 and abs(final_y - target_y) <= 15:  # 稍微放宽误差
                    self.logger.info(f"✅ 方法3成功: 窗口移动到位置 ({final_x}, {final_y})")
                    return True
                else:
                    self.logger.warning(f"⚠️ 方法3失败: 窗口位置 ({final_x}, {final_y})，目标 ({target_x}, {target_y})")

                    # 最后尝试：强制设置位置（即使有偏差也接受）
                    if abs(final_x - target_x) <= 50 and abs(final_y - target_y) <= 50:
                        self.logger.info(f"✅ 方法3部分成功: 窗口移动到接近位置 ({final_x}, {final_y})")
                        return True

                    return False

            except Exception as e:
                self.logger.error(f"❌ SetWindowPos调用失败: {e}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 方法3异常: {e}")
            return False

    def _enhanced_fallback_switch_to_wechat2(self) -> bool:
        """增强的备用微信2窗口切换方法"""
        try:
            self.logger.info("🔄 使用增强备用方法切换到微信2窗口...")

            # 查找微信2窗口
            wechat2_windows = self._find_wechat2_windows()

            if not wechat2_windows:
                self.logger.error("❌ 未找到微信2窗口")
                return False

            # 选择第一个微信2窗口
            target_window = wechat2_windows[0]
            self.logger.info(f"🎯 找到微信2窗口: {target_window.title}")

            # 🔧 使用增强的激活方法
            if self._enhanced_activate_window(target_window.hwnd):
                self.logger.info("✅ 增强备用方法成功切换到微信2窗口")
                return True
            else:
                self.logger.error("❌ 增强备用方法激活微信2窗口失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 增强备用方法切换到微信2窗口异常: {e}")
            return False

    def _enhanced_activate_window(self, hwnd: int) -> bool:
        """增强的窗口激活方法"""
        try:
            self.logger.info(f"🎯 增强激活窗口: {hwnd}")

            # 方法1: 使用window_manager的激活功能
            try:
                # 尝试多种导入方式
                try:
                    from modules.window_manager import WeChatWindowManager
                except ImportError:
                    from window_manager import WeChatWindowManager
                window_manager = WeChatWindowManager()

                if window_manager.activate_window(hwnd):
                    self.logger.info("✅ 使用window_manager激活成功")
                    return True
                else:
                    self.logger.warning("⚠️ window_manager激活失败，尝试传统方法")
            except Exception as e:
                self.logger.warning(f"⚠️ window_manager激活异常: {e}")

            # 方法2: 传统激活方法
            return self._activate_window(hwnd)

        except Exception as e:
            self.logger.error(f"❌ 增强激活窗口异常: {e}")
            return False

    # ==================== 辅助方法 ====================

    def _find_windows_by_criteria(self, window_type: str) -> List[WindowInfo]:
        """根据条件查找窗口"""
        try:
            if window_type not in self.target_windows:
                return []

            criteria = self.target_windows[window_type]
            found_windows = []

            def enum_windows_callback(hwnd, _):
                try:
                    window_info = self._get_window_info(hwnd)
                    if self._match_window_criteria(window_info, criteria):
                        found_windows.append(window_info)
                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)
            return found_windows

        except Exception as e:
            self.logger.error(f"❌ 查找窗口异常: {e}")
            return []

    def _get_window_info(self, hwnd: int) -> WindowInfo:
        """获取窗口信息"""
        try:
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            rect = win32gui.GetWindowRect(hwnd)
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_enabled = win32gui.IsWindowEnabled(hwnd)

            return WindowInfo(
                hwnd=hwnd,
                title=title or "",
                class_name=class_name or "",
                rect=rect,
                is_visible=bool(is_visible),
                is_enabled=bool(is_enabled)
            )
        except Exception as e:
            self.logger.error(f"❌ 获取窗口信息异常: {e}")
            return WindowInfo(
                hwnd=hwnd,
                title="",
                class_name="",
                rect=(0, 0, 0, 0),
                is_visible=False,
                is_enabled=False
            )

    def _match_window_criteria(self, window_info: WindowInfo, criteria: Dict) -> bool:
        """检查窗口是否符合条件"""
        try:
            # 检查窗口是否可见和启用
            if not window_info.is_visible:
                return False

            # 检查类名
            class_names = criteria.get("class_names", [])
            if class_names and window_info.class_name not in class_names:
                return False

            # 检查标题模式
            title_patterns = criteria.get("title_patterns", [])
            if title_patterns:
                title_match = any(pattern in window_info.title for pattern in title_patterns)
                if not title_match:
                    return False

            # 检查窗口大小
            size_range = criteria.get("size_range", {})
            width = window_info.rect[2] - window_info.rect[0]
            height = window_info.rect[3] - window_info.rect[1]

            if size_range:
                min_width = size_range.get("min_width", 0)
                max_width = size_range.get("max_width", 9999)
                min_height = size_range.get("min_height", 0)
                max_height = size_range.get("max_height", 9999)

                if not (min_width <= width <= max_width and min_height <= height <= max_height):
                    return False

            # 检查排除的窗口大小
            exclude_sizes = criteria.get("exclude_sizes", [])
            if exclude_sizes:
                for exclude_width, exclude_height in exclude_sizes:
                    if width == exclude_width and height == exclude_height:
                        self.logger.debug(f"排除窗口 {window_info.title} (大小: {width}x{height})")
                        return False

            return True

        except Exception as e:
            self.logger.error(f"❌ 匹配窗口条件异常: {e}")
            return False

    def _extract_window_text(self, hwnd: int) -> str:
        """提取窗口文本内容 - 简化但更可靠的版本"""
        try:
            all_texts = []

            # 方法1: 获取窗口标题
            title = win32gui.GetWindowText(hwnd)
            if title.strip():
                all_texts.append(title.strip())

            # 方法2: 递归获取所有子控件文本（深度遍历）
            child_texts = []

            def enum_child_callback(child_hwnd, depth=0):
                try:
                    # 获取子窗口文本
                    child_text = win32gui.GetWindowText(child_hwnd)
                    child_class = win32gui.GetClassName(child_hwnd)

                    if child_text.strip():
                        child_texts.append(child_text.strip())
                        # 特别记录包含关键词的文本
                        if any(keyword in child_text for keyword in ["操作", "频繁", "稍后", "再试"]):
                            self.logger.info(f"🎯 发现关键文本: [{child_class}] {child_text}")

                    # 递归遍历子窗口的子窗口
                    if depth < 3:  # 限制递归深度
                        def nested_enum_callback(nested_hwnd, _):
                            return enum_child_callback(nested_hwnd, depth + 1)
                        win32gui.EnumChildWindows(child_hwnd, nested_enum_callback, None)

                except Exception as e:
                    self.logger.debug(f"获取子窗口文本异常: {e}")
                return True

            win32gui.EnumChildWindows(hwnd, enum_child_callback, 0)

            # 合并所有文本
            if child_texts:
                all_texts.extend(child_texts)

            combined_text = " ".join(all_texts)

            # 记录提取到的文本用于调试
            if combined_text.strip():
                self.logger.info(f"🔍 提取到窗口文本: {combined_text[:200]}...")
                # 如果包含关键词，记录完整文本
                if any(keyword in combined_text for keyword in ["操作过于频繁", "请稍后再试", "操作频繁"]):
                    self.logger.info(f"� 完整关键文本: {combined_text}")
            else:
                self.logger.warning(f"⚠️ 未能提取到窗口文本，窗口句柄: {hwnd}")

            return combined_text

        except Exception as e:
            self.logger.error(f"❌ 提取窗口文本异常: {e}")
            return ""



    def _analyze_text_for_errors(self, text_content: str, window_info: WindowInfo) -> ErrorDetectionResult:
        """分析文本内容是否包含错误信息"""
        try:
            if not text_content:
                return self._create_no_error_result()

            text_lower = text_content.lower()

            # 检查各种错误模式
            for error_type, patterns in self.error_patterns.items():
                for pattern in patterns:
                    if pattern in text_content or pattern.lower() in text_lower:
                        confidence = self._calculate_confidence(pattern, text_content, window_info)

                        self.logger.info(f"🎯 检测到错误: {error_type}")
                        self.logger.info(f"📝 匹配模式: {pattern}")
                        self.logger.info(f"📊 置信度: {confidence:.2f}")
                        self.logger.info(f"📋 窗口文本: {text_content[:100]}...")

                        return ErrorDetectionResult(
                            has_error=True,
                            error_type=error_type,
                            error_message=f"检测到'{pattern}'错误提示",
                            window_info=window_info,
                            detection_time=time.time(),
                            confidence=confidence
                        )

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 分析文本错误异常: {e}")
            return self._create_no_error_result()

    def _calculate_confidence(self, pattern: str, text_content: str, window_info: WindowInfo) -> float:
        """计算检测置信度"""
        try:
            confidence = 0.5  # 基础置信度

            # 根据匹配模式调整置信度
            if "操作过于频繁" in pattern:
                confidence += 0.3
            elif "请稍后再试" in pattern:
                confidence += 0.2

            # 根据窗口类型调整置信度
            if "微信" in window_info.title:
                confidence += 0.1

            # 根据窗口大小调整置信度（330x194是目标大小）
            width = window_info.rect[2] - window_info.rect[0]
            height = window_info.rect[3] - window_info.rect[1]
            if width == 330 and height == 194:
                confidence += 0.1

            return min(1.0, confidence)

        except Exception as e:
            self.logger.error(f"❌ 计算置信度异常: {e}")
            return 0.5

    def _create_no_error_result(self) -> ErrorDetectionResult:
        """创建无错误结果"""
        return ErrorDetectionResult(
            has_error=False,
            error_type="none",
            error_message="未检测到错误",
            window_info=None,
            detection_time=time.time(),
            confidence=1.0
        )

    def _find_ok_button_in_window(self, hwnd: int) -> Optional[Tuple[int, int]]:
        """在窗口中查找确定按钮位置 - 支持屏幕分辨率适配"""
        try:
            # 获取窗口信息
            window_info = self._get_window_info(hwnd)
            rect = win32gui.GetWindowRect(hwnd)
            window_width = rect[2] - rect[0]
            window_height = rect[3] - rect[1]

            # 获取屏幕分辨率信息
            screen_width = win32api.GetSystemMetrics(0)  # SM_CXSCREEN
            screen_height = win32api.GetSystemMetrics(1)  # SM_CYSCREEN
            self.logger.info(f"🖥️ 当前屏幕分辨率: {screen_width}x{screen_height}")

            # 检查是否是错误提示窗口 (330x194)
            if window_width == 330 and window_height == 194 and "Weixin" in window_info.title:
                # 🔧 使用新的精确坐标，支持不同分辨率适配
                button_x, button_y = self._get_adaptive_ok_button_coordinates(
                    screen_width, screen_height, rect
                )

                self.logger.info(f"📍 使用适配坐标 (错误提示窗口): ({button_x}, {button_y})")
                self.logger.info(f"🎯 窗口信息: {window_info.title} ({window_width}x{window_height})")
                self.logger.info(f"📍 窗口位置: ({rect[0]}, {rect[1]}) 到 ({rect[2]}, {rect[3]})")
                return (button_x, button_y)

            # 其他窗口使用计算方法
            button_x = rect[0] + window_width // 2
            button_y = rect[1] + int(window_height * 0.75)

            self.logger.info(f"📍 计算确定按钮位置: ({button_x}, {button_y})")
            return (button_x, button_y)

        except Exception as e:
            self.logger.error(f"❌ 查找确定按钮异常: {e}")
            import traceback
            self.logger.error(f"详细异常信息: {traceback.format_exc()}")
            return None

    def _get_adaptive_ok_button_coordinates(self, screen_width: int, screen_height: int,
                                          window_rect: Tuple[int, int, int, int]) -> Tuple[int, int]:
        """获取适配不同屏幕分辨率的确定按钮坐标"""
        try:
            # 默认坐标（基于用户提供的新坐标）
            default_x = 1364
            default_y = 252

            # 常见分辨率的坐标映射
            resolution_coordinates = {
                (1920, 1080): (1364, 252),  # 1080p - 用户提供的新坐标
                (1366, 768): (1024, 200),   # 768p - 按比例缩放
                (1440, 900): (1080, 225),   # 900p - 按比例缩放
                (1600, 900): (1200, 225),   # 900p宽屏 - 按比例缩放
                (2560, 1440): (1819, 336),  # 1440p - 按比例缩放
                (3840, 2160): (2728, 504),  # 4K - 按比例缩放
            }

            # 检查是否有精确匹配的分辨率
            resolution_key = (screen_width, screen_height)
            if resolution_key in resolution_coordinates:
                coords = resolution_coordinates[resolution_key]
                self.logger.info(f"✅ 使用预设坐标 ({screen_width}x{screen_height}): {coords}")
                return coords

            # 如果没有精确匹配，使用比例缩放
            # 基准分辨率：1920x1080
            base_width = 1920
            base_height = 1080
            base_x = 1364
            base_y = 252

            # 计算缩放比例
            scale_x = screen_width / base_width
            scale_y = screen_height / base_height

            # 应用缩放
            scaled_x = int(base_x * scale_x)
            scaled_y = int(base_y * scale_y)

            self.logger.info(f"🔧 使用比例缩放坐标:")
            self.logger.info(f"   基准分辨率: {base_width}x{base_height}")
            self.logger.info(f"   当前分辨率: {screen_width}x{screen_height}")
            self.logger.info(f"   缩放比例: X={scale_x:.3f}, Y={scale_y:.3f}")
            self.logger.info(f"   原始坐标: ({base_x}, {base_y})")
            self.logger.info(f"   缩放坐标: ({scaled_x}, {scaled_y})")

            # 边界检查：确保坐标在屏幕范围内
            scaled_x = max(0, min(scaled_x, screen_width - 1))
            scaled_y = max(0, min(scaled_y, screen_height - 1))

            return (scaled_x, scaled_y)

        except Exception as e:
            self.logger.error(f"❌ 计算适配坐标异常: {e}")
            # 返回默认坐标作为备选
            self.logger.info(f"🔄 使用默认坐标: ({default_x}, {default_y})")
            return (default_x, default_y)

    def _activate_window(self, hwnd: int) -> bool:
        """激活窗口"""
        try:
            # 多种激活方法
            win32gui.SetForegroundWindow(hwnd)
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, 0, 0,
                                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)

            time.sleep(0.5)  # 等待激活完成

            # 验证激活是否成功
            current_hwnd = win32gui.GetForegroundWindow()
            return current_hwnd == hwnd

        except Exception as e:
            self.logger.error(f"❌ 激活窗口异常: {e}")
            return False

    def _close_window(self, hwnd: int) -> bool:
        """增强的窗口关闭方法 - 支持多种关闭方式"""
        try:
            window_info = self._get_window_info(hwnd)
            self.logger.info(f"🔄 尝试关闭窗口: {window_info.title} ({window_info.class_name})")

            # 方法1: 发送WM_CLOSE消息
            self.logger.info("   方法1: 发送WM_CLOSE消息...")
            win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
            time.sleep(0.8)

            # 检查是否已关闭
            if self._is_window_closed(hwnd):
                self.logger.info("   ✅ WM_CLOSE成功关闭窗口")
                return True

            # 方法2: 激活窗口后按ESC键
            self.logger.info("   方法2: 激活窗口后按ESC键...")
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)
                # 按ESC键
                win32api.keybd_event(27, 0, 0, 0)  # ESC键按下
                win32api.keybd_event(27, 0, win32con.KEYEVENTF_KEYUP, 0)  # ESC键释放
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ ESC键成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   ESC键方法失败: {e}")

            # 方法3: 强制点击窗口右上角的X按钮
            self.logger.info("   方法3: 强制点击窗口右上角X按钮...")
            try:
                rect = win32gui.GetWindowRect(hwnd)
                # 计算X按钮位置（通常在右上角）
                x_button_x = rect[2] - 15  # 距离右边15像素
                x_button_y = rect[1] + 15  # 距离顶部15像素

                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)

                # 强制点击X按钮 - 使用多种点击方法
                self.logger.info(f"   强制点击X按钮位置: ({x_button_x}, {x_button_y})")

                # 方法3a: pyautogui点击
                pyautogui.click(x_button_x, x_button_y)
                time.sleep(0.3)

                # 方法3b: 双击确保点击
                pyautogui.doubleClick(x_button_x, x_button_y)
                time.sleep(0.5)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ 强制点击X按钮成功关闭窗口")
                    return True

                # 方法3c: 使用win32api点击
                self.logger.info("   尝试win32api点击...")
                win32api.SetCursorPos((x_button_x, x_button_y))
                time.sleep(0.2)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, x_button_x, x_button_y, 0, 0)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, x_button_x, x_button_y, 0, 0)
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ win32api点击成功关闭窗口")
                    return True

            except Exception as e:
                self.logger.debug(f"   强制点击X按钮方法失败: {e}")

            # 方法4: 发送WM_SYSCOMMAND关闭消息
            self.logger.info("   方法4: 发送系统关闭命令...")
            try:
                win32gui.PostMessage(hwnd, win32con.WM_SYSCOMMAND, win32con.SC_CLOSE, 0)
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ 系统关闭命令成功")
                    return True
            except Exception as e:
                self.logger.debug(f"   系统关闭命令失败: {e}")

            # 所有方法都失败
            self.logger.warning(f"   ⚠️ 所有关闭方法都失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 关闭窗口异常: {e}")
            return False

    def _is_window_closed(self, hwnd: int) -> bool:
        """检查窗口是否已关闭"""
        try:
            return not win32gui.IsWindow(hwnd) or not win32gui.IsWindowVisible(hwnd)
        except:
            return True  # 窗口句柄无效，说明已关闭

    def _is_wechat_window(self, window_info: WindowInfo) -> bool:
        """判断是否是微信窗口"""
        try:
            # 检查类名
            wechat_class_names = ["Qt51514QWindowIcon", "WeChatMainWndForPC"]
            if window_info.class_name not in wechat_class_names:
                return False

            # 检查标题
            wechat_titles = ["微信", "Weixin", "WeChat"]
            title_match = any(title in window_info.title for title in wechat_titles)

            return title_match

        except Exception as e:
            self.logger.error(f"❌ 判断微信窗口异常: {e}")
            return False

    def _find_wechat2_windows(self) -> List[WindowInfo]:
        """查找微信2窗口"""
        try:
            wechat2_windows = []

            def enum_windows_callback(hwnd, _):
                try:
                    window_info = self._get_window_info(hwnd)

                    # 检查是否是微信窗口
                    if self._is_wechat_window(window_info):
                        # 检查是否包含"2"或其他标识
                        if "2" in window_info.title or "微信2" in window_info.title:
                            wechat2_windows.append(window_info)
                        # 如果没有明确的"2"标识，也添加到候选列表
                        elif len(wechat2_windows) == 0:
                            wechat2_windows.append(window_info)
                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)

            self.logger.info(f"🔍 找到 {len(wechat2_windows)} 个微信2候选窗口")
            for i, window in enumerate(wechat2_windows):
                self.logger.info(f"   {i+1}. {window.title} ({window.class_name})")

            return wechat2_windows

        except Exception as e:
            self.logger.error(f"❌ 查找微信2窗口异常: {e}")
            return []

    # ==================== 公共接口方法 ====================

    def get_detection_history(self) -> List[ErrorDetectionResult]:
        """获取检测历史"""
        with self._lock:
            return self.detection_history.copy()

    def clear_detection_history(self):
        """清空检测历史"""
        with self._lock:
            self.detection_history.clear()
        self.logger.info("🧹 已清空检测历史")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            history = self.detection_history.copy()

        total_detections = len(history)
        error_detections = sum(1 for result in history if result.has_error)

        error_types = {}
        for result in history:
            if result.has_error:
                error_types[result.error_type] = error_types.get(result.error_type, 0) + 1

        avg_confidence = 0.0
        if history:
            avg_confidence = sum(result.confidence for result in history) / len(history)

        return {
            "total_detections": total_detections,
            "error_detections": error_detections,
            "success_rate": (total_detections - error_detections) / max(1, total_detections),
            "error_types": error_types,
            "average_confidence": avg_confidence,
            "last_detection_time": self.last_detection_time
        }

    def detect_frequency_error_for_window(self, window_hwnd: int, window_title: str) -> ErrorDetectionResult:
        """为指定窗口检测频率错误

        Args:
            window_hwnd: 窗口句柄
            window_title: 窗口标题

        Returns:
            ErrorDetectionResult: 检测结果
        """
        try:
            self.logger.info(f"🔍 开始为窗口 {window_title} (句柄: {window_hwnd}) 检测频率错误...")

            # 更新窗口错误状态跟踪
            with self._lock:
                if window_hwnd not in self.window_error_status:
                    self.window_error_status[window_hwnd] = {
                        'title': window_title,
                        'error_count': 0,
                        'last_error_time': 0,
                        'is_failed': False
                    }

            # 检查窗口是否仍然存在
            try:
                import win32gui
                if not win32gui.IsWindow(window_hwnd):
                    self.logger.warning(f"⚠️ 窗口 {window_title} (句柄: {window_hwnd}) 已不存在")
                    return self._create_no_error_result()
            except Exception as e:
                self.logger.error(f"❌ 检查窗口存在性异常: {e}")
                return self._create_no_error_result()

            # 创建窗口信息对象
            try:
                rect = win32gui.GetWindowRect(window_hwnd)
                class_name = win32gui.GetClassName(window_hwnd) or ""
                is_visible = bool(win32gui.IsWindowVisible(window_hwnd))
                is_enabled = bool(win32gui.IsWindowEnabled(window_hwnd))

                window_info = WindowInfo(
                    hwnd=window_hwnd,
                    title=window_title,
                    class_name=class_name,
                    rect=rect,
                    is_visible=is_visible,
                    is_enabled=is_enabled
                )
            except Exception as e:
                self.logger.error(f"❌ 获取窗口信息异常: {e}")
                return self._create_no_error_result()

            # 执行错误检测
            detection_result = self._check_specific_window_for_errors(window_info)

            # 更新窗口错误状态
            if detection_result.has_error:
                with self._lock:
                    status = self.window_error_status[window_hwnd]
                    status['error_count'] += 1
                    status['last_error_time'] = time.time()

                    # 如果错误次数过多，标记为失败
                    if status['error_count'] >= 3:
                        status['is_failed'] = True
                        self.logger.warning(f"⚠️ 窗口 {window_title} 错误次数过多，标记为失败")

                        # 检查是否所有窗口都失败了
                        self._check_all_windows_failed()

            return detection_result

        except Exception as e:
            self.logger.error(f"❌ 窗口频率错误检测异常: {e}")
            return self._create_no_error_result()

    def _check_specific_window_for_errors(self, window_info: WindowInfo) -> ErrorDetectionResult:
        """检查特定窗口的错误信息"""
        try:
            # 获取窗口文本内容
            text_content = self._extract_window_text(window_info.hwnd)

            # 分析文本内容
            if text_content:
                error_result = self._analyze_text_for_errors(text_content, window_info)
                if error_result.has_error:
                    return error_result

            # 基于窗口特征的检测
            confidence = self._calculate_error_probability_by_window_features(window_info)
            if confidence > 0.7:
                return ErrorDetectionResult(
                    has_error=True,
                    error_type="too_frequent",
                    error_message=f"基于窗口特征检测到错误 (置信度: {confidence:.2f})",
                    window_info=window_info,
                    detection_time=time.time(),
                    confidence=confidence
                )

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 检查特定窗口错误异常: {e}")
            return self._create_no_error_result()

    def is_terminate_required(self) -> bool:
        """检查是否需要终止程序

        Returns:
            bool: 如果所有窗口都失败则返回True
        """
        with self._lock:
            return self.terminate_required

    def get_window_error_status(self) -> Dict[int, Dict[str, Any]]:
        """获取窗口错误状态

        Returns:
            Dict: 窗口错误状态字典
        """
        with self._lock:
            return self.window_error_status.copy()

    def is_restart_required(self) -> bool:
        """检查是否需要重新开始流程

        Returns:
            bool: 如果需要重新开始则返回True
        """
        with self._lock:
            return self._restart_required

    def _set_restart_flag(self):
        """设置重新开始标志

        这个方法通常在频率错误处理完成后调用，
        用于通知其他模块需要重新开始流程
        """
        with self._lock:
            self._restart_required = True
        self.logger.info("🔄 已设置重新开始标志")

    def clear_restart_flag(self):
        """清除重新开始标志

        这个方法通常在重新开始流程时调用，
        用于重置标志状态
        """
        with self._lock:
            self._restart_required = False
        self.logger.info("🧹 已清除重新开始标志")

    def get_restart_status(self) -> Dict[str, Any]:
        """获取重新开始状态信息

        Returns:
            Dict: 包含重新开始状态的详细信息
        """
        with self._lock:
            return {
                "restart_required": self._restart_required,
                "last_detection_time": self.last_detection_time,
                "detection_history_count": len(self.detection_history),
                "terminate_required": self.terminate_required
            }

    def _check_all_windows_failed(self):
        """检查是否所有窗口都失败了"""
        try:
            if not self.window_error_status:
                return

            # 检查是否所有窗口都标记为失败
            failed_windows = [hwnd for hwnd, status in self.window_error_status.items()
                            if status.get('is_failed', False)]

            total_windows = len(self.window_error_status)
            failed_count = len(failed_windows)

            self.logger.info(f"📊 窗口失败统计: {failed_count}/{total_windows}")

            # 如果失败窗口数量达到总数，设置终止标志
            if failed_count >= total_windows and total_windows > 0:
                self.terminate_required = True
                self.logger.error("🚨 所有微信窗口都已失败，设置程序终止标志")

        except Exception as e:
            self.logger.error(f"❌ 检查窗口失败状态异常: {e}")

    def set_detection_timeout(self, timeout: float):
        """设置检测超时时间"""
        self.detection_timeout = max(1.0, min(30.0, timeout))
        self.logger.info(f"⏱️ 设置检测超时时间: {self.detection_timeout}秒")

    def set_retry_attempts(self, attempts: int):
        """设置重试次数"""
        self.retry_attempts = max(1, min(10, attempts))
        self.logger.info(f"🔄 设置重试次数: {self.retry_attempts}")

    def test_ok_button_coordinates(self) -> Dict[str, Any]:
        """测试确定按钮坐标设置 - 用于验证坐标更新效果"""
        try:
            self.logger.info("🧪 开始测试确定按钮坐标设置...")

            # 获取屏幕信息
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)

            # 模拟错误提示窗口矩形 (330x194)
            mock_window_rect = (100, 100, 430, 294)  # x1, y1, x2, y2

            # 测试坐标计算
            test_coords = self._get_adaptive_ok_button_coordinates(
                screen_width, screen_height, mock_window_rect
            )

            # 收集测试结果
            test_result = {
                "screen_resolution": f"{screen_width}x{screen_height}",
                "calculated_coordinates": test_coords,
                "original_coordinates": (1364, 252),
                "coordinate_difference": (
                    test_coords[0] - 1364,
                    test_coords[1] - 252
                ),
                "is_within_screen": (
                    0 <= test_coords[0] < screen_width and
                    0 <= test_coords[1] < screen_height
                ),
                "test_status": "success"
            }

            self.logger.info("📊 坐标测试结果:")
            self.logger.info(f"   屏幕分辨率: {test_result['screen_resolution']}")
            self.logger.info(f"   计算坐标: {test_result['calculated_coordinates']}")
            self.logger.info(f"   原始坐标: {test_result['original_coordinates']}")
            self.logger.info(f"   坐标差异: {test_result['coordinate_difference']}")
            self.logger.info(f"   坐标有效性: {'✅ 有效' if test_result['is_within_screen'] else '❌ 超出屏幕范围'}")

            return test_result

        except Exception as e:
            self.logger.error(f"❌ 测试坐标设置异常: {e}")
            return {
                "test_status": "failed",
                "error": str(e)
            }


# ==================== 使用示例 ====================

def example_usage():
    """使用示例 - 包含坐标测试功能"""
    print("🚀 微信频率错误处理器使用示例")
    print("=" * 50)

    # 创建错误处理器
    handler = FrequencyErrorHandler()

    # 🆕 测试确定按钮坐标设置
    print("\n🧪 测试确定按钮坐标设置...")
    test_result = handler.test_ok_button_coordinates()
    print(f"测试结果: {test_result}")

    # 在点击确定按钮后检测错误
    print("\n🔍 开始错误检测...")
    detection_result = handler.detect_frequency_error_after_click(timeout=5.0)

    # 如果检测到错误，进行处理
    if detection_result.has_error:
        print(f"✅ 检测到错误: {detection_result.error_type}")
        print(f"📝 错误信息: {detection_result.error_message}")
        print(f"📊 置信度: {detection_result.confidence:.2f}")

        # 处理错误
        print("\n🔧 开始处理错误...")
        success = handler.handle_frequency_error(detection_result)
        if success:
            print("✅ 错误处理成功")
        else:
            print("❌ 错误处理失败")
    else:
        print("ℹ️ 未检测到错误")

    # 获取统计信息
    print("\n📊 获取统计信息...")
    stats = handler.get_statistics()
    print(f"统计信息: {stats}")

    print("\n" + "=" * 50)
    print("🎉 示例执行完成")


if __name__ == "__main__":
    example_usage()
