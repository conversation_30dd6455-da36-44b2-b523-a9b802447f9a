2025-07-30 11:07:21,762 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:07:21,763 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:07:21,764 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:07:21,764 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:07:21,765 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 11:07:21,766 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:07:21,766 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:07:21,767 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:07:21,768 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:07:21,768 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:07:21,768 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:07:21,770 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:07:21,773 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_110721.log
2025-07-30 11:07:21,775 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:07:21,776 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:07:21,776 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:07:21,776 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 11:07:21,777 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 11:07:21,778 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 19:07:21
2025-07-30 11:07:21,778 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 11:07:21,779 - __main__ - INFO - 📅 启动时间: 2025-07-30 19:07:21
2025-07-30 11:07:21,779 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 11:07:21,782 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:07:22,319 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:07:22,320 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:07:22,324 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 11:07:22,324 - __main__ - INFO - ✅ 找到 1 个微信窗口
2025-07-30 11:07:22,325 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 11:07:22,325 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 11:07:23,535 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 11:07:23,536 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 11:07:23,536 - __main__ - INFO - 📋 待处理联系人数: 2905
2025-07-30 11:07:23,537 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 11:07:23,537 - __main__ - INFO - 📊 总窗口数: 1, 总联系人数: 2905
2025-07-30 11:07:23,538 - __main__ - INFO - 
============================================================
2025-07-30 11:07:23,538 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 1 轮)
2025-07-30 11:07:23,541 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:07:23,541 - __main__ - INFO - 📊 全局进度：已处理 0/2905 个联系人
2025-07-30 11:07:23,542 - __main__ - INFO - ============================================================
2025-07-30 11:07:23,543 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:07:23,546 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:07:23,550 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:07:23,550 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:07:23,551 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:07:23,884 - modules.window_manager - INFO - ✅ 激活方法 1 部分成功（窗口可见）
2025-07-30 11:07:23,884 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:07:23,885 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:07:23,885 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:07:23,885 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:07:23,885 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:07:23,886 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:07:23,886 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:07:23,886 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:07:23,886 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:07:24,089 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 11:07:24,090 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:07:24,091 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:07:24,091 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:07:24,500 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 11:07:24,500 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:07:24,501 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:07:24,501 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:07:24,501 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:07:24,502 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:07:24,502 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:07:24,502 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:07:24,503 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:07:24,503 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:07:24,705 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 11:07:24,706 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:07:24,709 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:07:25,009 - modules.window_manager - WARNING - ⚠️ 窗口可能未完全置于最前面 (当前前台: 525668)
2025-07-30 11:07:25,010 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:07:25,010 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:07:25,011 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:07:25,011 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:07:25,012 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:07:25,012 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:07:25,013 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:07:26,020 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:07:26,052 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:07:26,064 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:07:26,093 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:07:26,118 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:07:26,129 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:07:26,135 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:07:26,142 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:07:26,359 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:07:26,360 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:07:28,742 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:07:28,742 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:07:28,743 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 11:07:31,552 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:07:31,753 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:07:31,754 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:07:34,124 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:07:34,124 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:07:34,125 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 11:07:35,924 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:07:36,124 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:07:36,125 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:07:38,507 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:07:38,507 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:07:38,508 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 11:07:40,946 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:07:41,147 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:07:41,148 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:07:43,524 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:07:43,525 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:07:43,525 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 11:07:45,986 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:07:46,187 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:07:46,187 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:07:48,574 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:07:48,575 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:07:48,575 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:07:48,576 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:07:48,576 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:07:48,578 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:07:48,578 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:07:48,579 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:07:48,579 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:07:48,581 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 10224836, 进程: Weixin.exe)
2025-07-30 11:07:48,583 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:07:48,584 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 10224836)
2025-07-30 11:07:48,584 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 10224836) - 增强版
2025-07-30 11:07:48,888 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:07:48,889 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:07:48,889 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:07:48,890 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:07:48,890 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:07:48,890 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:07:49,095 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:07:49,095 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:07:49,297 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:07:49,297 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:07:49,297 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:07:49,298 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:07:49,298 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:07:49,298 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:07:49,298 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:07:50,299 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:07:50,299 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:07:50,301 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:07:50,301 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:07:50,303 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:07:50,304 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:07:50,306 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 10224836, 进程: Weixin.exe)
2025-07-30 11:07:50,309 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:07:50,309 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:07:50,310 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:07:50,310 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:07:50,310 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:07:50,311 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:07:50,619 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:07:50,619 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:07:50,620 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:07:50,620 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:07:50,620 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:07:50,620 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:07:50,621 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:07:50,621 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:07:50,621 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:07:50,622 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:07:50,823 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:07:50,824 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:07:50,825 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:07:51,126 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:07:51,126 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:07:51,126 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:07:52,127 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:07:52,127 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 11:07:52,128 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:07:52,131 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_110752.log
2025-07-30 11:07:52,132 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:07:52,132 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:07:52,133 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:07:52,133 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 11:07:52,133 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:07:52,134 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:07:52,137 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 11:07:52,137 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 11:07:52,137 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:07:52,138 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:07:52,138 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 11:07:52,139 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 11:07:52,139 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:07:52,140 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:07:52,141 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 10224836
2025-07-30 11:07:52,141 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 10224836) - 增强版
2025-07-30 11:07:52,449 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:07:52,450 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:07:52,450 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:07:52,450 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:07:52,451 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:07:52,451 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:07:52,451 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:07:52,452 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:07:52,654 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:07:52,654 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:07:52,657 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 10224836 (API返回: None)
2025-07-30 11:07:52,957 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:07:52,958 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:07:52,958 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:07:52,958 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:07:52,959 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:07:52,960 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:07:52,960 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:07:52,964 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:07:52,966 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:07:53,464 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:07:53,464 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:07:53,717 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2917 个
2025-07-30 11:07:53,718 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2917 个 (总计: 3135 个)
2025-07-30 11:07:53,719 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:07:53,719 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:07:53,720 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:07:53,720 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:07:53,720 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2917
2025-07-30 11:07:53,721 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18254993253 (王莹)
2025-07-30 11:07:53,721 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:00,299 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18254993253
2025-07-30 11:08:00,299 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:08:00,300 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18254993253 执行添加朋友操作...
2025-07-30 11:08:00,300 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:08:00,300 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:08:00,301 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:08:00,302 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:08:00,306 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:08:00,307 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:08:00,308 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:08:00,309 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:08:00,309 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:08:00,309 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:08:00,310 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:08:00,310 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:08:00,317 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:08:00,318 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:08:00,324 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:08:00,326 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:08:00,330 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 11:08:00,331 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:08:00,833 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:08:00,834 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:08:00,919 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差44.04, 边缘比例0.0360
2025-07-30 11:08:00,927 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_110800.png
2025-07-30 11:08:00,929 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:08:00,931 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:08:00,938 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:08:00,940 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:08:00,941 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:08:00,947 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_110800.png
2025-07-30 11:08:00,948 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:08:00,949 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:08:00,950 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:08:00,951 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:08:00,954 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:08:00,956 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:08:00,958 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:08:00,959 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:08:00,967 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_110800.png
2025-07-30 11:08:00,970 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:08:00,974 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:08:00,979 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_110800.png
2025-07-30 11:08:01,046 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:08:01,048 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:08:01,051 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:08:01,054 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:08:01,405 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:08:02,172 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:08:02,173 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:08:02,174 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:02,174 - modules.wechat_auto_add_simple - INFO - ✅ 18254993253 添加朋友操作执行成功
2025-07-30 11:08:02,174 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:02,175 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:08:04,177 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:08:04,177 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:08:04,177 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:08:04,178 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:08:04,178 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:08:04,178 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:08:04,179 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:08:04,179 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:08:04,179 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:08:04,179 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18254993253
2025-07-30 11:08:04,183 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:08:04,183 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:08:04,184 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:08:04,184 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:08:04,185 - modules.friend_request_window - INFO -    📱 phone: '18254993253'
2025-07-30 11:08:04,186 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:08:04,186 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:08:04,685 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:08:04,685 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:08:04,686 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:08:04,686 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:08:04,688 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18254993253
2025-07-30 11:08:04,688 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:08:04,689 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:08:04,691 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:08:04,691 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:08:04,692 - modules.friend_request_window - INFO -    📱 手机号码: 18254993253
2025-07-30 11:08:04,695 - modules.friend_request_window - INFO -    🆔 准考证: 014325110138
2025-07-30 11:08:04,697 - modules.friend_request_window - INFO -    👤 姓名: 王莹
2025-07-30 11:08:04,699 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:08:04,701 - modules.friend_request_window - INFO -    📝 备注格式: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:04,703 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:08:04,704 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:04,709 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:08:04,711 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3475954, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:08:04,713 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3475954)
2025-07-30 11:08:04,714 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:08:04,714 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:08:04,715 - modules.friend_request_window - INFO - 🔄 激活窗口: 3475954
2025-07-30 11:08:05,417 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:08:05,418 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:08:05,420 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:08:05,420 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:08:05,421 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:08:05,421 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:08:05,421 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:08:05,422 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:08:05,422 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:08:05,422 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:08:05,422 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:08:05,423 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:08:05,423 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:08:05,423 - modules.friend_request_window - INFO -    📝 remark参数: '014325110138-王莹-2025-07-30 19:08:04' (类型: <class 'str'>, 长度: 35)
2025-07-30 11:08:05,423 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:08:05,424 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:05,424 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:08:05,424 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:08:05,425 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:08:05,425 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:08:05,426 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:08:05,426 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:08:05,427 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:08:06,338 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:08:11,581 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:08:11,581 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:08:11,581 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:08:11,582 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:08:11,583 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '...' (前50字符)
2025-07-30 11:08:11,895 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:08:11,895 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:08:12,798 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:08:12,799 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:08:12,799 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:08:12,799 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:08:12,799 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:08:13,300 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:08:13,301 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:08:13,301 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:08:13,301 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:08:13,302 - modules.friend_request_window - INFO -    📝 内容: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:13,302 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 11:08:13,303 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110138-\xe7\x8e\x8b\xe8\x8e\xb9-2025-07-30 19:08:04'
2025-07-30 11:08:13,303 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:08:14,222 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:08:19,463 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:08:19,463 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:08:19,464 - modules.friend_request_window - INFO -    📝 原始文本: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:19,464 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 11:08:19,464 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '学习指导【视频作业】...' (前50字符)
2025-07-30 11:08:19,777 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:08:19,777 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:08:20,681 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:08:20,691 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:08:20,692 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:20,692 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:08:20,693 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:20,693 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 11:08:21,194 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:21,194 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:08:21,195 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:08:21,195 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:08:21,195 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:08:21,196 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:08:21,196 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:08:21,997 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:08:21,997 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:08:21,998 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:08:22,606 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:22,606 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:08:22,607 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:08:22,607 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:08:23,125 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:23,366 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:23,603 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:23,838 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:24,077 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:24,313 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:24,546 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:24,781 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:25,012 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:25,249 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:25,486 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:25,721 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:25,955 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:26,192 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:26,520 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:26,788 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:27,021 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:27,259 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:27,498 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:08:27,716 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:08:27,716 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:08:28,717 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:08:28,720 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:08:28,720 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:08:28,721 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:08:28,721 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:08:28,721 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:08:28,722 - modules.friend_request_window - INFO -    📝 备注信息: '014325110138-王莹-2025-07-30 19:08:04'
2025-07-30 11:08:29,223 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:08:29,224 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:29,224 - modules.wechat_auto_add_simple - INFO - ✅ 18254993253 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:08:29,225 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18254993253
2025-07-30 11:08:29,226 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:32,766 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2917
2025-07-30 11:08:32,767 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17352057656 (王发志)
2025-07-30 11:08:32,767 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:39,347 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17352057656
2025-07-30 11:08:39,347 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:08:39,348 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17352057656 执行添加朋友操作...
2025-07-30 11:08:39,348 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:08:39,348 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:08:39,349 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:08:39,350 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:08:39,355 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:08:39,356 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:08:39,357 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:08:39,358 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:08:39,358 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:08:39,358 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:08:39,359 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:08:39,359 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:08:39,366 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:08:39,368 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:08:39,370 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:08:39,372 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:08:39,374 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 11:08:39,377 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:08:39,883 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:08:39,884 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:08:39,950 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.74, 边缘比例0.0351
2025-07-30 11:08:39,957 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_110839.png
2025-07-30 11:08:39,959 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:08:39,960 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:08:39,963 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:08:39,965 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:08:39,967 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:08:39,976 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_110839.png
2025-07-30 11:08:39,978 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 11:08:39,981 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:08:39,985 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:08:39,989 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:08:39,992 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 11:08:39,993 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 11:08:39,995 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 11:08:39,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 11:08:39,997 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:08:39,998 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 11:08:40,001 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 11:08:40,005 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 11:08:40,007 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:08:40,009 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 11:08:40,009 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 11:08:40,011 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:08:40,012 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:08:40,013 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 11:08:40,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:08:40,014 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:08:40,017 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 11:08:40,019 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 11:08:40,024 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 11:08:40,026 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 11:08:40,027 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 11:08:40,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 11:08:40,029 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 11:08:40,031 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 11:08:40,032 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 11:08:40,033 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 11:08:40,039 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 11:08:40,046 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 11:08:40,048 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 11:08:40,056 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 11:08:40,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 11:08:40,061 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 11:08:40,062 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 11:08:40,063 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 11:08:40,065 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:08:40,071 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 11:08:40,072 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 11:08:40,074 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:08:40,075 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:08:40,082 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_110840.png
2025-07-30 11:08:40,084 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:08:40,087 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 11:08:40,091 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_110840.png
2025-07-30 11:08:40,112 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:08:40,113 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 11:08:40,115 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:08:40,122 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:08:40,425 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 11:08:41,192 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:08:41,193 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:08:41,195 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:41,195 - modules.wechat_auto_add_simple - INFO - ✅ 17352057656 添加朋友操作执行成功
2025-07-30 11:08:41,195 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:41,196 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:08:43,197 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:08:43,198 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:08:43,198 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:08:43,198 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:08:43,199 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:08:43,199 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:08:43,199 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:08:43,200 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:08:43,200 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:08:43,200 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17352057656
2025-07-30 11:08:43,201 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:08:43,201 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:08:43,202 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:08:43,202 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:08:43,202 - modules.friend_request_window - INFO -    📱 phone: '17352057656'
2025-07-30 11:08:43,203 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:08:43,203 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:08:43,754 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:08:43,755 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:08:43,756 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:08:43,756 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:08:43,757 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17352057656
2025-07-30 11:08:43,757 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:08:43,758 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:08:43,758 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:08:43,759 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:08:43,759 - modules.friend_request_window - INFO -    📱 手机号码: 17352057656
2025-07-30 11:08:43,759 - modules.friend_request_window - INFO -    🆔 准考证: 014325110139
2025-07-30 11:08:43,759 - modules.friend_request_window - INFO -    👤 姓名: 王发志
2025-07-30 11:08:43,760 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:08:43,760 - modules.friend_request_window - INFO -    📝 备注格式: '014325110139-王发志-2025-07-30 19:08:43'
2025-07-30 11:08:43,761 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:08:43,761 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110139-王发志-2025-07-30 19:08:43'
2025-07-30 11:08:43,762 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:08:43,764 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:08:43,764 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 11:08:43,765 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:43,765 - modules.wechat_auto_add_simple - INFO - ✅ 17352057656 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 11:08:43,765 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17352057656
2025-07-30 11:08:43,767 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:08:45,089 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 11:08:45,090 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 11:08:45,090 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 11:08:45,091 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 11:08:45,092 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 11:08:45,092 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 11:08:45,092 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 11:08:45,093 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 11:08:45,093 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 11:08:45,093 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 11:08:45,093 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 11:08:45,094 - __main__ - INFO - � 更新全局进度：已处理 2/2905 个联系人
2025-07-30 11:08:45,095 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 11:08:48,095 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 11:08:48,096 - __main__ - INFO - 📊 当前进度：已处理 2/2905 个联系人
2025-07-30 11:08:48,096 - __main__ - INFO - 
============================================================
2025-07-30 11:08:48,096 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 2 轮)
2025-07-30 11:08:48,096 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:08:48,097 - __main__ - INFO - 📊 全局进度：已处理 2/2905 个联系人
2025-07-30 11:08:48,097 - __main__ - INFO - ============================================================
2025-07-30 11:08:48,097 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:08:48,098 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:08:48,098 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:08:48,098 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:08:48,099 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:08:48,409 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:08:48,409 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:08:48,409 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:08:48,410 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:08:48,410 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:08:48,411 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:08:48,411 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:08:48,411 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:08:48,412 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:08:48,412 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:08:48,614 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:08:48,614 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:08:48,615 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:08:48,615 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:08:48,919 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:08:48,919 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:08:48,920 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:08:48,920 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:08:48,920 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:08:48,921 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:08:48,921 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:08:48,921 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:08:48,921 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:08:48,922 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:08:49,124 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:08:49,124 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:08:49,126 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:08:49,426 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:08:49,427 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:08:49,427 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:08:49,428 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:08:49,428 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:08:49,428 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:08:49,428 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:08:49,429 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:08:50,429 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:08:50,430 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:08:50,430 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:08:50,430 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:08:50,431 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:08:50,431 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:08:50,431 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:08:50,432 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:08:50,632 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:08:50,633 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:08:53,020 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:08:53,020 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:08:53,020 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 11:08:55,712 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:08:55,913 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:08:55,914 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
