2025-07-29 15:17:58,782 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:17:58,783 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:17:58,784 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-29 15:17:58,784 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:17:58,785 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-29 15:17:58,785 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:17:58,786 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:17:58,787 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:17:58,788 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:17:58,790 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:17:58,791 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:17:58,794 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:17:58,799 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_151758.log
2025-07-29 15:17:58,800 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:17:58,800 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 15:17:58,801 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 15:17:58,801 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-29 15:17:58,801 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-29 15:17:58,802 - __main__ - INFO - 📅 当前北京时间: 2025-07-29 23:17:58
2025-07-29 15:17:58,802 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-29 15:17:58,802 - __main__ - INFO - 📅 启动时间: 2025-07-29 23:17:58
2025-07-29 15:17:58,803 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-29 15:17:58,804 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:17:59,344 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:17:59,345 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:17:59,880 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:17:59,880 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:17:59,883 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-29 15:17:59,883 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-29 15:17:59,884 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-29 15:17:59,884 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-29 15:17:59,884 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-29 15:18:00,823 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-29 15:18:00,824 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-29 15:18:00,824 - __main__ - INFO - 📋 待处理联系人数: 2939
2025-07-29 15:18:00,825 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-29 15:18:00,825 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2939
2025-07-29 15:18:00,825 - __main__ - INFO - 
============================================================
2025-07-29 15:18:00,825 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-29 15:18:00,826 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 15:18:00,826 - __main__ - INFO - 📊 全局进度：已处理 0/2939 个联系人
2025-07-29 15:18:00,826 - __main__ - INFO - ============================================================
2025-07-29 15:18:00,827 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-29 15:18:00,827 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-29 15:18:00,827 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-29 15:18:00,827 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-29 15:18:00,828 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:18:01,146 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:18:01,146 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:18:01,147 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:18:01,148 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:18:01,148 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:18:01,148 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:18:01,149 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:18:01,149 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:18:01,150 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:18:01,150 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:18:01,352 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:18:01,352 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:18:01,352 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 15:18:01,353 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:18:01,657 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:18:01,657 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:18:01,658 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:18:01,658 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:18:01,658 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:18:01,659 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:18:01,659 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:18:01,659 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:18:01,659 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:18:01,660 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:18:01,863 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:18:01,863 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:18:01,865 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 15:18:02,165 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:18:02,166 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:18:02,166 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:18:02,166 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:18:02,167 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:18:02,167 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:18:02,167 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-29 15:18:02,168 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-29 15:18:03,168 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-29 15:18:03,169 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-29 15:18:03,169 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-29 15:18:03,170 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-29 15:18:03,170 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-29 15:18:03,170 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-29 15:18:03,171 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-29 15:18:03,172 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-29 15:18:03,373 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-29 15:18:03,374 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-29 15:18:05,758 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-29 15:18:05,759 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-29 15:18:05,759 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-29 15:18:08,127 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-29 15:18:08,328 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-29 15:18:08,329 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-29 15:18:10,698 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-29 15:18:10,698 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-29 15:18:10,699 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-29 15:18:13,080 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-29 15:18:13,281 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-29 15:18:13,282 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-29 15:18:15,656 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-29 15:18:15,657 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-29 15:18:15,657 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-29 15:18:18,348 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-29 15:18:18,549 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-29 15:18:18,550 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-29 15:18:20,923 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-29 15:18:20,923 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-29 15:18:20,924 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-29 15:18:22,979 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-29 15:18:23,181 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-29 15:18:23,181 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-29 15:18:25,557 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-29 15:18:25,557 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-29 15:18:25,557 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 15:18:25,558 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-29 15:18:25,558 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:18:25,560 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:18:25,561 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:18:25,562 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:18:25,563 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:18:25,565 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 9438760, 进程: Weixin.exe)
2025-07-29 15:18:25,567 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 15:18:25,568 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 9438760)
2025-07-29 15:18:25,569 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9438760) - 增强版
2025-07-29 15:18:25,873 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:18:25,873 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:18:25,874 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 15:18:25,874 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 15:18:25,874 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-29 15:18:25,875 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 15:18:26,077 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-29 15:18:26,078 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 15:18:26,281 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:18:26,281 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:18:26,282 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-29 15:18:26,282 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-29 15:18:26,282 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-29 15:18:26,282 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-29 15:18:26,283 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-29 15:18:27,283 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-29 15:18:27,283 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-29 15:18:27,285 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:18:27,285 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-29 15:18:27,286 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-29 15:18:27,287 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-29 15:18:27,288 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 9438760, 进程: Weixin.exe)
2025-07-29 15:18:27,290 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-29 15:18:27,291 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-29 15:18:27,292 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-29 15:18:27,292 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-29 15:18:27,292 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-29 15:18:27,293 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-29 15:18:27,599 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:18:27,600 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:18:27,600 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-29 15:18:27,601 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-29 15:18:27,601 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-29 15:18:27,601 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-29 15:18:27,602 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-29 15:18:27,602 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-29 15:18:27,603 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-29 15:18:27,603 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-29 15:18:27,804 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:18:27,805 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:18:27,806 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-29 15:18:28,107 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:18:28,107 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-29 15:18:28,108 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-29 15:18:29,108 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-29 15:18:29,108 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-29 15:18:29,109 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-29 15:18:29,112 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250729_151829.log
2025-07-29 15:18:29,113 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:18:29,113 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-29 15:18:29,114 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-29 15:18:29,114 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-29 15:18:29,114 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-29 15:18:29,115 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-29 15:18:29,116 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-29 15:18:29,117 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-29 15:18:29,117 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-29 15:18:29,117 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-29 15:18:29,118 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-29 15:18:29,118 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-29 15:18:29,118 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-29 15:18:29,119 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:18:29,120 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 9438760
2025-07-29 15:18:29,121 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9438760) - 增强版
2025-07-29 15:18:29,428 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-29 15:18:29,429 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-29 15:18:29,429 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-29 15:18:29,430 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-29 15:18:29,430 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-29 15:18:29,430 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-29 15:18:29,431 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-29 15:18:29,431 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-29 15:18:29,633 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-29 15:18:29,633 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-29 15:18:29,639 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 9438760 (API返回: None)
2025-07-29 15:18:29,940 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-29 15:18:29,941 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-29 15:18:29,941 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-29 15:18:29,941 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-29 15:18:29,942 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-29 15:18:29,943 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-29 15:18:29,943 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-29 15:18:29,947 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-29 15:18:29,947 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-29 15:18:30,452 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-29 15:18:30,453 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:18:30,710 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2939 个
2025-07-29 15:18:30,712 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2939 个 (总计: 3135 个)
2025-07-29 15:18:30,712 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-29 15:18:30,713 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-29 15:18:30,714 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:18:30,714 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-29 15:18:30,715 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2939
2025-07-29 15:18:30,715 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18672909010 (张鹏)
2025-07-29 15:18:30,716 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:18:37,318 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18672909010
2025-07-29 15:18:37,319 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 15:18:37,319 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18672909010 执行添加朋友操作...
2025-07-29 15:18:37,319 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 15:18:37,319 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 15:18:37,320 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:18:37,321 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 15:18:37,327 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 15:18:37,328 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 15:18:37,328 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 15:18:37,329 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 15:18:37,329 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 15:18:37,330 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 15:18:37,330 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 15:18:37,330 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 15:18:37,334 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 15:18:37,341 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:18:37,345 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:18:37,346 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 15:18:37,348 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 15:18:37,348 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 15:18:37,851 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 15:18:37,852 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 15:18:37,933 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.80, 边缘比例0.0366
2025-07-29 15:18:37,942 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_151837.png
2025-07-29 15:18:37,943 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 15:18:37,945 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 15:18:37,947 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 15:18:37,949 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 15:18:37,955 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 15:18:37,961 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_151837.png
2025-07-29 15:18:37,965 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 15:18:37,966 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 15:18:37,968 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 15:18:37,973 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 15:18:37,978 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 15:18:37,978 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 15:18:37,979 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 15:18:37,980 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 15:18:37,994 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_151837.png
2025-07-29 15:18:37,996 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 15:18:37,997 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 15:18:38,003 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_151837.png
2025-07-29 15:18:38,069 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 15:18:38,070 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 15:18:38,071 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 15:18:38,072 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 15:18:38,373 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 15:18:39,140 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 15:18:39,141 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 15:18:39,142 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:18:39,142 - modules.wechat_auto_add_simple - INFO - ✅ 18672909010 添加朋友操作执行成功
2025-07-29 15:18:39,143 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:18:39,143 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 15:18:41,145 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 15:18:41,146 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 15:18:41,146 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 15:18:41,146 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:18:41,146 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:18:41,147 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:18:41,147 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:18:41,147 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:18:41,148 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 15:18:41,148 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18672909010
2025-07-29 15:18:41,152 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 15:18:41,152 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:18:41,153 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:18:41,153 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 15:18:41,153 - modules.friend_request_window - INFO -    📱 phone: '18672909010'
2025-07-29 15:18:41,154 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 15:18:41,155 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 15:18:41,660 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 15:18:41,660 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 15:18:41,660 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 15:18:41,661 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:18:41,662 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18672909010
2025-07-29 15:18:41,663 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 15:18:41,664 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:18:41,665 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 15:18:41,665 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 15:18:41,665 - modules.friend_request_window - INFO -    📱 手机号码: 18672909010
2025-07-29 15:18:41,666 - modules.friend_request_window - INFO -    🆔 准考证: 014325110110
2025-07-29 15:18:41,667 - modules.friend_request_window - INFO -    👤 姓名: 张鹏
2025-07-29 15:18:41,667 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:18:41,668 - modules.friend_request_window - INFO -    📝 备注格式: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:41,668 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:18:41,669 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:41,669 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 15:18:41,672 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5113930, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 15:18:41,674 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5113930)
2025-07-29 15:18:41,674 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 15:18:41,674 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 15:18:41,675 - modules.friend_request_window - INFO - 🔄 激活窗口: 5113930
2025-07-29 15:18:42,380 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 15:18:42,380 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 15:18:42,381 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 15:18:42,381 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 15:18:42,381 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 15:18:42,381 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:18:42,382 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 15:18:42,382 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:18:42,382 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 15:18:42,382 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 15:18:42,383 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 15:18:42,383 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 15:18:42,383 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 15:18:42,383 - modules.friend_request_window - INFO -    📝 remark参数: '014325110110-张鹏-2025-07-29 23:18:41' (类型: <class 'str'>, 长度: 35)
2025-07-29 15:18:42,384 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 15:18:42,384 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:42,384 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 15:18:42,385 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 15:18:42,387 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 15:18:42,387 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 15:18:42,388 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 15:18:42,388 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 15:18:42,388 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 15:18:43,307 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 15:18:48,581 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 15:18:48,581 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 15:18:48,582 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 15:18:48,582 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 15:18:48,584 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python -c "from modul...' (前50字符)
2025-07-29 15:18:48,897 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:18:48,897 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:18:49,800 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:18:49,811 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:18:49,813 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 15:18:49,813 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 15:18:49,813 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 15:18:49,814 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 15:18:50,315 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 15:18:50,315 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 15:18:50,316 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 15:18:50,316 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 15:18:50,317 - modules.friend_request_window - INFO -    📝 内容: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:50,318 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 15:18:50,318 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110110-\xe5\xbc\xa0\xe9\xb9\x8f-2025-07-29 23:18:41'
2025-07-29 15:18:50,319 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 15:18:51,238 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 15:18:56,492 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 15:18:56,493 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 15:18:56,493 - modules.friend_request_window - INFO -    📝 原始文本: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:56,493 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 15:18:56,494 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python -c "from modul...' (前50字符)
2025-07-29 15:18:56,806 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:18:56,807 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:18:57,710 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:18:57,720 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:18:57,721 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:57,721 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 15:18:57,722 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:57,722 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 15:18:58,223 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:18:58,224 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 15:18:58,224 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 15:18:58,225 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 15:18:58,225 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 15:18:58,225 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 15:18:58,225 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 15:18:59,026 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 15:18:59,026 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 15:18:59,027 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 15:18:59,639 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:18:59,639 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 15:18:59,640 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 15:18:59,640 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 15:19:00,159 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:00,500 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:00,735 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:00,967 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:01,207 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:01,439 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:01,671 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:01,903 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:02,133 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:02,372 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:02,604 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:02,840 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:03,072 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:03,308 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:03,544 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:03,776 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:04,010 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:04,242 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:04,477 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-29 15:19:04,695 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-29 15:19:04,695 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-29 15:19:05,696 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 15:19:05,699 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-29 15:19:05,699 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-29 15:19:05,699 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-29 15:19:05,700 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-29 15:19:05,701 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:19:05,701 - modules.friend_request_window - INFO -    📝 备注信息: '014325110110-张鹏-2025-07-29 23:18:41'
2025-07-29 15:19:06,202 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-29 15:19:06,202 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:19:06,203 - modules.wechat_auto_add_simple - INFO - ✅ 18672909010 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-29 15:19:06,203 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18672909010
2025-07-29 15:19:06,204 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:19:09,820 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2939
2025-07-29 15:19:09,821 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15019004476 (王硕)
2025-07-29 15:19:09,821 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:19:16,393 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15019004476
2025-07-29 15:19:16,393 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-29 15:19:16,393 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15019004476 执行添加朋友操作...
2025-07-29 15:19:16,394 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-29 15:19:16,394 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-29 15:19:16,395 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-29 15:19:16,396 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-29 15:19:16,401 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-29 15:19:16,402 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-29 15:19:16,403 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-29 15:19:16,403 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-29 15:19:16,404 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-29 15:19:16,404 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-29 15:19:16,405 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-29 15:19:16,405 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-29 15:19:16,415 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-29 15:19:16,417 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:19:16,419 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-29 15:19:16,422 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1637x978
2025-07-29 15:19:16,424 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-29 15:19:16,429 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-29 15:19:16,941 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-29 15:19:16,943 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-29 15:19:17,003 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差40.31, 边缘比例0.0379
2025-07-29 15:19:17,010 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250729_151917.png
2025-07-29 15:19:17,013 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-29 15:19:17,014 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-29 15:19:17,015 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-29 15:19:17,017 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-29 15:19:17,021 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-29 15:19:17,027 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250729_151917.png
2025-07-29 15:19:17,030 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-29 15:19:17,031 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-29 15:19:17,033 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-29 15:19:17,037 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-29 15:19:17,040 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-29 15:19:17,041 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-29 15:19:17,043 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-29 15:19:17,046 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-29 15:19:17,058 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250729_151917.png
2025-07-29 15:19:17,060 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-29 15:19:17,061 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-29 15:19:17,065 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250729_151917.png
2025-07-29 15:19:17,093 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-29 15:19:17,107 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-29 15:19:17,113 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-29 15:19:17,116 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-29 15:19:17,452 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-29 15:19:18,294 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-29 15:19:18,296 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-29 15:19:18,297 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:19:18,297 - modules.wechat_auto_add_simple - INFO - ✅ 15019004476 添加朋友操作执行成功
2025-07-29 15:19:18,298 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:19:18,298 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-29 15:19:20,300 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-29 15:19:20,300 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-29 15:19:20,301 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-29 15:19:20,301 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-29 15:19:20,301 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-29 15:19:20,302 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-29 15:19:20,302 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-29 15:19:20,302 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-29 15:19:20,302 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-29 15:19:20,303 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15019004476
2025-07-29 15:19:20,303 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-29 15:19:20,304 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:19:20,304 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:19:20,304 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-29 15:19:20,304 - modules.friend_request_window - INFO -    📱 phone: '15019004476'
2025-07-29 15:19:20,305 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-29 15:19:20,305 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-29 15:19:20,911 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-29 15:19:20,911 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-29 15:19:20,911 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-29 15:19:20,912 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-29 15:19:20,913 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15019004476
2025-07-29 15:19:20,913 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-29 15:19:20,914 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:19:20,915 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-29 15:19:20,915 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-29 15:19:20,915 - modules.friend_request_window - INFO -    📱 手机号码: 15019004476
2025-07-29 15:19:20,915 - modules.friend_request_window - INFO -    🆔 准考证: 014325110113
2025-07-29 15:19:20,917 - modules.friend_request_window - INFO -    👤 姓名: 王硕
2025-07-29 15:19:20,918 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-29 15:19:20,918 - modules.friend_request_window - INFO -    📝 备注格式: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:20,919 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-29 15:19:20,919 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:20,920 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-29 15:19:20,921 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2427938, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-29 15:19:20,922 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2427938)
2025-07-29 15:19:20,922 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-29 15:19:20,923 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-29 15:19:20,924 - modules.friend_request_window - INFO - 🔄 激活窗口: 2427938
2025-07-29 15:19:21,627 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-29 15:19:21,628 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-29 15:19:21,628 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-29 15:19:21,629 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-29 15:19:21,629 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-29 15:19:21,629 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-29 15:19:21,629 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-29 15:19:21,630 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-29 15:19:21,630 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-29 15:19:21,630 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-29 15:19:21,631 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-29 15:19:21,631 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-29 15:19:21,631 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-29 15:19:21,631 - modules.friend_request_window - INFO -    📝 remark参数: '014325110113-王硕-2025-07-29 23:19:20' (类型: <class 'str'>, 长度: 35)
2025-07-29 15:19:21,632 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-29 15:19:21,632 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:21,633 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-29 15:19:21,634 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-29 15:19:21,635 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-29 15:19:21,636 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-29 15:19:21,636 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-29 15:19:21,636 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-29 15:19:21,637 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-29 15:19:22,554 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-29 15:19:27,797 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-29 15:19:27,798 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-29 15:19:27,798 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-29 15:19:27,798 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-29 15:19:27,799 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python -c "from modul...' (前50字符)
2025-07-29 15:19:28,109 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:19:28,110 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:19:29,012 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:19:29,021 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:19:29,022 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-29 15:19:29,022 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-29 15:19:29,023 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-29 15:19:29,023 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-29 15:19:29,524 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-29 15:19:29,524 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-29 15:19:29,524 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-29 15:19:29,525 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-29 15:19:29,525 - modules.friend_request_window - INFO -    📝 内容: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:29,525 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-29 15:19:29,525 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110113-\xe7\x8e\x8b\xe7\xa1\x95-2025-07-29 23:19:20'
2025-07-29 15:19:29,526 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-29 15:19:30,435 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-29 15:19:35,686 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-29 15:19:35,687 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-29 15:19:35,687 - modules.friend_request_window - INFO -    📝 原始文本: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:35,687 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-29 15:19:35,688 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'PS D:\程序-测试\微信7.28 - 副本 (2)> python -c "from modul...' (前50字符)
2025-07-29 15:19:36,000 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-29 15:19:36,000 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-29 15:19:36,905 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-29 15:19:36,920 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-29 15:19:36,921 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:36,922 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-29 15:19:36,923 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:36,923 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-29 15:19:37,424 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110113-王硕-2025-07-29 23:19:20'
2025-07-29 15:19:37,424 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-29 15:19:37,424 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-29 15:19:37,425 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-29 15:19:37,425 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-29 15:19:37,425 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-29 15:19:37,426 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-29 15:19:38,226 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-29 15:19:38,227 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-29 15:19:38,227 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-29 15:19:38,835 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-29 15:19:38,835 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-29 15:19:38,836 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-29 15:19:38,836 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-29 15:19:39,338 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-29 15:19:39,340 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-29 15:19:39,341 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-29 15:19:39,341 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-29 15:19:39,341 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-29 15:19:39,341 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-29 15:19:39,342 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-29 15:19:39,342 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-29 15:19:39,342 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-29 15:19:39,343 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 15:19:39,343 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-29 15:19:39,343 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-29 15:19:39,344 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-29 15:19:39,344 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-29 15:19:39,345 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-29 15:19:39,346 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-29 15:19:39,346 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-29 15:19:39,347 - modules.friend_request_window - INFO - 📊 窗口 Weixin 错误次数: 1/3
2025-07-29 15:19:39,366 - modules.friend_request_window - INFO - 📊 窗口状态统计: 0/4 个窗口已失败
2025-07-29 15:19:39,368 - modules.friend_request_window - INFO - 🔄 步骤1: 点击错误提示窗口的'确定'按钮...
2025-07-29 15:19:39,369 - modules.friend_request_window - INFO - 🔍 开始智能识别和点击错误对话框的确定按钮...
2025-07-29 15:19:39,370 - modules.friend_request_window - INFO - 📋 从检测结果获取窗口信息: Weixin
2025-07-29 15:19:39,872 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-29 15:19:39,873 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-29 15:19:39,873 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-29 15:19:39,873 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-29 15:19:39,874 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-29 15:19:39,874 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-29 15:19:39,874 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-29 15:19:39,874 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-29 15:19:40,785 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-29 15:19:40,786 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-29 15:19:40,786 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-29 15:19:41,787 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭出现频繁错误的微信窗口...
2025-07-29 15:19:41,787 - modules.friend_request_window - INFO - 🎯 精确关闭出现频繁错误的微信窗口: Weixin
2025-07-29 15:19:41,788 - modules.friend_request_window - INFO - 📋 目标窗口句柄: 3999012
2025-07-29 15:19:41,788 - modules.friend_request_window - INFO - 📋 执行顺序: 1.关闭添加朋友窗口 → 2.关闭微信主窗口 → 3.清理残留窗口
2025-07-29 15:19:41,788 - modules.friend_request_window - INFO - 🔄 步骤1: 智能关闭添加朋友窗口...
2025-07-29 15:19:41,788 - modules.friend_request_window - INFO - 🔍 智能搜索添加朋友窗口...
2025-07-29 15:19:41,790 - modules.friend_request_window - INFO - 🔍 发现添加朋友窗口: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-29 15:19:41,792 - modules.friend_request_window - INFO - 📊 共发现 1 个添加朋友窗口
2025-07-29 15:19:41,792 - modules.friend_request_window - INFO - 🎯 关闭添加朋友窗口: 添加朋友
2025-07-29 15:19:42,293 - modules.friend_request_window - INFO - ✅ 成功关闭添加朋友窗口: 添加朋友
2025-07-29 15:19:42,294 - modules.friend_request_window - INFO - 🔄 备用方案：使用坐标点击关闭...
2025-07-29 15:19:42,294 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-29 15:19:42,794 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-29 15:19:43,903 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-29 15:19:43,903 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭目标微信主窗口...
2025-07-29 15:19:43,903 - modules.friend_request_window - WARNING - ⚠️ 目标微信窗口已不存在或无效: 3999012
2025-07-29 15:19:43,904 - modules.friend_request_window - INFO - 🔄 步骤3: 清理残留的错误对话框和相关窗口...
2025-07-29 15:19:43,904 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 15:19:43,905 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 15:19:43,906 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-29 15:19:43,906 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 15:19:43,908 - modules.friend_request_window - INFO - 📊 共发现 3 个可能的错误对话框
2025-07-29 15:19:43,908 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 15:19:44,109 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: 微信
2025-07-29 15:19:44,312 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 15:19:44,514 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
2025-07-29 15:19:44,539 - modules.friend_request_window - INFO - ✅ 精确关闭完成，仅关闭了出现频繁错误的微信窗口
2025-07-29 15:19:44,540 - modules.friend_request_window - INFO - 🔄 步骤3: 执行最终清理操作...
2025-07-29 15:19:44,541 - modules.friend_request_window - INFO - 🧹 清理残留的错误对话框...
2025-07-29 15:19:44,542 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1637x978
2025-07-29 15:19:44,548 - modules.friend_request_window - INFO - 📊 共发现 1 个可能的错误对话框
2025-07-29 15:19:44,551 - modules.friend_request_window - INFO - 🗑️ 清理错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-29 15:19:44,804 - modules.friend_request_window - INFO - 🔄 关闭错误相关窗口...
