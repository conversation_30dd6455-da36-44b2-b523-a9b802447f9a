2025-07-30 10:47:14,358 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,359 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,359 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:47:14,360 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:47:14,361 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 10:47:14,361 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:47:14,361 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:47:14,363 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:47:14,364 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:47:14,364 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:47:14,364 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:47:14,367 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,370 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_104714.log
2025-07-30 10:47:14,371 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,371 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:47:14,372 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:47:14,372 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 10:47:14,372 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 10:47:14,373 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 18:47:14
2025-07-30 10:47:14,373 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 10:47:14,373 - __main__ - INFO - 📅 启动时间: 2025-07-30 18:47:14
2025-07-30 10:47:14,374 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 10:47:14,374 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:47:14,923 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:14,924 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:47:15,449 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:15,450 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:47:15,452 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:47:15,452 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 10:47:15,453 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 10:47:15,453 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 10:47:15,453 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 10:47:16,341 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 10:47:16,341 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 10:47:16,342 - __main__ - INFO - 📋 待处理联系人数: 2921
2025-07-30 10:47:16,342 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 10:47:16,342 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2921
2025-07-30 10:47:16,343 - __main__ - INFO - 
============================================================
2025-07-30 10:47:16,343 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 10:47:16,343 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:47:16,343 - __main__ - INFO - 📊 全局进度：已处理 0/2921 个联系人
2025-07-30 10:47:16,344 - __main__ - INFO - ============================================================
2025-07-30 10:47:16,344 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 10:47:16,344 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:47:16,345 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:47:16,345 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 10:47:16,346 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:47:16,663 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:47:16,664 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:47:16,664 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:47:16,664 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:47:16,665 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:47:16,665 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:47:16,666 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:47:16,666 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:47:16,666 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:47:16,666 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:47:16,868 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:47:16,869 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:47:16,869 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:47:16,869 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:47:17,173 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:47:17,173 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:47:17,174 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:47:17,174 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:47:17,174 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:47:17,175 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:47:17,175 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:47:17,175 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:47:17,176 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:47:17,176 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:47:17,377 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:47:17,378 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:47:17,379 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:47:17,680 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:47:17,681 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:47:17,681 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:47:17,681 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:47:17,682 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:47:17,682 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:47:17,682 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:47:17,682 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:47:18,683 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:47:18,684 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 10:47:18,684 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:47:18,684 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:47:18,684 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:47:18,685 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:47:18,685 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:47:18,685 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:47:18,886 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:47:18,887 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:47:21,265 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:47:21,265 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:47:21,266 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 10:47:23,616 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:47:23,818 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:47:23,818 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:47:26,186 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:47:26,186 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:47:26,187 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 10:47:28,756 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:47:28,957 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:47:28,958 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:47:31,332 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:47:31,332 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:47:31,333 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 10:47:34,268 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:47:34,469 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:47:34,469 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:47:36,850 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:47:36,850 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:47:36,850 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 10:47:39,465 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:47:39,666 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:47:39,667 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:47:42,047 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:47:42,048 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:47:42,048 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:47:42,049 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:47:42,049 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:47:42,051 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:42,051 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:47:42,052 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:42,053 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:47:42,054 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7931978, 进程: Weixin.exe)
2025-07-30 10:47:42,057 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:47:42,057 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 7931978)
2025-07-30 10:47:42,058 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 7931978) - 增强版
2025-07-30 10:47:42,362 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:47:42,362 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:47:42,362 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:47:42,363 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:47:42,363 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:47:42,363 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:47:42,567 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:47:42,568 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:47:42,770 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:47:42,770 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:47:42,770 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:47:42,770 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:47:42,771 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:47:42,771 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:47:42,771 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:47:43,772 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:47:43,772 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:47:43,774 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:43,774 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:47:43,775 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:43,775 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:47:43,776 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7931978, 进程: Weixin.exe)
2025-07-30 10:47:43,781 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:47:43,781 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:47:43,782 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:47:43,782 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:47:43,783 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:47:43,783 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:47:44,091 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:47:44,092 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:47:44,092 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:47:44,093 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:47:44,093 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:47:44,093 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:47:44,093 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:47:44,094 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:47:44,094 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:47:44,094 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:47:44,297 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:47:44,297 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:47:44,298 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:47:44,599 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:47:44,600 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:47:44,600 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:47:45,601 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:47:45,601 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 10:47:45,602 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:47:45,604 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_104745.log
2025-07-30 10:47:45,605 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:45,606 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:47:45,606 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:47:45,606 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 10:47:45,606 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:47:45,607 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:47:45,609 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 10:47:45,609 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 10:47:45,610 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:47:45,610 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:47:45,611 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 10:47:45,611 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 10:47:45,612 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:47:45,613 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:45,614 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 7931978
2025-07-30 10:47:45,615 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 7931978) - 增强版
2025-07-30 10:47:45,925 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:47:45,926 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:47:45,926 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:47:45,927 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:47:45,927 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 10:47:45,927 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:47:45,928 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 10:47:45,928 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:47:46,130 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:47:46,130 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:47:46,134 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 7931978 (API返回: None)
2025-07-30 10:47:46,435 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:47:46,435 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:47:46,436 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:47:46,436 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:47:46,437 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:46,437 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:47:46,437 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:47:46,441 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:47:46,441 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:47:46,927 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:47:46,927 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:47:47,183 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2921 个
2025-07-30 10:47:47,184 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2921 个 (总计: 3135 个)
2025-07-30 10:47:47,184 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:47:47,185 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:47:47,185 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:47:47,185 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:47:47,186 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 19104446711 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:48,557 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 13783597105 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:49,973 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 17659905589 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:51,424 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 18254993253 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:52,791 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 17352057656 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:54,245 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 15763096192 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:55,607 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 13356873403 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:56,962 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 18644881373 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:58,346 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 18263399923 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:47:59,802 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 13688506431 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:48:01,177 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 18853061046 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:48:02,543 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 18944948991 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:48:03,983 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 13580982528 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:48:05,490 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 15814063678 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:48:07,387 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 15336500606 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:48:09,150 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 13250591024 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
2025-07-30 10:48:10,772 - modules.wechat_auto_add_simple - ERROR - ❌ 处理联系人异常: 15968490212 - 'FrequencyErrorHandler' object has no attribute 'is_restart_required'
