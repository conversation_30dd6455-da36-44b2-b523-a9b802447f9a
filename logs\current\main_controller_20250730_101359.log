2025-07-30 10:13:59,559 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:13:59,560 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:13:59,560 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:13:59,561 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:13:59,562 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 10:13:59,562 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:13:59,562 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:13:59,563 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:13:59,564 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:13:59,564 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:13:59,565 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:13:59,567 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:13:59,571 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_101359.log
2025-07-30 10:13:59,572 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:13:59,572 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:13:59,573 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:13:59,573 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 10:13:59,574 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 10:13:59,575 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 18:13:59
2025-07-30 10:13:59,575 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 10:13:59,576 - __main__ - INFO - 📅 启动时间: 2025-07-30 18:13:59
2025-07-30 10:13:59,578 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 10:13:59,578 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:14:00,150 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:14:00,150 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:14:00,710 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:14:00,710 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:14:00,713 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:14:00,714 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 10:14:00,714 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 10:14:00,714 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 10:14:00,715 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 10:14:01,657 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 10:14:01,657 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 10:14:01,658 - __main__ - INFO - 📋 待处理联系人数: 2927
2025-07-30 10:14:01,658 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 10:14:01,658 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2927
2025-07-30 10:14:01,659 - __main__ - INFO - 
============================================================
2025-07-30 10:14:01,659 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 10:14:01,659 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:14:01,660 - __main__ - INFO - 📊 全局进度：已处理 0/2927 个联系人
2025-07-30 10:14:01,660 - __main__ - INFO - ============================================================
2025-07-30 10:14:01,660 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 10:14:01,660 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:14:01,661 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:14:01,661 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 10:14:01,661 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:14:01,981 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:14:01,982 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:14:01,982 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:14:01,983 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:14:01,983 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:14:01,983 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:14:01,984 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:14:01,984 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:14:01,984 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:14:01,984 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:14:02,186 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:14:02,187 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:14:02,187 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:14:02,187 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:14:02,492 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:14:02,493 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:14:02,493 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:14:02,494 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:14:02,494 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:14:02,495 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:14:02,495 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:14:02,496 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:14:02,496 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:14:02,497 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:14:02,699 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:14:02,700 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:14:02,701 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:14:03,001 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:14:03,002 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:14:03,002 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:14:03,003 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:14:03,003 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:14:03,004 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:14:03,004 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:14:03,004 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:14:04,006 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:14:04,006 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 10:14:04,006 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:14:04,007 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:14:04,007 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:14:04,007 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:14:04,007 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:14:04,008 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:14:04,209 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:14:04,210 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:14:06,588 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:14:06,588 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:14:06,589 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-30 10:14:09,541 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:14:09,742 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:14:09,743 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:14:12,121 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:14:12,121 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:14:12,121 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 10:14:13,672 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:14:13,873 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:14:13,874 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:14:16,254 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:14:16,255 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:14:16,255 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 10:14:18,299 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:14:18,500 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:14:18,501 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:14:20,871 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:14:20,872 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:14:20,872 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 10:14:23,287 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:14:23,488 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:14:23,488 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:14:25,871 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:14:25,872 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:14:25,872 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:14:25,873 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:14:25,874 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:14:25,879 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:14:25,881 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:14:25,892 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:14:25,898 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:14:25,904 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5376260, 进程: Weixin.exe)
2025-07-30 10:14:25,916 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:14:25,922 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 5376260)
2025-07-30 10:14:25,923 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5376260) - 增强版
2025-07-30 10:14:26,227 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:14:26,227 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:14:26,227 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:14:26,228 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:14:26,228 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:14:26,228 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:14:26,433 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:14:26,434 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:14:26,636 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:14:26,636 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:14:26,637 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:14:26,637 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:14:26,638 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:14:26,638 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:14:26,638 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:14:27,639 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:14:27,639 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:14:27,641 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:14:27,642 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:14:27,643 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:14:27,643 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:14:27,645 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5376260, 进程: Weixin.exe)
2025-07-30 10:14:27,651 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:14:27,653 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:14:27,654 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:14:27,655 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:14:27,658 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:14:27,659 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:14:27,968 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:14:27,969 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:14:27,969 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:14:27,969 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:14:27,970 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:14:27,970 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:14:27,970 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:14:27,971 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:14:27,971 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:14:27,971 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:14:28,173 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:14:28,174 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:14:28,176 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:14:28,476 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:14:28,477 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:14:28,477 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:14:29,478 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:14:29,478 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 10:14:29,479 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:14:29,482 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_101429.log
2025-07-30 10:14:29,484 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:14:29,487 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:14:29,488 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:14:29,488 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 10:14:29,489 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:14:29,489 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:14:29,492 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 10:14:29,494 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 10:14:29,495 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:14:29,498 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:14:29,503 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 10:14:29,504 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 10:14:29,505 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:14:29,507 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:14:29,511 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 5376260
2025-07-30 10:14:29,512 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5376260) - 增强版
2025-07-30 10:14:29,824 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:14:29,824 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:14:29,825 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:14:29,825 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:14:29,825 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 10:14:29,826 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:14:29,826 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 10:14:29,826 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:14:30,028 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:14:30,029 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:14:30,032 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 5376260 (API返回: None)
2025-07-30 10:14:30,333 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:14:30,334 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:14:30,335 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:14:30,335 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:14:30,337 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:14:30,338 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:14:30,338 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:14:30,345 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:14:30,346 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:14:31,027 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:14:31,027 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:14:31,394 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2927 个
2025-07-30 10:14:31,395 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2927 个 (总计: 3135 个)
2025-07-30 10:14:31,396 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:14:31,396 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:14:31,397 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:14:31,397 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:14:31,397 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2927
2025-07-30 10:14:31,398 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18162602497 (李贤文)
2025-07-30 10:14:31,398 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:14:37,981 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18162602497
2025-07-30 10:14:37,982 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:14:37,983 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18162602497 执行添加朋友操作...
2025-07-30 10:14:37,983 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:14:37,984 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:14:37,985 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:14:37,986 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:14:37,994 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 10:14:37,999 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:14:38,015 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:14:38,021 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:14:38,027 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:14:38,027 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:14:38,028 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:14:38,030 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:14:38,037 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:14:38,042 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:14:38,059 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:14:38,061 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 10:14:38,064 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 10:14:38,065 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:14:38,570 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:14:38,571 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:14:38,649 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差41.52, 边缘比例0.0429
2025-07-30 10:14:38,658 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_101438.png
2025-07-30 10:14:38,662 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:14:38,664 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:14:38,666 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:14:38,675 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:14:38,681 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:14:38,692 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_101438.png
2025-07-30 10:14:38,701 - WeChatAutoAdd - INFO - 底部区域原始检测到 21 个轮廓
2025-07-30 10:14:38,708 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 10:14:38,710 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 10:14:38,711 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:14:38,712 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 10:14:38,714 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 10:14:38,722 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 10:14:38,723 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:14:38,725 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 10:14:38,728 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:14:38,729 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:14:38,730 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:14:38,732 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:14:38,740 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:14:38,745 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:14:38,749 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:14:38,756 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 10:14:38,757 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.5 (阈值:60)
2025-07-30 10:14:38,759 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 10:14:38,764 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 10:14:38,772 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 10:14:38,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,232), 尺寸4x9, 长宽比0.44, 面积36
2025-07-30 10:14:38,777 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(122,231), 尺寸10x12, 长宽比0.83, 面积120
2025-07-30 10:14:38,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,230), 尺寸32x13, 长宽比2.46, 面积416
2025-07-30 10:14:38,782 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=86.8 (阈值:60)
2025-07-30 10:14:38,789 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,230), 尺寸7x6, 长宽比1.17, 面积42
2025-07-30 10:14:38,790 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,230), 尺寸15x13, 长宽比1.15, 面积195
2025-07-30 10:14:38,792 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=82.7 (阈值:60)
2025-07-30 10:14:38,793 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:14:38,793 - WeChatAutoAdd - INFO - 底部区域找到 3 个按钮候选
2025-07-30 10:14:38,806 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 10:14:38,807 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 10:14:38,808 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 10:14:38,818 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_101438.png
2025-07-30 10:14:38,821 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 10:14:38,823 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:14:39,124 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 10:14:39,923 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:14:39,944 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:14:39,997 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:14:40,003 - modules.wechat_auto_add_simple - INFO - ✅ 18162602497 添加朋友操作执行成功
2025-07-30 10:14:40,010 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:14:40,014 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:14:42,025 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:14:42,026 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:14:42,026 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:14:42,026 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:14:42,027 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:14:42,027 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:14:42,028 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:14:42,029 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:14:42,030 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:14:42,030 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18162602497
2025-07-30 10:14:42,035 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:14:42,036 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:14:42,036 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:14:42,037 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:14:42,037 - modules.friend_request_window - INFO -    📱 phone: '18162602497'
2025-07-30 10:14:42,038 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:14:42,038 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:14:42,769 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:14:42,770 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:14:42,772 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:14:42,776 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:14:42,780 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18162602497
2025-07-30 10:14:42,792 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:14:42,801 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:14:42,812 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:14:42,813 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:14:42,815 - modules.friend_request_window - INFO -    📱 手机号码: 18162602497
2025-07-30 10:14:42,821 - modules.friend_request_window - INFO -    🆔 准考证: 014325110118
2025-07-30 10:14:42,824 - modules.friend_request_window - INFO -    👤 姓名: 李贤文
2025-07-30 10:14:42,826 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:14:42,827 - modules.friend_request_window - INFO -    📝 备注格式: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:42,830 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:14:42,831 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:42,835 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:14:42,840 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3935108, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:14:42,842 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3935108)
2025-07-30 10:14:42,843 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:14:42,844 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:14:42,845 - modules.friend_request_window - INFO - 🔄 激活窗口: 3935108
2025-07-30 10:14:43,547 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:14:43,548 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:14:43,548 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:14:43,548 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:14:43,549 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:14:43,550 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:14:43,551 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:14:43,551 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:14:43,552 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:14:43,552 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:14:43,553 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:14:43,553 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:14:43,553 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:14:43,554 - modules.friend_request_window - INFO -    📝 remark参数: '014325110118-李贤文-2025-07-30 18:14:42' (类型: <class 'str'>, 长度: 36)
2025-07-30 10:14:43,554 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:14:43,555 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:43,555 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:14:43,555 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:14:43,556 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:14:43,556 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:14:43,556 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:14:43,556 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:14:43,557 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:14:44,470 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:14:49,718 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:14:49,718 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:14:49,719 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:14:49,719 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:14:49,721 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:14:50,037 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:14:50,038 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:14:50,941 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:14:50,950 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:14:50,951 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:14:50,952 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:14:50,953 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:14:50,953 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:14:51,454 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:14:51,454 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:14:51,454 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:14:51,455 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:14:51,455 - modules.friend_request_window - INFO -    📝 内容: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:51,455 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 10:14:51,455 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110118-\xe6\x9d\x8e\xe8\xb4\xa4\xe6\x96\x87-2025-07-30 18:14:42'
2025-07-30 10:14:51,456 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:14:52,370 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:14:57,618 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:14:57,619 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:14:57,619 - modules.friend_request_window - INFO -    📝 原始文本: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:57,619 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 10:14:57,620 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:14:57,931 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:14:57,932 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:14:58,834 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:14:58,843 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:14:58,843 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:58,844 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:14:58,845 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:58,845 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 10:14:59,346 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:14:59,347 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:14:59,347 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:14:59,347 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:14:59,347 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:14:59,348 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:14:59,348 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:15:00,149 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:15:00,149 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:15:00,150 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:15:00,768 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:00,768 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:15:00,769 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:15:00,770 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:15:01,292 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:01,532 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:01,768 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:02,009 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:02,298 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:02,641 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:02,876 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:03,110 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:03,343 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:03,578 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:03,810 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:04,053 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:04,292 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:04,528 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:04,761 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:05,037 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:05,278 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:05,515 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:05,756 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:05,972 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 10:15:05,973 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 10:15:06,973 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:15:06,976 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 10:15:06,977 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 10:15:06,977 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:15:06,978 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:15:06,978 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:15:06,978 - modules.friend_request_window - INFO -    📝 备注信息: '014325110118-李贤文-2025-07-30 18:14:42'
2025-07-30 10:15:07,479 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:15:07,480 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:07,482 - modules.wechat_auto_add_simple - INFO - ✅ 18162602497 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:15:07,482 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18162602497
2025-07-30 10:15:07,483 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:11,331 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2927
2025-07-30 10:15:11,332 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18774956387 (夏梅)
2025-07-30 10:15:11,332 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:17,933 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18774956387
2025-07-30 10:15:17,933 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:15:17,933 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18774956387 执行添加朋友操作...
2025-07-30 10:15:17,934 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:15:17,934 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:15:17,935 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:15:17,936 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:15:17,940 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 10:15:17,945 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:15:17,946 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:15:17,947 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:15:17,948 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:15:17,949 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:15:17,949 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:15:17,950 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:15:17,965 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:15:17,978 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:15:17,988 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:15:17,990 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 10:15:17,993 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 10:15:17,995 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:15:18,497 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:15:18,499 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:15:18,571 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 10:15:18,572 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 10:15:18,581 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_101518.png
2025-07-30 10:15:18,585 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:15:18,587 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:15:18,592 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:15:18,593 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:15:18,594 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:15:18,599 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_101518.png
2025-07-30 10:15:18,605 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 10:15:18,606 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 10:15:18,608 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 10:15:18,610 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:15:18,613 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 10:15:18,622 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 10:15:18,625 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 10:15:18,630 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:15:18,644 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_101518.png
2025-07-30 10:15:18,648 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:15:18,655 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 10:15:18,662 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_101518.png
2025-07-30 10:15:18,732 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:15:18,734 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 10:15:18,735 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:15:18,738 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:15:19,040 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 10:15:19,817 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:15:19,818 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:15:19,820 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:19,823 - modules.wechat_auto_add_simple - INFO - ✅ 18774956387 添加朋友操作执行成功
2025-07-30 10:15:19,824 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:19,825 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:15:21,827 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:15:21,828 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:15:21,829 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:15:21,829 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:15:21,829 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:15:21,829 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:15:21,830 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:15:21,830 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:15:21,830 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:15:21,831 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18774956387
2025-07-30 10:15:21,831 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:15:21,832 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:15:21,832 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:15:21,832 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:15:21,832 - modules.friend_request_window - INFO -    📱 phone: '18774956387'
2025-07-30 10:15:21,833 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:15:21,833 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:15:22,418 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:15:22,419 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:15:22,419 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:15:22,420 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:15:22,421 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18774956387
2025-07-30 10:15:22,422 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:15:22,423 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:15:22,424 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:15:22,424 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:15:22,425 - modules.friend_request_window - INFO -    📱 手机号码: 18774956387
2025-07-30 10:15:22,425 - modules.friend_request_window - INFO -    🆔 准考证: 014325110119
2025-07-30 10:15:22,426 - modules.friend_request_window - INFO -    👤 姓名: 夏梅
2025-07-30 10:15:22,426 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:15:22,427 - modules.friend_request_window - INFO -    📝 备注格式: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:22,427 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:15:22,427 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:22,428 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:15:22,430 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5636756, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:15:22,432 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5636756)
2025-07-30 10:15:22,433 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:15:22,433 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:15:22,435 - modules.friend_request_window - INFO - 🔄 激活窗口: 5636756
2025-07-30 10:15:23,137 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:15:23,138 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:15:23,138 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:15:23,138 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:15:23,139 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:15:23,139 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:15:23,139 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:15:23,139 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:15:23,140 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:15:23,140 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:15:23,140 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:15:23,140 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:15:23,141 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:15:23,141 - modules.friend_request_window - INFO -    📝 remark参数: '014325110119-夏梅-2025-07-30 18:15:22' (类型: <class 'str'>, 长度: 35)
2025-07-30 10:15:23,141 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:15:23,141 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:23,142 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:15:23,142 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:15:23,142 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:15:23,142 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:15:23,143 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:15:23,143 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:15:23,143 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:15:24,052 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:15:29,297 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:15:29,298 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:15:29,298 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:15:29,299 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:15:29,299 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:15:29,609 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:15:29,609 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:15:30,512 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:15:30,524 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:15:30,524 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:15:30,525 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:15:30,526 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:15:30,526 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:15:31,027 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:15:31,027 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:15:31,028 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:15:31,028 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:15:31,028 - modules.friend_request_window - INFO -    📝 内容: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:31,029 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 10:15:31,029 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110119-\xe5\xa4\x8f\xe6\xa2\x85-2025-07-30 18:15:22'
2025-07-30 10:15:31,030 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:15:31,953 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:15:37,206 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:15:37,207 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:15:37,207 - modules.friend_request_window - INFO -    📝 原始文本: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:37,208 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 10:15:37,209 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:15:37,519 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:15:37,519 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:15:38,421 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:15:38,431 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:15:38,432 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:38,432 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:15:38,433 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:38,433 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 10:15:38,934 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:38,934 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:15:38,935 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:15:38,935 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:15:38,935 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:15:38,936 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:15:38,936 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:15:39,737 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:15:39,737 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:15:39,737 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:15:40,350 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:40,351 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:15:40,352 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:15:40,353 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:15:40,870 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:41,104 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:41,337 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:41,568 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:41,803 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:42,038 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:42,277 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:42,521 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:42,758 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:42,993 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:43,225 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:43,463 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:43,700 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:43,937 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:44,170 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:44,406 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:44,641 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:44,876 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:45,116 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:45,355 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:15:45,575 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 10:15:45,576 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 10:15:46,576 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:15:46,580 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 10:15:46,581 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 10:15:46,581 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:15:46,582 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:15:46,582 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:15:46,583 - modules.friend_request_window - INFO -    📝 备注信息: '014325110119-夏梅-2025-07-30 18:15:22'
2025-07-30 10:15:47,084 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:15:47,085 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:47,085 - modules.wechat_auto_add_simple - INFO - ✅ 18774956387 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:15:47,086 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18774956387
2025-07-30 10:15:47,087 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:15:48,575 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 10:15:48,575 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 10:15:48,576 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 10:15:48,577 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 10:15:48,577 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 10:15:48,578 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 10:15:48,578 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 10:15:48,578 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 10:15:48,579 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 10:15:48,580 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 10:15:48,584 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 10:15:48,586 - __main__ - INFO - � 更新全局进度：已处理 2/2927 个联系人
2025-07-30 10:15:48,590 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 10:15:51,591 - __main__ - INFO - 
============================================================
2025-07-30 10:15:51,592 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 10:15:51,592 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 10:15:51,592 - __main__ - INFO - 📊 全局进度：已处理 2/2927 个联系人
2025-07-30 10:15:51,593 - __main__ - INFO - ============================================================
2025-07-30 10:15:51,593 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 10:15:51,593 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 10:15:51,593 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:15:51,594 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 10:15:51,594 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:15:51,914 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:15:51,915 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:15:51,915 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:15:51,916 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:15:51,916 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:15:51,916 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:15:51,917 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:15:51,917 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:15:51,917 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:15:51,917 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:15:52,119 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:15:52,119 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:15:52,119 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 10:15:52,120 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:15:52,423 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:15:52,423 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:15:52,423 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:15:52,424 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:15:52,424 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:15:52,424 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:15:52,425 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:15:52,425 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:15:52,425 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:15:52,426 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:15:52,627 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:15:52,628 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:15:52,630 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 10:15:52,931 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:15:52,931 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:15:52,932 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:15:52,932 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:15:52,932 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:15:52,932 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:15:52,933 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:15:52,933 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:15:53,933 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:15:53,934 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 10:15:53,934 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:15:53,935 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:15:53,935 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:15:53,936 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:15:53,936 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:15:53,937 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:15:54,138 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:15:54,138 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:15:56,549 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:15:56,549 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:15:56,550 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 10:15:58,514 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:15:58,715 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:15:58,716 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:16:01,097 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:16:01,097 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:16:01,098 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 10:16:02,816 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:16:03,017 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:16:03,017 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:16:05,398 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:16:05,398 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:16:05,399 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 10:16:08,059 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:16:08,260 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:16:08,261 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:16:11,101 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:16:11,150 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:16:11,244 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 10:16:13,016 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:16:13,327 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:16:13,371 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:16:16,515 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:16:16,608 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:16:16,744 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:16:16,871 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:16:16,987 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:16:17,085 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:16:17,203 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:16:17,318 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5376260, 进程: Weixin.exe)
2025-07-30 10:16:17,411 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:16:17,452 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:16:17,549 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5375268, 进程: Weixin.exe)
2025-07-30 10:16:17,628 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 10:16:17,653 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 5375268)
2025-07-30 10:16:17,690 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5375268) - 增强版
2025-07-30 10:16:18,022 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:16:18,054 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:16:18,068 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:16:18,104 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:16:18,132 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:16:18,166 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:16:18,395 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:16:18,398 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:16:18,604 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:16:18,614 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:16:18,616 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:16:18,628 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:16:18,629 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:16:18,629 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:16:18,647 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:16:19,653 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:16:19,656 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:16:19,663 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:16:19,674 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:16:19,681 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5376260, 进程: Weixin.exe)
2025-07-30 10:16:19,683 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:16:19,684 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:16:19,686 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5375268, 进程: Weixin.exe)
2025-07-30 10:16:19,689 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 10:16:19,690 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:16:19,690 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:16:19,691 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:16:19,691 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 10:16:19,691 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:16:19,999 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:16:20,001 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:16:20,003 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:16:20,005 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:16:20,006 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:16:20,008 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:16:20,015 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:16:20,016 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:16:20,016 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:16:20,017 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:16:20,219 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:16:20,220 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:16:20,220 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 10:16:20,522 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:16:20,522 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:16:20,522 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:16:21,523 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:16:21,524 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 10:16:21,524 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:16:21,527 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_101621.log
2025-07-30 10:16:21,528 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:16:21,529 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:16:21,529 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:16:21,531 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 10:16:21,533 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:16:21,536 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:16:21,539 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 10:16:21,540 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 10:16:21,540 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:16:21,540 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:16:21,541 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 10:16:21,541 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 10:16:21,542 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 10:16:21,549 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:16:21,551 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:16:21,552 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 5375268
2025-07-30 10:16:21,553 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5375268) - 增强版
2025-07-30 10:16:21,862 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:16:21,863 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:16:21,864 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:16:21,865 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:16:21,866 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 10:16:21,867 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:16:21,868 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 10:16:21,868 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:16:22,080 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:16:22,081 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:16:22,084 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 5375268 (API返回: None)
2025-07-30 10:16:22,384 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:16:22,385 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:16:22,385 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:16:22,385 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:16:22,387 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:16:22,387 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:16:22,388 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:16:22,393 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:16:22,393 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:16:22,922 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:16:22,923 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:16:23,209 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2925 个
2025-07-30 10:16:23,210 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 10:16:23,211 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2923 个
2025-07-30 10:16:23,211 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2923 个 (总计: 3135 个)
2025-07-30 10:16:23,212 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:16:23,212 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:16:23,213 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:16:23,213 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:16:23,213 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2923
2025-07-30 10:16:23,214 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18660260752 (王祥微)
2025-07-30 10:16:23,214 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:16:29,810 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18660260752
2025-07-30 10:16:29,811 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:16:29,811 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18660260752 执行添加朋友操作...
2025-07-30 10:16:29,812 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:16:29,812 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:16:29,813 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:16:29,814 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:16:29,819 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 10:16:29,822 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:16:29,829 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:16:29,830 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:16:29,830 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:16:29,831 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:16:29,831 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:16:29,831 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:16:29,834 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:16:29,837 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:16:29,838 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:16:29,839 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:16:29,842 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 10:16:29,847 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 10:16:29,848 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:16:30,350 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:16:30,352 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:16:30,415 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.71, 边缘比例0.0335
2025-07-30 10:16:30,426 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_101630.png
2025-07-30 10:16:30,435 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:16:30,438 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:16:30,439 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:16:30,441 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:16:30,442 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:16:30,451 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_101630.png
2025-07-30 10:16:30,454 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 10:16:30,455 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 10:16:30,457 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 10:16:30,464 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:16:30,465 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 10:16:30,467 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 10:16:30,468 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 10:16:30,471 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:16:30,480 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_101630.png
2025-07-30 10:16:30,482 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:16:30,483 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 10:16:30,487 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_101630.png
2025-07-30 10:16:30,515 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:16:30,518 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 10:16:30,521 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:16:30,522 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:16:30,824 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 10:16:31,595 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:16:31,597 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:16:31,598 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:16:31,598 - modules.wechat_auto_add_simple - INFO - ✅ 18660260752 添加朋友操作执行成功
2025-07-30 10:16:31,598 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:16:31,598 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:16:33,600 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:16:33,601 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:16:33,601 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:16:33,601 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:16:33,601 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:16:33,602 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:16:33,602 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:16:33,602 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:16:33,603 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:16:33,603 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18660260752
2025-07-30 10:16:33,604 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:16:33,605 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:16:33,606 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:16:33,608 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:16:33,611 - modules.friend_request_window - INFO -    📱 phone: '18660260752'
2025-07-30 10:16:33,614 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:16:33,614 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:16:34,138 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:16:34,138 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:16:34,140 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:16:34,142 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:16:34,146 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18660260752
2025-07-30 10:16:34,148 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:16:34,149 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:16:34,150 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:16:34,153 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:16:34,153 - modules.friend_request_window - INFO -    📱 手机号码: 18660260752
2025-07-30 10:16:34,154 - modules.friend_request_window - INFO -    🆔 准考证: 014325110129
2025-07-30 10:16:34,155 - modules.friend_request_window - INFO -    👤 姓名: 王祥微
2025-07-30 10:16:34,155 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:16:34,155 - modules.friend_request_window - INFO -    📝 备注格式: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:34,156 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:16:34,156 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:34,156 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:16:34,158 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 7407690, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:16:34,161 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 7407690)
2025-07-30 10:16:34,164 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:16:34,167 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:16:34,173 - modules.friend_request_window - INFO - 🔄 激活窗口: 7407690
2025-07-30 10:16:34,877 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:16:34,877 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:16:34,878 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:16:34,878 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:16:34,878 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:16:34,879 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:16:34,879 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:16:34,879 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:16:34,879 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:16:34,880 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:16:34,880 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:16:34,880 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:16:34,880 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:16:34,881 - modules.friend_request_window - INFO -    📝 remark参数: '014325110129-王祥微-2025-07-30 18:16:34' (类型: <class 'str'>, 长度: 36)
2025-07-30 10:16:34,881 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:16:34,881 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:34,882 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:16:34,882 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:16:34,882 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:16:34,883 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:16:34,883 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:16:34,883 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:16:34,884 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:16:35,798 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:16:41,039 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:16:41,040 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:16:41,040 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:16:41,040 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:16:41,041 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:16:41,351 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:16:41,352 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:16:42,255 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:16:42,263 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:16:42,265 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:16:42,265 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:16:42,266 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:16:42,266 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:16:42,767 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:16:42,768 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:16:42,768 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:16:42,769 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:16:42,769 - modules.friend_request_window - INFO -    📝 内容: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:42,769 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 10:16:42,770 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110129-\xe7\x8e\x8b\xe7\xa5\xa5\xe5\xbe\xae-2025-07-30 18:16:34'
2025-07-30 10:16:42,770 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:16:43,679 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:16:48,921 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:16:48,921 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:16:48,922 - modules.friend_request_window - INFO -    📝 原始文本: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:48,922 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 10:16:48,923 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:16:49,232 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:16:49,233 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:16:50,136 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:16:50,150 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:16:50,151 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:50,152 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:16:50,152 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:50,153 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 10:16:50,654 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:50,654 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:16:50,655 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:16:50,655 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:16:50,655 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:16:50,655 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:16:50,656 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:16:51,456 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:16:51,457 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:16:51,457 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:16:52,061 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:16:52,061 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:16:52,062 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:16:52,062 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:16:52,582 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:52,582 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:52,814 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:52,814 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,053 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,054 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,289 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,289 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,525 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,526 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,764 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:53,764 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,018 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,021 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,264 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,264 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,497 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,498 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,737 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,738 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,970 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:54,970 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,208 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,209 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,445 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,446 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,690 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,690 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,923 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:55,924 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,160 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,161 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,394 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,395 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,632 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,632 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,869 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:56,870 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 10:16:57,090 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 10:16:57,091 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 10:16:58,092 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:16:58,094 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 10:16:58,094 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 10:16:58,095 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:16:58,095 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:16:58,095 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:16:58,095 - modules.friend_request_window - INFO -    📝 备注信息: '014325110129-王祥微-2025-07-30 18:16:34'
2025-07-30 10:16:58,597 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:16:58,597 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:16:58,598 - modules.wechat_auto_add_simple - INFO - ✅ 18660260752 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:16:58,598 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18660260752
2025-07-30 10:16:58,599 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:17:02,221 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2923
2025-07-30 10:17:02,222 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17773601924 (唐涛)
2025-07-30 10:17:02,222 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:17:08,785 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17773601924
2025-07-30 10:17:08,786 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:17:08,786 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17773601924 执行添加朋友操作...
2025-07-30 10:17:08,786 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:17:08,786 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:17:08,787 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:17:08,788 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:17:08,793 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 10:17:08,795 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:17:08,796 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:17:08,797 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:17:08,798 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:17:08,799 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:17:08,800 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:17:08,801 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:17:08,805 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:17:08,809 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:17:08,813 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:17:08,816 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:17:08,819 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1854x978
2025-07-30 10:17:08,821 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 10:17:08,822 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:17:09,326 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:17:09,327 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:17:09,395 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.41, 边缘比例0.0456
2025-07-30 10:17:09,440 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_101709.png
2025-07-30 10:17:09,450 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:17:09,454 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:17:09,457 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:17:09,465 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:17:09,467 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:17:09,474 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_101709.png
2025-07-30 10:17:09,483 - WeChatAutoAdd - INFO - 底部区域原始检测到 39 个轮廓
2025-07-30 10:17:09,487 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 10:17:09,490 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 10:17:09,496 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 10:17:09,499 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 10:17:09,502 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 10:17:09,505 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(182,279), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:17:09,509 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,279), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:17:09,513 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(188,274), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:17:09,515 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(161,272), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 10:17:09,517 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,266), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,520 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,265), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 10:17:09,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(177,264), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 10:17:09,533 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,263), 尺寸20x8, 长宽比2.50, 面积160
2025-07-30 10:17:09,537 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(185,262), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 10:17:09,539 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(179,262), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,545 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,260), 尺寸5x2, 长宽比2.50, 面积10
2025-07-30 10:17:09,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(186,259), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,549 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(184,259), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:17:09,552 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 10:17:09,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:17:09,554 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 10:17:09,563 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,565 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(173,256), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 10:17:09,567 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:17:09,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,255), 尺寸8x5, 长宽比1.60, 面积40
2025-07-30 10:17:09,569 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 10:17:09,571 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,574 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,580 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 10:17:09,582 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,250), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 10:17:09,588 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=66.3 (阈值:60)
2025-07-30 10:17:09,590 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 10:17:09,597 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=63.9 (阈值:60)
2025-07-30 10:17:09,604 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 10:17:09,633 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 10:17:09,637 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 10:17:09,646 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,241), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,648 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,238), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 10:17:09,651 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(160,235), 尺寸4x6, 长宽比0.67, 面积24
2025-07-30 10:17:09,653 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,235), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 10:17:09,659 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,235), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 10:17:09,662 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,233), 尺寸6x10, 长宽比0.60, 面积60
2025-07-30 10:17:09,665 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸37x15, 长宽比2.47, 面积555
2025-07-30 10:17:09,666 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=92.1 (阈值:60)
2025-07-30 10:17:09,669 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:17:09,672 - WeChatAutoAdd - INFO - 底部区域找到 4 个按钮候选
2025-07-30 10:17:09,678 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 10:17:09,682 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 10:17:09,684 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 10:17:09,694 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_101709.png
2025-07-30 10:17:09,698 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 10:17:09,699 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:17:10,001 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 10:17:10,776 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:17:10,778 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:17:10,779 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:17:10,779 - modules.wechat_auto_add_simple - INFO - ✅ 17773601924 添加朋友操作执行成功
2025-07-30 10:17:10,780 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:17:10,781 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:17:12,782 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:17:12,783 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:17:12,783 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:17:12,783 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:17:12,784 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:17:12,784 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:17:12,784 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:17:12,784 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:17:12,785 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:17:12,785 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17773601924
2025-07-30 10:17:12,786 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:17:12,786 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:17:12,786 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:17:12,787 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:17:12,787 - modules.friend_request_window - INFO -    📱 phone: '17773601924'
2025-07-30 10:17:12,787 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:17:12,787 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:17:13,329 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:17:13,330 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:17:13,330 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:17:13,330 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:17:13,332 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17773601924
2025-07-30 10:17:13,332 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:17:13,333 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:17:13,334 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:17:13,334 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:17:13,334 - modules.friend_request_window - INFO -    📱 手机号码: 17773601924
2025-07-30 10:17:13,335 - modules.friend_request_window - INFO -    🆔 准考证: 014325110130
2025-07-30 10:17:13,335 - modules.friend_request_window - INFO -    👤 姓名: 唐涛
2025-07-30 10:17:13,336 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:17:13,337 - modules.friend_request_window - INFO -    📝 备注格式: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:13,337 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:17:13,337 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:13,337 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:17:13,339 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5179424, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:17:13,348 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5179424)
2025-07-30 10:17:13,350 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:17:13,352 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:17:13,352 - modules.friend_request_window - INFO - 🔄 激活窗口: 5179424
2025-07-30 10:17:14,055 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:17:14,056 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:17:14,057 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:17:14,057 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:17:14,057 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:17:14,057 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:17:14,058 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:17:14,058 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:17:14,058 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:17:14,058 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:17:14,059 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:17:14,059 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:17:14,059 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:17:14,060 - modules.friend_request_window - INFO -    📝 remark参数: '014325110130-唐涛-2025-07-30 18:17:13' (类型: <class 'str'>, 长度: 35)
2025-07-30 10:17:14,060 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:17:14,060 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:14,060 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:17:14,061 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:17:14,061 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:17:14,062 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:17:14,063 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:17:14,063 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:17:14,064 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:17:14,980 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:17:20,233 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:17:20,234 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:17:20,234 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:17:20,234 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:17:20,235 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:17:20,544 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:17:20,545 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:17:21,447 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:17:21,457 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:17:21,457 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:17:21,458 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:17:21,459 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:17:21,459 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:17:21,960 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:17:21,960 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:17:21,961 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:17:21,961 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:17:21,961 - modules.friend_request_window - INFO -    📝 内容: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:21,962 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 10:17:21,962 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110130-\xe5\x94\x90\xe6\xb6\x9b-2025-07-30 18:17:13'
2025-07-30 10:17:21,962 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:17:22,876 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:17:28,121 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:17:28,121 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:17:28,122 - modules.friend_request_window - INFO -    📝 原始文本: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:28,122 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 10:17:28,123 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标定位：基于精确坐标 (1364, 252) 进行确定按钮点击...' (前50字符)
2025-07-30 10:17:28,433 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:17:28,433 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:17:29,354 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:17:29,370 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:17:29,370 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:29,372 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:17:29,373 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:29,373 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 10:17:29,874 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:17:29,876 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:17:29,877 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:17:29,878 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:17:29,878 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:17:29,879 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:17:29,881 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:17:30,687 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:17:30,687 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:17:30,688 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:17:31,300 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:17:31,300 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:17:31,301 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:17:31,301 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:17:31,804 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 10:17:31,807 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 10:17:31,808 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 10:17:31,808 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 10:17:31,809 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 10:17:31,812 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 10:17:31,813 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 10:17:31,821 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 10:17:31,830 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 10:17:31,833 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:17:31,834 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 10:17:31,835 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 10:17:31,836 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 10:17:31,837 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 10:17:31,841 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 10:17:31,845 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 10:17:31,846 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:17:31,849 - modules.friend_request_window - INFO - 📊 窗口 Weixin 错误次数: 1/3
2025-07-30 10:17:31,896 - modules.friend_request_window - INFO - 📊 窗口状态统计: 0/5 个窗口已失败
2025-07-30 10:17:31,897 - modules.friend_request_window - INFO - 🔄 步骤1: 点击错误提示窗口的'确定'按钮...
2025-07-30 10:17:31,898 - modules.friend_request_window - INFO - 🎯 开始使用坐标定位点击错误对话框的确定按钮...
2025-07-30 10:17:31,898 - modules.friend_request_window - INFO - 🔧 调试：_click_error_dialog_ok_button 方法被调用
2025-07-30 10:17:31,899 - modules.friend_request_window - INFO - 🔍 智能查找确定按钮位置...
2025-07-30 10:17:31,900 - modules.friend_request_window - INFO - 🔍 发现错误对话框: Weixin (Qt51514QWindowIcon) 330x194
2025-07-30 10:17:31,901 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 10:17:31,901 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 10:17:31,902 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 10:17:31,902 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 10:17:31,903 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1854x978
2025-07-30 10:17:31,905 - modules.friend_request_window - INFO - 📊 共发现 6 个可能的错误对话框
2025-07-30 10:17:31,906 - modules.friend_request_window - INFO - 🔧 调试：找到 6 个错误对话框
2025-07-30 10:17:31,907 - modules.friend_request_window - INFO - 🎯 尝试点击对话框: Weixin (大小: 330x194)
2025-07-30 10:17:31,911 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:17:31,913 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (165, 220)
2025-07-30 10:17:31,913 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (165, 220)
2025-07-30 10:17:31,914 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 10:17:31,914 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 10:17:31,914 - modules.friend_request_window - INFO - 🎯 开始坐标点击操作，目标坐标: (165, 220)
2025-07-30 10:17:31,916 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:17:31,917 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:17:32,865 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:32,866 - modules.friend_request_window - INFO - 🖱️ 方法2: win32api点击...
2025-07-30 10:17:33,976 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:33,977 - modules.friend_request_window - INFO - 🖱️ 方法3: 多次连续点击...
2025-07-30 10:17:33,977 - modules.friend_request_window - INFO -    第1次点击...
2025-07-30 10:17:34,392 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:34,392 - modules.friend_request_window - INFO -    第2次点击...
2025-07-30 10:17:34,861 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:34,862 - modules.friend_request_window - INFO -    第3次点击...
2025-07-30 10:17:35,294 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:35,294 - modules.friend_request_window - ERROR - ❌ 所有坐标点击方法都失败
2025-07-30 10:17:35,295 - modules.friend_request_window - INFO - 🎯 尝试点击对话框: 添加朋友 (大小: 328x454)
2025-07-30 10:17:35,295 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:17:35,295 - modules.friend_request_window - INFO - 📍 计算确定按钮位置: (1364, 340)
2025-07-30 10:17:35,296 - modules.friend_request_window - INFO - 🎯 开始坐标点击操作，目标坐标: (1364, 340)
2025-07-30 10:17:35,296 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:17:35,296 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:17:36,208 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:36,208 - modules.friend_request_window - INFO - 🖱️ 方法2: win32api点击...
2025-07-30 10:17:37,326 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:37,326 - modules.friend_request_window - INFO - 🖱️ 方法3: 多次连续点击...
2025-07-30 10:17:37,327 - modules.friend_request_window - INFO -    第1次点击...
2025-07-30 10:17:37,742 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:37,743 - modules.friend_request_window - INFO -    第2次点击...
2025-07-30 10:17:38,159 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:38,160 - modules.friend_request_window - INFO -    第3次点击...
2025-07-30 10:17:38,575 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:38,575 - modules.friend_request_window - ERROR - ❌ 所有坐标点击方法都失败
2025-07-30 10:17:38,576 - modules.friend_request_window - INFO - 🎯 尝试点击对话框: 微信 (大小: 726x650)
2025-07-30 10:17:38,577 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:17:38,577 - modules.friend_request_window - INFO - 📍 计算确定按钮位置: (363, 487)
2025-07-30 10:17:38,578 - modules.friend_request_window - INFO - 🎯 开始坐标点击操作，目标坐标: (363, 487)
2025-07-30 10:17:38,579 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:17:38,580 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:17:39,494 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:39,495 - modules.friend_request_window - INFO - 🖱️ 方法2: win32api点击...
2025-07-30 10:17:40,609 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:40,609 - modules.friend_request_window - INFO - 🖱️ 方法3: 多次连续点击...
2025-07-30 10:17:40,609 - modules.friend_request_window - INFO -    第1次点击...
2025-07-30 10:17:41,024 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:41,025 - modules.friend_request_window - INFO -    第2次点击...
2025-07-30 10:17:41,442 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:41,443 - modules.friend_request_window - INFO -    第3次点击...
2025-07-30 10:17:41,859 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:41,860 - modules.friend_request_window - ERROR - ❌ 所有坐标点击方法都失败
2025-07-30 10:17:41,861 - modules.friend_request_window - INFO - 🎯 尝试点击对话框: 添加朋友 (大小: 328x454)
2025-07-30 10:17:41,861 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:17:41,863 - modules.friend_request_window - INFO - 📍 计算确定按钮位置: (1364, 340)
2025-07-30 10:17:41,863 - modules.friend_request_window - INFO - 🎯 开始坐标点击操作，目标坐标: (1364, 340)
2025-07-30 10:17:41,864 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:17:41,864 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:17:42,776 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:42,777 - modules.friend_request_window - INFO - 🖱️ 方法2: win32api点击...
2025-07-30 10:17:43,909 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:43,910 - modules.friend_request_window - INFO - 🖱️ 方法3: 多次连续点击...
2025-07-30 10:17:43,910 - modules.friend_request_window - INFO -    第1次点击...
2025-07-30 10:17:44,325 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:44,326 - modules.friend_request_window - INFO -    第2次点击...
2025-07-30 10:17:44,743 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:44,743 - modules.friend_request_window - INFO -    第3次点击...
2025-07-30 10:17:45,159 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:45,159 - modules.friend_request_window - ERROR - ❌ 所有坐标点击方法都失败
2025-07-30 10:17:45,159 - modules.friend_request_window - INFO - 🎯 尝试点击对话框: 微信 (大小: 726x650)
2025-07-30 10:17:45,160 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:17:45,160 - modules.friend_request_window - INFO - 📍 计算确定按钮位置: (363, 487)
2025-07-30 10:17:45,161 - modules.friend_request_window - INFO - 🎯 开始坐标点击操作，目标坐标: (363, 487)
2025-07-30 10:17:45,161 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:17:45,161 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:17:46,074 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:46,075 - modules.friend_request_window - INFO - 🖱️ 方法2: win32api点击...
2025-07-30 10:17:47,209 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:47,209 - modules.friend_request_window - INFO - 🖱️ 方法3: 多次连续点击...
2025-07-30 10:17:47,209 - modules.friend_request_window - INFO -    第1次点击...
2025-07-30 10:17:47,624 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:47,624 - modules.friend_request_window - INFO -    第2次点击...
2025-07-30 10:17:48,060 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:48,061 - modules.friend_request_window - INFO -    第3次点击...
2025-07-30 10:17:48,474 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:48,475 - modules.friend_request_window - ERROR - ❌ 所有坐标点击方法都失败
2025-07-30 10:17:48,475 - modules.friend_request_window - INFO - 🎯 尝试点击对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (大小: 1854x978)
2025-07-30 10:17:48,477 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:17:48,477 - modules.friend_request_window - INFO - 📍 计算确定按钮位置: (986, 763)
2025-07-30 10:17:48,478 - modules.friend_request_window - INFO - 🎯 开始坐标点击操作，目标坐标: (986, 763)
2025-07-30 10:17:48,478 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:17:48,479 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:17:49,429 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:49,429 - modules.friend_request_window - INFO - 🖱️ 方法2: win32api点击...
2025-07-30 10:17:50,543 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:50,544 - modules.friend_request_window - INFO - 🖱️ 方法3: 多次连续点击...
2025-07-30 10:17:50,544 - modules.friend_request_window - INFO -    第1次点击...
2025-07-30 10:17:50,993 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:50,994 - modules.friend_request_window - INFO -    第2次点击...
2025-07-30 10:17:51,408 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:51,408 - modules.friend_request_window - INFO -    第3次点击...
2025-07-30 10:17:51,843 - modules.friend_request_window - INFO - ⚠️ 验证失败：错误提示窗口仍然存在
2025-07-30 10:17:51,844 - modules.friend_request_window - ERROR - ❌ 所有坐标点击方法都失败
2025-07-30 10:17:51,844 - modules.friend_request_window - INFO - 🔄 强制点击添加朋友窗口确定按钮坐标...
2025-07-30 10:17:51,844 - modules.friend_request_window - INFO - 🔧 调试：即将调用 _force_click_add_friend_ok_button 方法
2025-07-30 10:17:51,844 - modules.friend_request_window - INFO - 🎯 强制点击添加朋友窗口确定按钮...
2025-07-30 10:17:51,845 - modules.friend_request_window - INFO - 🔧 调试：开始执行强制点击方法
2025-07-30 10:17:51,846 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 10:17:51,846 - modules.friend_request_window - INFO - 🔍 发现错误对话框: Weixin (Qt51514QWindowIcon) 330x194
2025-07-30 10:17:51,847 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 10:17:51,847 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 添加朋友 (Qt51514QWindowIcon) 328x454
2025-07-30 10:17:51,847 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 微信 (Qt51514QWindowIcon) 726x650
2025-07-30 10:17:51,848 - modules.friend_request_window - INFO - 🔍 发现错误对话框: 014325110097-董源臣-2025-07-29 22:0 (Qt51514QWindowIcon) 598x640
2025-07-30 10:17:51,848 - modules.friend_request_window - INFO - 🔍 发现错误对话框: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code (Chrome_WidgetWin_1) 1854x978
2025-07-30 10:17:51,850 - modules.friend_request_window - INFO - 📊 共发现 7 个可能的错误对话框
2025-07-30 10:17:51,851 - modules.friend_request_window - INFO - 🎯 基于窗口 Weixin 计算确定按钮位置: (1364, 274)
2025-07-30 10:17:51,852 - modules.friend_request_window - INFO - 🔧 调试：准备点击 5 个坐标位置
2025-07-30 10:17:51,852 - modules.friend_request_window - INFO - 🖱️ 尝试点击坐标 1/5: (1364, 274)
2025-07-30 10:17:51,853 - modules.friend_request_window - INFO - 🔧 调试：执行 pyautogui.click(1364, 274)
2025-07-30 10:17:52,776 - modules.friend_request_window - INFO - ✅ pyautogui点击坐标 (1364, 274) 完成
2025-07-30 10:17:53,277 - modules.friend_request_window - INFO - 🔧 调试：执行 win32api 点击(1364, 274)
2025-07-30 10:17:54,109 - modules.friend_request_window - INFO - 🖱️ 尝试点击坐标 2/5: (1364, 254)
2025-07-30 10:17:54,109 - modules.friend_request_window - INFO - 🔧 调试：执行 pyautogui.click(1364, 254)
2025-07-30 10:17:55,012 - modules.friend_request_window - INFO - ✅ pyautogui点击坐标 (1364, 254) 完成
2025-07-30 10:17:55,513 - modules.friend_request_window - INFO - ✅ 窗口已关闭，点击成功！
2025-07-30 10:17:55,513 - modules.friend_request_window - INFO - ✅ 强制坐标点击成功
2025-07-30 10:17:55,514 - modules.friend_request_window - INFO - 🔧 调试：_click_error_dialog_ok_button 返回 True
2025-07-30 10:17:55,514 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 10:17:56,515 - modules.friend_request_window - INFO - 🔄 步骤2: 精确关闭出现频繁错误的微信窗口...
2025-07-30 10:17:56,516 - modules.friend_request_window - INFO - 🎯 使用坐标点击关闭微信窗口: Weixin
2025-07-30 10:17:56,516 - modules.friend_request_window - INFO - 📋 目标窗口句柄: 5704898
2025-07-30 10:17:56,516 - modules.friend_request_window - INFO - 📋 执行顺序: 1.关闭添加朋友窗口 → 2.关闭微信主窗口
2025-07-30 10:17:56,516 - modules.friend_request_window - INFO - 🔄 步骤1: 使用坐标关闭添加朋友窗口...
2025-07-30 10:17:56,517 - modules.friend_request_window - INFO - 🎯 使用坐标点击关闭添加朋友窗口...
2025-07-30 10:17:56,517 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...
2025-07-30 10:17:57,018 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (1504, 13)...
2025-07-30 10:17:58,127 - modules.friend_request_window - INFO - ✅ pyautogui点击添加朋友窗口关闭按钮成功
2025-07-30 10:17:58,127 - modules.friend_request_window - INFO - 🔄 步骤2: 使用坐标关闭微信主窗口...
2025-07-30 10:17:58,127 - modules.friend_request_window - INFO - 🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...
2025-07-30 10:17:58,628 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui点击坐标 (700, 16)...
2025-07-30 10:17:59,740 - modules.friend_request_window - INFO - ✅ pyautogui点击当前微信窗口关闭按钮成功
2025-07-30 10:17:59,741 - modules.friend_request_window - INFO - ✅ 坐标关闭完成
2025-07-30 10:17:59,742 - modules.friend_request_window - INFO - 🔄 步骤3: 执行最终清理操作...
2025-07-30 10:17:59,743 - modules.friend_request_window - INFO - 🧹 简化清理残留窗口...
2025-07-30 10:18:00,744 - modules.friend_request_window - INFO - ✅ 清理完成
2025-07-30 10:18:00,744 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 10:18:00,744 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，需要重新开始流程
2025-07-30 10:18:00,745 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 10:18:00,745 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 10:18:00,746 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 10:18:00,746 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:18:00,747 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:18:00,747 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:18:00,747 - modules.friend_request_window - INFO -    📝 备注信息: '014325110130-唐涛-2025-07-30 18:17:13'
2025-07-30 10:18:01,248 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:18:01,249 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:18:01,249 - modules.wechat_auto_add_simple - INFO - ✅ 17773601924 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:18:01,250 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17773601924
2025-07-30 10:18:01,251 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:18:03,034 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 10:18:03,034 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 10:18:03,035 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 10:18:03,036 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 10:18:03,037 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 10:18:03,037 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 10:18:03,038 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 10:18:03,038 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 10:18:03,038 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 10:18:03,039 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-30 10:18:03,039 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 10:18:03,040 - __main__ - INFO - � 更新全局进度：已处理 4/2927 个联系人
2025-07-30 10:18:03,040 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 10:18:06,041 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 10:18:06,041 - __main__ - INFO - 📊 当前进度：已处理 4/2927 个联系人
2025-07-30 10:18:06,041 - __main__ - INFO - 
============================================================
2025-07-30 10:18:06,042 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 2 轮)
2025-07-30 10:18:06,042 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:18:06,042 - __main__ - INFO - 📊 全局进度：已处理 4/2927 个联系人
2025-07-30 10:18:06,042 - __main__ - INFO - ============================================================
2025-07-30 10:18:06,043 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 10:18:06,043 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:18:06,044 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:18:06,044 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 10:18:06,044 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:18:06,367 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:18:06,368 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:18:06,368 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:18:06,369 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:18:06,369 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:18:06,369 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:18:06,372 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:18:06,373 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:18:06,374 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:18:06,375 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:18:06,577 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:18:06,577 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:18:06,578 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:18:06,578 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:18:06,882 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:18:06,882 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:18:06,883 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:18:06,884 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:18:06,884 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:18:06,885 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:18:06,886 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:18:06,888 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:18:06,889 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:18:06,890 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:18:07,092 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:18:07,093 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:18:07,095 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:18:07,396 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:18:07,397 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:18:07,397 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:18:07,398 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:18:07,398 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:18:07,398 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:18:07,399 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:18:07,399 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:18:08,400 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:18:08,400 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 10:18:08,400 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:18:08,401 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:18:08,401 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:18:08,402 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:18:08,402 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:18:08,403 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:18:08,604 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:18:08,605 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:18:10,994 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:18:10,995 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:18:10,995 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 10:18:13,175 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:18:13,376 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:18:13,377 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:18:15,783 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:18:15,784 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:18:15,785 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
