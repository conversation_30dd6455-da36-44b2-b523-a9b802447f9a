#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FrequencyErrorHandler 修复验证测试脚本

测试修复的 is_restart_required、_set_restart_flag、clear_restart_flag 方法
"""

import logging
import sys
import time
from typing import Dict, Any

def setup_logger() -> logging.Logger:
    """设置测试日志记录器"""
    logger = logging.getLogger('frequency_error_fix_test')
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def test_frequency_error_handler_methods():
    """测试 FrequencyErrorHandler 的重新开始标志方法"""
    logger = setup_logger()
    
    try:
        logger.info("🧪 开始测试 FrequencyErrorHandler 修复...")
        
        # 1. 导入测试
        logger.info("📦 测试模块导入...")
        from modules.frequency_error_handler import FrequencyErrorHandler
        logger.info("✅ 模块导入成功")
        
        # 2. 创建实例
        logger.info("🔧 创建 FrequencyErrorHandler 实例...")
        handler = FrequencyErrorHandler(logger)
        logger.info("✅ 实例创建成功")
        
        # 3. 检查方法存在性
        logger.info("🔍 检查关键方法存在性...")
        required_methods = [
            'is_restart_required',
            '_set_restart_flag', 
            'clear_restart_flag',
            'get_restart_status'
        ]
        
        for method_name in required_methods:
            if hasattr(handler, method_name):
                logger.info(f"✅ {method_name} 方法存在")
            else:
                logger.error(f"❌ {method_name} 方法不存在")
                return False
        
        # 4. 测试初始状态
        logger.info("🔍 测试初始状态...")
        initial_state = handler.is_restart_required()
        if not initial_state:
            logger.info("✅ 初始状态正确：重新开始标志为 False")
        else:
            logger.error("❌ 初始状态错误：重新开始标志应为 False")
            return False
        
        # 5. 测试设置重新开始标志
        logger.info("🔧 测试设置重新开始标志...")
        handler._set_restart_flag()
        after_set = handler.is_restart_required()
        if after_set:
            logger.info("✅ 设置重新开始标志成功")
        else:
            logger.error("❌ 设置重新开始标志失败")
            return False
        
        # 6. 测试清除重新开始标志
        logger.info("🧹 测试清除重新开始标志...")
        handler.clear_restart_flag()
        after_clear = handler.is_restart_required()
        if not after_clear:
            logger.info("✅ 清除重新开始标志成功")
        else:
            logger.error("❌ 清除重新开始标志失败")
            return False
        
        # 7. 测试状态信息获取
        logger.info("📊 测试状态信息获取...")
        status = handler.get_restart_status()
        if isinstance(status, dict) and 'restart_required' in status:
            logger.info(f"✅ 状态信息获取成功: {status}")
        else:
            logger.error("❌ 状态信息获取失败")
            return False
        
        # 8. 模拟原来出错的场景
        logger.info("🎭 模拟原来出错的场景...")
        
        # 模拟 wechat_auto_add_simple.py 中的代码
        logger.info("   模拟联系人处理前检查...")
        if handler.is_restart_required():
            logger.info("   检测到需要重新开始")
        else:
            logger.info("   ✅ 正常状态，继续处理")
        
        # 模拟频率错误发生
        logger.info("   模拟频率错误发生...")
        handler._set_restart_flag()
        
        # 模拟联系人处理循环中的检查
        logger.info("   模拟联系人处理循环检查...")
        if handler.is_restart_required():
            logger.info("   ✅ 检测到频率错误，应该停止循环")
            handler.clear_restart_flag()
            logger.info("   ✅ 已清除重新开始标志")
        else:
            logger.error("   ❌ 未能检测到频率错误")
            return False
        
        logger.info("🎉 所有测试通过！FrequencyErrorHandler 修复成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 FrequencyErrorHandler 修复验证测试")
    print("=" * 60)
    
    success = test_frequency_error_handler_methods()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 修复验证测试通过！")
        print("📋 修复内容:")
        print("   - 添加了 is_restart_required() 方法")
        print("   - 添加了 _set_restart_flag() 方法") 
        print("   - 添加了 clear_restart_flag() 方法")
        print("   - 添加了 get_restart_status() 方法")
        print("   - 在 handle_frequency_error() 成功后自动设置重新开始标志")
        print("   - 所有方法都使用线程安全锁保护")
        print("\n🎯 现在 wechat_auto_add_simple.py 中的错误应该已经解决！")
        return 0
    else:
        print("❌ 修复验证测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
