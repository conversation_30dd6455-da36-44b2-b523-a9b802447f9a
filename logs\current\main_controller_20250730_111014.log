2025-07-30 11:10:14,602 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:10:14,603 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:10:14,603 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:10:14,604 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:10:14,604 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 11:10:14,605 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:10:14,605 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:10:14,606 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:10:14,606 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:10:14,607 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:10:14,608 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:10:14,610 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:10:14,613 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_111014.log
2025-07-30 11:10:14,615 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:10:14,616 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:10:14,616 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:10:14,616 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 11:10:14,617 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 11:10:14,617 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 19:10:14
2025-07-30 11:10:14,618 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 11:10:14,618 - __main__ - INFO - 📅 启动时间: 2025-07-30 19:10:14
2025-07-30 11:10:14,619 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 11:10:14,619 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:10:15,158 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:10:15,158 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:10:15,688 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:10:15,689 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:10:15,691 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:10:15,692 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 11:10:15,692 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 11:10:15,692 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 11:10:15,693 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 11:10:16,660 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 11:10:16,661 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 11:10:16,662 - __main__ - INFO - 📋 待处理联系人数: 2905
2025-07-30 11:10:16,662 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 11:10:16,663 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2905
2025-07-30 11:10:16,663 - __main__ - INFO - 
============================================================
2025-07-30 11:10:16,663 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 11:10:16,664 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:10:16,664 - __main__ - INFO - 📊 全局进度：已处理 0/2905 个联系人
2025-07-30 11:10:16,664 - __main__ - INFO - ============================================================
2025-07-30 11:10:16,665 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 11:10:16,665 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 11:10:16,665 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:10:16,666 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 11:10:16,666 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:10:16,987 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:10:16,988 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:10:16,988 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:10:16,989 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:10:16,989 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:10:16,989 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:10:16,990 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:10:16,990 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:10:16,990 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:10:16,990 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:10:17,193 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:10:17,194 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:10:17,194 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:10:17,194 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:10:17,503 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:10:17,515 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:10:17,530 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:10:17,536 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:10:17,541 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:10:17,542 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:10:17,544 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:10:17,545 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:10:17,547 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:10:17,548 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:10:17,770 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:10:17,771 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:10:17,773 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:10:18,074 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:10:18,074 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:10:18,074 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:10:18,075 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:10:18,075 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:10:18,075 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:10:18,076 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:10:18,076 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:10:19,077 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:10:19,077 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 11:10:19,078 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:10:19,078 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:10:19,079 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:10:19,079 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:10:19,079 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:10:19,080 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:10:19,281 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:10:19,282 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:10:21,664 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:10:21,665 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:10:21,665 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 11:10:23,464 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:10:23,665 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:10:23,665 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:10:26,034 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:10:26,034 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:10:26,034 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 11:10:28,394 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:10:28,595 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:10:28,596 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:10:30,980 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:10:30,981 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:10:30,981 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 11:10:33,780 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:10:33,981 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:10:33,982 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:10:36,349 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:10:36,350 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:10:36,350 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 11:10:39,256 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:10:39,457 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:10:39,458 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:10:41,830 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:10:41,830 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:10:41,831 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:10:41,831 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:10:41,832 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:10:41,833 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:10:41,834 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:10:41,835 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:10:41,835 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:10:41,836 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7734786, 进程: Weixin.exe)
2025-07-30 11:10:41,839 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:10:41,840 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 7734786)
2025-07-30 11:10:41,841 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 7734786) - 增强版
2025-07-30 11:10:42,145 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:10:42,146 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:10:42,146 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:10:42,146 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:10:42,147 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:10:42,147 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:10:42,351 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:10:42,351 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:10:42,553 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:10:42,554 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:10:42,554 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:10:42,555 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:10:42,555 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:10:42,555 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:10:42,555 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:10:43,556 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:10:43,556 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:10:43,558 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:10:43,559 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:10:43,560 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:10:43,560 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:10:43,561 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7734786, 进程: Weixin.exe)
2025-07-30 11:10:43,564 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 11:10:43,565 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:10:43,565 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:10:43,565 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:10:43,566 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 11:10:43,566 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:10:43,873 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:10:43,873 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:10:43,874 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:10:43,874 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:10:43,874 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:10:43,875 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:10:43,875 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:10:43,875 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:10:43,876 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:10:43,876 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:10:44,078 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:10:44,079 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:10:44,080 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 11:10:44,380 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:10:44,381 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:10:44,381 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:10:45,382 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:10:45,382 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 11:10:45,383 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:10:45,386 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_111045.log
2025-07-30 11:10:45,387 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:10:45,387 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:10:45,387 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:10:45,388 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 11:10:45,388 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:10:45,388 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:10:45,392 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 11:10:45,392 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 11:10:45,393 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:10:45,393 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:10:45,394 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 11:10:45,394 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 11:10:45,395 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:10:45,396 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:10:45,397 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 7734786
2025-07-30 11:10:45,397 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 7734786) - 增强版
2025-07-30 11:10:45,706 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:10:45,706 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:10:45,707 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:10:45,707 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:10:45,708 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:10:45,708 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:10:45,708 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:10:45,709 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:10:45,910 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:10:45,911 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:10:45,914 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 7734786 (API返回: None)
2025-07-30 11:10:46,215 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:10:46,215 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:10:46,216 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:10:46,217 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:10:46,218 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:10:46,218 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:10:46,219 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:10:46,222 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:10:46,224 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:10:46,712 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:10:46,712 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:10:46,966 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2915 个
2025-07-30 11:10:46,967 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2915 个 (总计: 3135 个)
2025-07-30 11:10:46,968 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:10:46,968 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:10:46,968 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:10:46,969 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:10:46,969 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2915
2025-07-30 11:10:46,969 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13356873403 (辛奕辰)
2025-07-30 11:10:46,970 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:10:53,539 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13356873403
2025-07-30 11:10:53,539 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:10:53,540 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13356873403 执行添加朋友操作...
2025-07-30 11:10:53,540 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:10:53,540 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:10:53,541 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:10:53,542 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:10:53,546 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:10:53,547 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:10:53,548 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:10:53,549 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:10:53,550 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:10:53,550 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:10:53,550 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:10:53,551 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:10:53,555 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:10:53,557 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:10:53,558 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:10:53,564 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:10:53,570 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 11:10:53,572 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:10:54,073 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:10:54,074 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:10:54,152 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差42.48, 边缘比例0.0371
2025-07-30 11:10:54,160 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_111054.png
2025-07-30 11:10:54,162 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:10:54,164 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:10:54,168 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:10:54,172 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:10:54,173 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:10:54,179 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_111054.png
2025-07-30 11:10:54,181 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:10:54,182 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:10:54,184 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:10:54,189 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:10:54,190 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:10:54,191 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:10:54,195 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:10:54,196 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:10:54,205 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_111054.png
2025-07-30 11:10:54,207 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:10:54,211 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:10:54,217 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_111054.png
2025-07-30 11:10:54,286 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:10:54,288 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:10:54,288 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:10:54,289 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:10:54,590 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:10:55,358 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:10:55,361 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:10:55,363 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:10:55,363 - modules.wechat_auto_add_simple - INFO - ✅ 13356873403 添加朋友操作执行成功
2025-07-30 11:10:55,364 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:10:55,367 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:10:57,368 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:10:57,369 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:10:57,369 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:10:57,370 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:10:57,370 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:10:57,370 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:10:57,370 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:10:57,371 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:10:57,371 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:10:57,371 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13356873403
2025-07-30 11:10:57,375 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:10:57,375 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:10:57,376 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:10:57,376 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:10:57,377 - modules.friend_request_window - INFO -    📱 phone: '13356873403'
2025-07-30 11:10:57,377 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:10:57,378 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:10:57,866 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:10:57,866 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:10:57,867 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:10:57,867 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:10:57,869 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13356873403
2025-07-30 11:10:57,869 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:10:57,870 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:10:57,871 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:10:57,871 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:10:57,872 - modules.friend_request_window - INFO -    📱 手机号码: 13356873403
2025-07-30 11:10:57,872 - modules.friend_request_window - INFO -    🆔 准考证: 014325110142
2025-07-30 11:10:57,873 - modules.friend_request_window - INFO -    👤 姓名: 辛奕辰
2025-07-30 11:10:57,873 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:10:57,874 - modules.friend_request_window - INFO -    📝 备注格式: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:10:57,875 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:10:57,875 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:10:57,876 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:10:57,878 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4001004, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:10:57,880 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4001004)
2025-07-30 11:10:57,885 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:10:57,886 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:10:57,886 - modules.friend_request_window - INFO - 🔄 激活窗口: 4001004
2025-07-30 11:10:58,590 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:10:58,590 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:10:58,590 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:10:58,591 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:10:58,592 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:10:58,592 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:10:58,592 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:10:58,593 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:10:58,593 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:10:58,593 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:10:58,594 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:10:58,594 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:10:58,595 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:10:58,595 - modules.friend_request_window - INFO -    📝 remark参数: '014325110142-辛奕辰-2025-07-30 19:10:57' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:10:58,596 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:10:58,596 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:10:58,596 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:10:58,597 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:10:58,597 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:10:58,597 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:10:58,597 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:10:58,598 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:10:58,598 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:10:59,511 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:11:04,755 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:11:04,756 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:11:04,756 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:11:04,756 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:11:04,758 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '在微信频率错误处理流程中，当程序切换到微信2窗口时，存在一个坐标定位问题：新切换的微信2窗口中的"添...' (前50字符)
2025-07-30 11:11:05,068 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:11:05,069 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:11:05,972 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:11:05,982 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:11:05,982 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:11:05,983 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:11:05,983 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:11:05,984 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:11:06,485 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:11:06,485 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:11:06,485 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:11:06,486 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:11:06,486 - modules.friend_request_window - INFO -    📝 内容: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:11:06,486 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:11:06,487 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110142-\xe8\xbe\x9b\xe5\xa5\x95\xe8\xbe\xb0-2025-07-30 19:10:57'
2025-07-30 11:11:06,487 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:11:07,395 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:11:12,638 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:11:12,639 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:11:12,639 - modules.friend_request_window - INFO -    📝 原始文本: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:11:12,639 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:11:12,640 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '在微信频率错误处理流程中，当程序切换到微信2窗口时，存在一个坐标定位问题：新切换的微信2窗口中的"添...' (前50字符)
2025-07-30 11:11:12,951 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:11:12,951 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:11:13,854 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:11:13,865 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:11:13,866 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:11:13,867 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:11:13,868 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:11:13,868 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:11:14,369 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:11:14,369 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:11:14,370 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:11:14,370 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:11:14,370 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:11:14,370 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:11:14,371 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:11:15,172 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:11:15,172 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:11:15,172 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:11:15,816 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:11:15,817 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:11:15,817 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:11:15,817 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:11:16,335 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:16,571 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:16,805 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:17,043 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:17,281 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:17,515 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:17,754 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:17,988 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:18,229 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:18,464 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:18,698 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:18,934 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:19,172 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:19,406 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:19,640 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:19,876 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:20,113 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:20,346 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:20,582 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:20,818 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:21,035 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:11:21,035 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:11:22,036 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:11:22,038 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:11:22,039 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:11:22,039 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:11:22,040 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:11:22,040 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:11:22,041 - modules.friend_request_window - INFO -    📝 备注信息: '014325110142-辛奕辰-2025-07-30 19:10:57'
2025-07-30 11:11:22,541 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:11:22,542 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:11:22,543 - modules.wechat_auto_add_simple - INFO - ✅ 13356873403 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:11:22,543 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13356873403
2025-07-30 11:11:22,544 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:11:26,123 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2915
2025-07-30 11:11:26,123 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18644881373 (杨志远)
2025-07-30 11:11:26,123 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:11:32,703 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18644881373
2025-07-30 11:11:32,704 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:11:32,704 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18644881373 执行添加朋友操作...
2025-07-30 11:11:32,704 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:11:32,704 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:11:32,705 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:11:32,707 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:11:32,712 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:11:32,714 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:11:32,715 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:11:32,715 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:11:32,715 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:11:32,716 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:11:32,716 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:11:32,718 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:11:32,724 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:11:32,726 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:11:32,728 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:11:32,732 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:11:32,735 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 11:11:32,736 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:11:33,238 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:11:33,239 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:11:33,315 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 11:11:33,316 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 11:11:33,323 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_111133.png
2025-07-30 11:11:33,328 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:11:33,332 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:11:33,334 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:11:33,336 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:11:33,341 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:11:33,352 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_111133.png
2025-07-30 11:11:33,357 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:11:33,361 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:11:33,364 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:11:33,365 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:11:33,366 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:11:33,368 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:11:33,369 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:11:33,370 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:11:33,382 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_111133.png
2025-07-30 11:11:33,384 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:11:33,385 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:11:33,389 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_111133.png
2025-07-30 11:11:33,419 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:11:33,421 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:11:33,428 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:11:33,430 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:11:33,731 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:11:34,499 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:11:34,501 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:11:34,502 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:11:34,502 - modules.wechat_auto_add_simple - INFO - ✅ 18644881373 添加朋友操作执行成功
2025-07-30 11:11:34,502 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:11:34,502 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:11:36,504 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:11:36,505 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:11:36,505 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:11:36,506 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:11:36,506 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:11:36,506 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:11:36,506 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:11:36,507 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:11:36,507 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:11:36,507 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18644881373
2025-07-30 11:11:36,508 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:11:36,508 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:11:36,508 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:11:36,509 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:11:36,509 - modules.friend_request_window - INFO -    📱 phone: '18644881373'
2025-07-30 11:11:36,509 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:11:36,509 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:11:37,051 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:11:37,051 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:11:37,052 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:11:37,052 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:11:37,053 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18644881373
2025-07-30 11:11:37,054 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:11:37,054 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:11:37,055 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:11:37,055 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:11:37,056 - modules.friend_request_window - INFO -    📱 手机号码: 18644881373
2025-07-30 11:11:37,056 - modules.friend_request_window - INFO -    🆔 准考证: 014325110143
2025-07-30 11:11:37,057 - modules.friend_request_window - INFO -    👤 姓名: 杨志远
2025-07-30 11:11:37,058 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:11:37,058 - modules.friend_request_window - INFO -    📝 备注格式: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:37,059 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:11:37,059 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:37,059 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:11:37,061 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5049294, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:11:37,062 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5049294)
2025-07-30 11:11:37,062 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:11:37,063 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:11:37,063 - modules.friend_request_window - INFO - 🔄 激活窗口: 5049294
2025-07-30 11:11:37,766 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:11:37,766 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:11:37,767 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:11:37,767 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:11:37,768 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:11:37,768 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:11:37,769 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:11:37,769 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:11:37,769 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:11:37,770 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:11:37,770 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:11:37,770 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:11:37,770 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:11:37,771 - modules.friend_request_window - INFO -    📝 remark参数: '014325110143-杨志远-2025-07-30 19:11:37' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:11:37,771 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:11:37,771 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:37,771 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:11:37,772 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:11:37,774 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:11:37,774 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:11:37,775 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:11:37,775 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:11:37,775 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:11:38,693 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:11:43,936 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:11:43,936 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:11:43,936 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:11:43,937 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:11:43,937 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '在微信频率错误处理流程中，当程序切换到微信2窗口时，存在一个坐标定位问题：新切换的微信2窗口中的"添...' (前50字符)
2025-07-30 11:11:44,246 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:11:44,247 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:11:45,150 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:11:45,159 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:11:45,160 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:11:45,161 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:11:45,162 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:11:45,163 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:11:45,663 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:11:45,664 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:11:45,664 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:11:45,664 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:11:45,665 - modules.friend_request_window - INFO -    📝 内容: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:45,665 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:11:45,665 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110143-\xe6\x9d\xa8\xe5\xbf\x97\xe8\xbf\x9c-2025-07-30 19:11:37'
2025-07-30 11:11:45,665 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:11:46,575 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:11:51,817 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:11:51,817 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:11:51,818 - modules.friend_request_window - INFO -    📝 原始文本: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:51,818 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:11:51,818 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '在微信频率错误处理流程中，当程序切换到微信2窗口时，存在一个坐标定位问题：新切换的微信2窗口中的"添...' (前50字符)
2025-07-30 11:11:52,129 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:11:52,129 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:11:53,032 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:11:53,043 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:11:53,044 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:53,045 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:11:53,045 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:53,045 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:11:53,546 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:11:53,547 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:11:53,547 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:11:53,547 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:11:53,548 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:11:53,548 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:11:53,548 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:11:54,349 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:11:54,349 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:11:54,349 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:11:54,959 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:11:54,960 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:11:54,960 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:11:54,960 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:11:55,480 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:55,715 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:55,949 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:56,181 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:56,417 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:56,650 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:56,884 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:57,118 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:57,351 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:57,585 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:57,818 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:58,055 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:58,287 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:58,523 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:58,757 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:58,994 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:59,227 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:59,460 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:59,696 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:11:59,932 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 11:12:00,149 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 11:12:00,149 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 11:12:01,150 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:12:01,152 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 11:12:01,153 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 11:12:01,153 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:12:01,153 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:12:01,153 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:12:01,154 - modules.friend_request_window - INFO -    📝 备注信息: '014325110143-杨志远-2025-07-30 19:11:37'
2025-07-30 11:12:01,655 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:12:01,656 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:12:01,656 - modules.wechat_auto_add_simple - INFO - ✅ 18644881373 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:12:01,657 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18644881373
2025-07-30 11:12:01,657 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:12:03,047 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 11:12:03,048 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 11:12:03,049 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 11:12:03,050 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 11:12:03,050 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 11:12:03,051 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 11:12:03,051 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 11:12:03,051 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 11:12:03,052 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 11:12:03,052 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 11:12:03,052 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 11:12:03,054 - __main__ - INFO - � 更新全局进度：已处理 2/2905 个联系人
2025-07-30 11:12:03,055 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 11:12:06,055 - __main__ - INFO - 
============================================================
2025-07-30 11:12:06,056 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 11:12:06,056 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:12:06,056 - __main__ - INFO - 📊 全局进度：已处理 2/2905 个联系人
2025-07-30 11:12:06,057 - __main__ - INFO - ============================================================
2025-07-30 11:12:06,057 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 11:12:06,057 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 525668)
2025-07-30 11:12:06,058 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 11:12:06,058 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 11:12:06,058 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:12:06,381 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:12:06,381 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:12:06,381 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:12:06,382 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:12:06,382 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:12:06,382 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:12:06,383 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:12:06,383 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:12:06,383 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:12:06,383 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:12:06,586 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:12:06,586 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:12:06,586 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:12:06,587 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:12:06,890 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:12:06,891 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:12:06,891 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:12:06,892 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:12:06,892 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:12:06,892 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:12:06,893 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:12:06,893 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:12:06,893 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:12:06,894 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:12:07,096 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:12:07,096 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:12:07,097 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:12:07,398 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:12:07,399 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:12:07,399 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:12:07,400 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:12:07,400 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:12:07,400 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:12:07,401 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 11:12:07,401 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 11:12:08,402 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 11:12:08,402 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 11:12:08,402 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:12:08,403 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:12:08,403 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:12:08,404 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:12:08,404 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:12:08,405 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:12:08,605 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:12:08,606 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:12:10,991 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:12:10,991 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:12:10,991 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 11:12:13,856 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:12:14,058 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:12:14,058 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:12:16,442 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:12:16,442 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:12:16,443 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 11:12:19,211 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:12:19,412 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:12:19,413 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:12:21,781 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:12:21,782 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:12:21,782 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 11:12:24,652 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:12:24,853 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:12:24,853 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:12:27,239 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:12:27,240 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:12:27,240 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 11:12:30,111 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:12:30,313 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:12:30,313 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:12:32,691 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:12:32,691 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:12:32,692 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:12:32,692 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:12:32,692 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:12:32,694 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:12:32,695 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:12:32,695 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7734786, 进程: Weixin.exe)
2025-07-30 11:12:32,696 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:12:32,696 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:12:32,697 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4000242, 进程: Weixin.exe)
2025-07-30 11:12:32,700 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:12:32,702 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4000242)
2025-07-30 11:12:32,703 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4000242) - 增强版
2025-07-30 11:12:33,008 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:12:33,008 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:12:33,009 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:12:33,009 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:12:33,010 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:12:33,010 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:12:33,214 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:12:33,214 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:12:33,417 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:12:33,417 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:12:33,417 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:12:33,418 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:12:33,418 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:12:33,418 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:12:33,419 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:12:34,420 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:12:34,420 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:12:34,422 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:12:34,422 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 11:12:34,423 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7734786, 进程: Weixin.exe)
2025-07-30 11:12:34,423 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:12:34,424 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:12:34,425 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4000242, 进程: Weixin.exe)
2025-07-30 11:12:34,427 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 11:12:34,427 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:12:34,428 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:12:34,428 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:12:34,428 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 525668
2025-07-30 11:12:34,429 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 11:12:34,737 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:12:34,737 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:12:34,738 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:12:34,738 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:12:34,738 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:12:34,739 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:12:34,739 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:12:34,739 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:12:34,739 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:12:34,740 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:12:34,942 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:12:34,942 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:12:34,944 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 525668 (API返回: None)
2025-07-30 11:12:35,245 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:12:35,245 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 11:12:35,246 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 11:12:36,246 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 11:12:36,247 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 11:12:36,247 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 11:12:36,249 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_111236.log
2025-07-30 11:12:36,250 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:12:36,251 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 11:12:36,252 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 11:12:36,253 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 11:12:36,254 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 11:12:36,254 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 11:12:36,256 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 11:12:36,256 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 11:12:36,256 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 11:12:36,257 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 11:12:36,257 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 11:12:36,257 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 11:12:36,258 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 11:12:36,259 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 11:12:36,260 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:12:36,260 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4000242
2025-07-30 11:12:36,261 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4000242) - 增强版
2025-07-30 11:12:36,568 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:12:36,569 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:12:36,570 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:12:36,571 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:12:36,572 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 11:12:36,573 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:12:36,575 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 11:12:36,576 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:12:36,777 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:12:36,778 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:12:36,781 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4000242 (API返回: None)
2025-07-30 11:12:37,082 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 11:12:37,082 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 11:12:37,082 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 11:12:37,083 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 11:12:37,083 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:12:37,084 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 11:12:37,084 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 11:12:37,088 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 11:12:37,089 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 11:12:37,587 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 11:12:37,587 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:12:37,836 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2913 个
2025-07-30 11:12:37,837 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 11:12:37,837 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2911 个
2025-07-30 11:12:37,837 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2911 个 (总计: 3135 个)
2025-07-30 11:12:37,838 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 11:12:37,838 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 11:12:37,839 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:12:37,839 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 11:12:37,839 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2911
2025-07-30 11:12:37,839 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18853061046 (王清月)
2025-07-30 11:12:37,840 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:12:44,398 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18853061046
2025-07-30 11:12:44,399 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 11:12:44,399 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18853061046 执行添加朋友操作...
2025-07-30 11:12:44,399 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 11:12:44,399 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 11:12:44,400 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 11:12:44,402 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 11:12:44,406 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 11:12:44,408 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 11:12:44,410 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 11:12:44,410 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 11:12:44,411 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 11:12:44,412 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 11:12:44,413 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 11:12:44,415 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 11:12:44,419 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:12:44,423 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:12:44,426 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 11:12:44,429 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 11:12:44,431 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 11:12:44,432 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 11:12:44,434 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 11:12:44,940 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 11:12:44,942 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 11:12:44,999 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差37.18, 边缘比例0.0412
2025-07-30 11:12:45,008 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_111245.png
2025-07-30 11:12:45,010 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 11:12:45,012 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 11:12:45,013 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 11:12:45,015 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 11:12:45,016 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 11:12:45,022 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_111245.png
2025-07-30 11:12:45,025 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 11:12:45,026 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 11:12:45,028 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 11:12:45,030 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 11:12:45,031 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 11:12:45,033 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 11:12:45,034 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 11:12:45,038 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 11:12:45,048 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_111245.png
2025-07-30 11:12:45,051 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 11:12:45,053 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 11:12:45,060 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_111245.png
2025-07-30 11:12:45,083 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 11:12:45,090 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 11:12:45,094 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 11:12:45,095 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 11:12:45,401 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 11:12:46,168 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 11:12:46,170 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 11:12:46,171 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:12:46,171 - modules.wechat_auto_add_simple - INFO - ✅ 18853061046 添加朋友操作执行成功
2025-07-30 11:12:46,172 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:12:46,172 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 11:12:48,173 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 11:12:48,174 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 11:12:48,174 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 11:12:48,174 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 11:12:48,175 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 11:12:48,175 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 11:12:48,175 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 11:12:48,176 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 11:12:48,176 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 11:12:48,176 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18853061046
2025-07-30 11:12:48,177 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 11:12:48,177 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:12:48,177 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:12:48,178 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 11:12:48,178 - modules.friend_request_window - INFO -    📱 phone: '18853061046'
2025-07-30 11:12:48,178 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 11:12:48,178 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 11:12:48,659 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 11:12:48,660 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 11:12:48,660 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 11:12:48,660 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 11:12:48,662 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18853061046
2025-07-30 11:12:48,662 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 11:12:48,662 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:12:48,663 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 11:12:48,663 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 11:12:48,664 - modules.friend_request_window - INFO -    📱 手机号码: 18853061046
2025-07-30 11:12:48,664 - modules.friend_request_window - INFO -    🆔 准考证: 014325110146
2025-07-30 11:12:48,664 - modules.friend_request_window - INFO -    👤 姓名: 王清月
2025-07-30 11:12:48,664 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:12:48,665 - modules.friend_request_window - INFO -    📝 备注格式: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:12:48,665 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 11:12:48,666 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:12:48,666 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 11:12:48,669 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 9963278, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 11:12:48,674 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 9963278)
2025-07-30 11:12:48,675 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 11:12:48,675 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 11:12:48,675 - modules.friend_request_window - INFO - 🔄 激活窗口: 9963278
2025-07-30 11:12:49,378 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 11:12:49,378 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 11:12:49,379 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 11:12:49,379 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 11:12:49,379 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 11:12:49,379 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 11:12:49,380 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 11:12:49,380 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 11:12:49,380 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 11:12:49,381 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 11:12:49,381 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 11:12:49,381 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 11:12:49,381 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 11:12:49,382 - modules.friend_request_window - INFO -    📝 remark参数: '014325110146-王清月-2025-07-30 19:12:48' (类型: <class 'str'>, 长度: 36)
2025-07-30 11:12:49,382 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 11:12:49,382 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:12:49,382 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 11:12:49,383 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 11:12:49,383 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 11:12:49,383 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 11:12:49,384 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 11:12:49,385 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 11:12:49,385 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 11:12:50,306 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 11:12:55,549 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 11:12:55,549 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 11:12:55,549 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 11:12:55,550 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 11:12:55,550 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '在微信频率错误处理流程中，当程序切换到微信2窗口时，存在一个坐标定位问题：新切换的微信2窗口中的"添...' (前50字符)
2025-07-30 11:12:55,862 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:12:55,862 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:12:56,764 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:12:56,776 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:12:56,776 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 11:12:56,777 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 11:12:56,778 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 11:12:56,779 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 11:12:57,280 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 11:12:57,281 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 11:12:57,281 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 11:12:57,281 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 11:12:57,282 - modules.friend_request_window - INFO -    📝 内容: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:12:57,282 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 11:12:57,282 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110146-\xe7\x8e\x8b\xe6\xb8\x85\xe6\x9c\x88-2025-07-30 19:12:48'
2025-07-30 11:12:57,283 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 11:12:58,188 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 11:13:03,429 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 11:13:03,430 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 11:13:03,430 - modules.friend_request_window - INFO -    📝 原始文本: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:13:03,430 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 11:13:03,431 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '在微信频率错误处理流程中，当程序切换到微信2窗口时，存在一个坐标定位问题：新切换的微信2窗口中的"添...' (前50字符)
2025-07-30 11:13:03,741 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 11:13:03,741 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 11:13:04,644 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 11:13:04,654 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 11:13:04,656 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:13:04,656 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 11:13:04,657 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:13:04,657 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 11:13:05,158 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:13:05,158 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 11:13:05,159 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 11:13:05,159 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 11:13:05,159 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 11:13:05,160 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 11:13:05,160 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 11:13:05,961 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 11:13:05,961 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 11:13:05,961 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 11:13:06,569 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:13:06,570 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 11:13:06,570 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 11:13:06,570 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 11:13:07,072 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 11:13:07,074 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 11:13:07,074 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 11:13:07,075 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 11:13:07,075 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 11:13:07,075 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 11:13:07,076 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 11:13:07,076 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 11:13:07,076 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 11:13:07,076 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:13:07,077 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 11:13:07,077 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 11:13:07,077 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 11:13:07,077 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 11:13:07,078 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 11:13:07,078 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 11:13:07,079 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 11:13:07,079 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 11:13:07,080 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 11:13:07,582 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 11:13:07,582 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 11:13:07,582 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 11:13:07,583 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 11:13:07,583 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 11:13:07,583 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 11:13:07,584 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 11:13:07,584 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 11:13:08,504 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 11:13:08,505 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 11:13:08,505 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 11:13:08,505 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 11:13:08,523 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 11:13:08,524 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:13:09,324 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:13:09,325 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 11:13:09,325 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 11:13:09,326 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:13:10,126 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:13:10,127 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 11:13:10,127 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 2/2
2025-07-30 11:13:10,127 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 11:13:10,128 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 11:13:10,128 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 11:13:10,929 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 11:13:10,929 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 11:13:10,930 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 11:13:12,930 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 11:13:12,931 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:13:12,932 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 11:13:12,932 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:13:12,933 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:13:12,934 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:13:12,936 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 11:13:12,936 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 11:13:12,936 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 11:13:12,959 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 11:13:12,959 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 11:13:12,959 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 11:13:12,960 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 197994
2025-07-30 11:13:12,961 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:13:12,962 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 11:13:13,266 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:13:13,266 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:13:13,266 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 11:13:13,267 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 11:13:13,267 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 11:13:13,267 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 11:13:13,268 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 11:13:13,268 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 11:13:13,268 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 11:13:13,269 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 11:13:13,471 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:13:13,471 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:13:13,471 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 11:13:13,472 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 11:13:13,472 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 11:13:13,472 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 11:13:13,472 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 11:13:15,473 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 11:13:15,474 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:13:15,475 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 11:13:15,475 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 11:13:15,475 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 11:13:15,476 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 11:13:15,476 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 11:13:15,476 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 11:13:15,476 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 11:13:15,477 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 11:13:15,477 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 11:13:15,477 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 11:13:15,678 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 11:13:15,678 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 11:13:18,054 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 11:13:18,054 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 11:13:18,055 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 11:13:20,613 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 11:13:20,814 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 11:13:20,815 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 11:13:23,186 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 11:13:23,186 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 11:13:23,187 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 11:13:25,209 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 11:13:25,410 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 11:13:25,411 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 11:13:27,780 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 11:13:27,780 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 11:13:27,781 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 11:13:30,211 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 11:13:30,412 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 11:13:30,413 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 11:13:32,782 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 11:13:32,783 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 11:13:32,783 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 11:13:35,686 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 11:13:35,886 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 11:13:35,887 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 11:13:38,269 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 11:13:38,269 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 11:13:38,269 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:13:38,270 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:13:38,271 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 11:13:38,271 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:13:38,273 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:13:38,273 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:13:38,274 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3017820, 进程: Weixin.exe)
2025-07-30 11:13:38,277 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:13:38,277 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3017820)
2025-07-30 11:13:38,278 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3017820) - 增强版
2025-07-30 11:13:38,581 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 11:13:38,582 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 11:13:38,582 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 11:13:38,583 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 11:13:38,583 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 11:13:38,583 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 11:13:38,787 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 11:13:38,787 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 11:13:38,989 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 11:13:38,989 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 11:13:38,990 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 11:13:38,990 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 11:13:38,990 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 11:13:38,990 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 11:13:38,991 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 11:13:39,991 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 11:13:39,992 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 11:13:39,993 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 11:13:39,994 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 11:13:39,994 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 11:13:39,995 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3017820, 进程: Weixin.exe)
2025-07-30 11:13:39,998 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 11:13:39,998 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 11:13:39,999 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 11:13:39,999 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 11:13:40,000 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 11:13:40,000 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 11:13:40,001 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 11:13:40,002 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 11:13:40,003 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 11:13:40,004 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 11:13:40,005 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 11:13:40,006 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 11:13:40,006 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 11:13:40,006 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 11:13:40,007 - modules.friend_request_window - INFO -    📝 备注信息: '014325110146-王清月-2025-07-30 19:12:48'
2025-07-30 11:13:40,507 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 11:13:40,508 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:13:40,508 - modules.wechat_auto_add_simple - INFO - ✅ 18853061046 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 11:13:40,509 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18853061046
2025-07-30 11:13:40,510 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 11:13:44,034 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2911
2025-07-30 11:13:44,035 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18944948991 (周燕来)
2025-07-30 11:13:44,035 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
