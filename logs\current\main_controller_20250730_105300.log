2025-07-30 10:53:00,216 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:53:00,217 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:53:00,217 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:53:00,218 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:53:00,219 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 10:53:00,220 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:53:00,220 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:53:00,221 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:53:00,222 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:53:00,222 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:53:00,223 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:53:00,228 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:53:00,234 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_105300.log
2025-07-30 10:53:00,237 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:53:00,239 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:53:00,239 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:53:00,240 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 10:53:00,242 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 10:53:00,248 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 18:53:00
2025-07-30 10:53:00,249 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 10:53:00,250 - __main__ - INFO - 📅 启动时间: 2025-07-30 18:53:00
2025-07-30 10:53:00,256 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 10:53:00,264 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:53:00,269 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:00,279 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:53:00,287 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:00,289 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:53:00,299 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:53:00,301 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 10:53:00,302 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 10:53:00,302 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 10:53:00,303 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 10:53:01,507 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 10:53:01,509 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 10:53:01,509 - __main__ - INFO - 📋 待处理联系人数: 2905
2025-07-30 10:53:01,510 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 10:53:01,510 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2905
2025-07-30 10:53:01,511 - __main__ - INFO - 
============================================================
2025-07-30 10:53:01,511 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 10:53:01,512 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:53:01,512 - __main__ - INFO - 📊 全局进度：已处理 0/2905 个联系人
2025-07-30 10:53:01,512 - __main__ - INFO - ============================================================
2025-07-30 10:53:01,513 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 10:53:01,515 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:53:01,517 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:53:01,519 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 10:53:01,520 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:53:01,938 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 10:53:01,938 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:53:01,939 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:53:01,939 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:53:01,940 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:53:01,940 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:53:01,940 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:53:01,942 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:53:01,943 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:53:01,943 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:53:02,145 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 10:53:02,146 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:53:02,147 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:53:02,147 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:53:02,558 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 10:53:02,558 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:53:02,558 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:53:02,559 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:53:02,559 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:53:02,560 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:53:02,560 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:53:02,560 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:53:02,560 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:53:02,561 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:53:02,763 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 10:53:02,763 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:53:02,766 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:53:03,067 - modules.window_manager - WARNING - ⚠️ 窗口可能未完全置于最前面 (当前前台: 2297084)
2025-07-30 10:53:03,068 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:53:03,068 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:53:03,069 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:53:03,069 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:53:03,069 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:53:03,070 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:53:03,070 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:53:04,070 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:53:04,071 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 10:53:04,071 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:53:04,071 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:53:04,072 - modules.main_interface - WARNING - ⚠️ 当前前台窗口不是微信窗口: 'main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code' (类名: Chrome_WidgetWin_1)
2025-07-30 10:53:04,072 - modules.main_interface - INFO - 🔄 尝试查找并激活微信窗口...
2025-07-30 10:53:04,073 - modules.main_interface - INFO - 🔄 使用已初始化的window_manager查找并激活微信窗口...
2025-07-30 10:53:04,073 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:53:04,076 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:04,077 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:53:04,078 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:04,078 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:53:04,080 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:53:04,081 - modules.main_interface - INFO - 🎯 找到微信窗口: 微信 (类名: Qt51514QWindowIcon, 句柄: 197994)
2025-07-30 10:53:04,081 - modules.main_interface - INFO - 🚀 使用window_manager激活窗口（包含位置调整）...
2025-07-30 10:53:04,081 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:53:04,491 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 10:53:04,491 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:53:04,492 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:53:04,492 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:53:04,492 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:53:04,493 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:53:04,493 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:53:04,493 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:53:04,494 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:53:04,494 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:53:04,696 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 10:53:04,697 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:53:04,697 - modules.main_interface - INFO - ✅ 微信窗口激活和位置调整成功
2025-07-30 10:53:05,697 - modules.main_interface - INFO - ✅ 微信窗口已激活
2025-07-30 10:53:05,698 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:53:05,698 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:53:05,899 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:53:05,900 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:53:08,281 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:53:08,281 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:53:08,281 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 10:53:11,081 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:53:11,282 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:53:11,282 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:53:13,662 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:53:13,663 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:53:13,663 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 10:53:16,252 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:53:16,457 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:53:16,459 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:53:18,844 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:53:18,845 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:53:18,845 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 10:53:21,725 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:53:21,926 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:53:21,927 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:53:24,294 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:53:24,294 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:53:24,295 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 10:53:26,139 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:53:26,340 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:53:26,341 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:53:28,711 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:53:28,712 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:53:28,712 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:53:28,712 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:53:28,713 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:53:28,714 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:28,715 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:53:28,716 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3804044, 进程: Weixin.exe)
2025-07-30 10:53:28,717 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:28,717 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:53:28,720 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:53:28,720 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3804044)
2025-07-30 10:53:28,720 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3804044) - 增强版
2025-07-30 10:53:29,024 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:53:29,024 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:53:29,025 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:53:29,025 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:53:29,026 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 10:53:29,026 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:53:29,229 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 10:53:29,230 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:53:29,432 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:53:29,432 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:53:29,432 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 10:53:29,432 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 10:53:29,433 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 10:53:29,433 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:53:29,433 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:53:30,434 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:53:30,434 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:53:30,436 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:30,436 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:53:30,437 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3804044, 进程: Weixin.exe)
2025-07-30 10:53:30,438 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:53:30,438 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:53:30,441 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 10:53:30,441 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 10:53:30,443 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 10:53:30,443 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:53:30,443 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:53:30,444 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:53:30,755 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:53:30,756 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:53:30,757 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:53:30,757 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:53:30,758 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:53:30,758 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:53:30,759 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:53:30,759 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:53:30,760 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:53:30,760 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:53:30,962 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:53:30,963 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:53:30,966 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:53:31,267 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:53:31,267 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 10:53:31,268 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 10:53:32,268 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 10:53:32,268 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 10:53:32,269 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 10:53:32,271 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_105332.log
2025-07-30 10:53:32,274 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:53:32,274 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:53:32,275 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:53:32,276 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 10:53:32,276 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 10:53:32,276 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 10:53:32,278 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 10:53:32,279 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 10:53:32,279 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 10:53:32,279 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 10:53:32,280 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 10:53:32,280 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 10:53:32,281 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 10:53:32,282 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:53:32,283 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3804044
2025-07-30 10:53:32,283 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3804044) - 增强版
2025-07-30 10:53:32,602 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:53:32,603 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:53:32,604 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 10:53:32,604 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 10:53:32,604 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 10:53:32,605 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 10:53:32,605 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 10:53:32,606 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 10:53:32,809 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:53:32,809 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:53:32,813 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3804044 (API返回: None)
2025-07-30 10:53:33,114 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:53:33,114 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 10:53:33,115 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 10:53:33,115 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 10:53:33,116 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:53:33,116 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 10:53:33,116 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 10:53:33,120 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 10:53:33,121 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 10:53:33,605 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 10:53:33,605 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:53:33,858 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2921 个
2025-07-30 10:53:33,859 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2921 个 (总计: 3135 个)
2025-07-30 10:53:33,859 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 10:53:33,859 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 10:53:33,860 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:53:33,860 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 10:53:33,860 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2921
2025-07-30 10:53:33,860 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 19104446711 (孟庆军)
2025-07-30 10:53:33,861 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:53:40,518 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 19104446711
2025-07-30 10:53:40,518 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 10:53:40,519 - modules.wechat_auto_add_simple - INFO - 👥 开始为 19104446711 执行添加朋友操作...
2025-07-30 10:53:40,519 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 10:53:40,519 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 10:53:40,520 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:53:40,521 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 10:53:40,526 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 10:53:40,527 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 10:53:40,528 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 10:53:40,528 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 10:53:40,528 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 10:53:40,529 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 10:53:40,529 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 10:53:40,530 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 10:53:40,534 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 10:53:40,535 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:53:40,542 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 10:53:40,545 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 2060x977
2025-07-30 10:53:40,547 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 10:53:40,548 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 10:53:41,050 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 10:53:41,051 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 10:53:41,115 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差41.09, 边缘比例0.0401
2025-07-30 10:53:41,123 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_105341.png
2025-07-30 10:53:41,125 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 10:53:41,127 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 10:53:41,128 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 10:53:41,133 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 10:53:41,134 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 10:53:41,138 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_105341.png
2025-07-30 10:53:41,141 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 10:53:41,142 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 10:53:41,145 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 10:53:41,146 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 10:53:41,150 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 10:53:41,151 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 10:53:41,152 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 10:53:41,153 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 10:53:41,161 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_105341.png
2025-07-30 10:53:41,165 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 10:53:41,166 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 10:53:41,172 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_105341.png
2025-07-30 10:53:41,229 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 10:53:41,231 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 10:53:41,231 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 10:53:41,232 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 10:53:41,533 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 10:53:42,300 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 10:53:42,301 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 10:53:42,302 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:53:42,303 - modules.wechat_auto_add_simple - INFO - ✅ 19104446711 添加朋友操作执行成功
2025-07-30 10:53:42,303 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:53:42,303 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 10:53:44,304 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 10:53:44,305 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 10:53:44,305 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 10:53:44,306 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:53:44,306 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:53:44,306 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:53:44,307 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:53:44,307 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:53:44,307 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 10:53:44,308 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 19104446711
2025-07-30 10:53:44,311 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 10:53:44,312 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:53:44,312 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:53:44,313 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 10:53:44,314 - modules.friend_request_window - INFO -    📱 phone: '19104446711'
2025-07-30 10:53:44,314 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 10:53:44,314 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 10:53:44,816 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 10:53:44,817 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 10:53:44,817 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 10:53:44,817 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 10:53:44,819 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 19104446711
2025-07-30 10:53:44,819 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 10:53:44,820 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:53:44,821 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 10:53:44,822 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 10:53:44,822 - modules.friend_request_window - INFO -    📱 手机号码: 19104446711
2025-07-30 10:53:44,822 - modules.friend_request_window - INFO -    🆔 准考证: 014325110135
2025-07-30 10:53:44,824 - modules.friend_request_window - INFO -    👤 姓名: 孟庆军
2025-07-30 10:53:44,824 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:53:44,825 - modules.friend_request_window - INFO -    📝 备注格式: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:53:44,825 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 10:53:44,825 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:53:44,827 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 10:53:44,828 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8259658, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 10:53:44,833 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8259658)
2025-07-30 10:53:44,835 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 10:53:44,835 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 10:53:44,836 - modules.friend_request_window - INFO - 🔄 激活窗口: 8259658
2025-07-30 10:53:45,539 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 10:53:45,539 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 10:53:45,540 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 10:53:45,540 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 10:53:45,541 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 10:53:45,541 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 10:53:45,541 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 10:53:45,541 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 10:53:45,542 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 10:53:45,542 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 10:53:45,542 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 10:53:45,542 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 10:53:45,543 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 10:53:45,543 - modules.friend_request_window - INFO -    📝 remark参数: '014325110135-孟庆军-2025-07-30 18:53:44' (类型: <class 'str'>, 长度: 36)
2025-07-30 10:53:45,543 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 10:53:45,543 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:53:45,544 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 10:53:45,544 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 10:53:45,545 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 10:53:45,545 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 10:53:45,545 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 10:53:45,546 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 10:53:45,546 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 10:53:46,458 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 10:53:51,703 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 10:53:51,704 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 10:53:51,705 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 10:53:51,706 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 10:53:51,708 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 10:47:54,245 - modules.wechat_auto_add_...' (前50字符)
2025-07-30 10:53:52,022 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:53:52,022 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:53:52,924 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:53:52,933 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:53:52,933 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 10:53:52,935 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 10:53:52,935 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 10:53:52,936 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 10:53:53,436 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 10:53:53,436 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 10:53:53,437 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 10:53:53,437 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 10:53:53,438 - modules.friend_request_window - INFO -    📝 内容: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:53:53,438 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 10:53:53,439 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110135-\xe5\xad\x9f\xe5\xba\x86\xe5\x86\x9b-2025-07-30 18:53:44'
2025-07-30 10:53:53,439 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 10:53:54,357 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 10:53:59,601 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 10:53:59,601 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 10:53:59,601 - modules.friend_request_window - INFO -    📝 原始文本: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:53:59,602 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 10:53:59,602 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 10:47:54,245 - modules.wechat_auto_add_...' (前50字符)
2025-07-30 10:53:59,910 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 10:53:59,911 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 10:54:00,813 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 10:54:00,824 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 10:54:00,825 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:54:00,826 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 10:54:00,826 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:54:00,827 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 10:54:01,328 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:54:01,328 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 10:54:01,328 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 10:54:01,329 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 10:54:01,329 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 10:54:01,329 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 10:54:01,330 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 10:54:02,130 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 10:54:02,131 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 10:54:02,131 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 10:54:02,741 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:54:02,741 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 10:54:02,741 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 10:54:02,741 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 10:54:03,243 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 10:54:03,244 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 10:54:03,245 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 10:54:03,245 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 10:54:03,245 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 10:54:03,246 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 10:54:03,246 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 10:54:03,246 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 10:54:03,246 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 10:54:03,247 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:54:03,247 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 10:54:03,247 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 10:54:03,247 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 10:54:03,248 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 10:54:03,248 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 10:54:03,249 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 10:54:03,249 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 10:54:03,250 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 10:54:03,251 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 10:54:03,752 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 10:54:03,753 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 10:54:03,753 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 10:54:03,754 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 10:54:03,754 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 10:54:03,754 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 10:54:03,754 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 10:54:03,755 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 10:54:04,675 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 10:54:04,676 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 10:54:04,676 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 10:54:04,676 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 10:54:04,692 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 10:54:04,693 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 10:54:05,494 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 10:54:05,494 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 10:54:05,494 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 10:54:05,495 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 10:54:05,495 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 10:54:05,495 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 10:54:06,296 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 10:54:06,297 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 10:54:06,297 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 10:54:08,298 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 10:54:08,299 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:54:08,299 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 10:54:08,300 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:08,301 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:08,301 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:08,303 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:08,304 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 10:54:08,304 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 10:54:08,322 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 10:54:08,323 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 10:54:08,323 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 10:54:08,323 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 525668
2025-07-30 10:54:08,324 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:54:08,325 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525668) - 增强版
2025-07-30 10:54:08,630 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:54:08,630 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:54:08,630 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:54:08,631 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:54:08,631 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:54:08,631 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:54:08,632 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:54:08,632 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:54:08,632 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:54:08,633 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:54:08,834 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:54:08,834 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:54:08,835 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 10:54:08,835 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 10:54:08,835 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 10:54:08,836 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 10:54:08,836 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 10:54:10,837 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 10:54:10,838 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:54:10,838 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:54:10,839 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 10:54:10,839 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 10:54:10,839 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 10:54:10,839 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:54:10,840 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:54:10,840 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:54:10,840 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:54:10,841 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:54:10,841 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:54:11,041 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:54:11,042 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:54:13,423 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:54:13,424 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:54:13,424 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 10:54:15,438 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 10:54:15,640 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 10:54:15,640 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 10:54:18,024 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 10:54:18,024 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 10:54:18,026 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 10:54:19,763 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 10:54:19,964 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 10:54:19,965 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 10:54:22,344 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 10:54:22,345 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 10:54:22,345 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 10:54:24,709 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 10:54:24,910 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 10:54:24,911 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 10:54:27,292 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 10:54:27,293 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 10:54:27,293 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 10:54:29,518 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 10:54:29,719 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 10:54:29,720 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 10:54:32,089 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 10:54:32,089 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 10:54:32,090 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:54:32,091 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:54:32,091 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 10:54:32,091 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:32,093 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:32,093 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:32,096 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:32,596 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:32,598 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:32,598 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:32,604 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:33,108 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:33,110 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:33,111 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:33,114 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:33,614 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:33,616 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:33,616 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:33,621 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:34,122 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:34,124 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:34,124 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:34,126 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:34,627 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:34,629 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:34,629 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:34,632 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:35,132 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:35,134 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:35,134 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:35,137 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:35,638 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:35,639 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:35,640 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:35,642 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:36,142 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:36,144 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:36,144 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:36,146 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:36,647 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:36,649 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:36,650 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:36,653 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:37,154 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:37,156 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:37,156 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:37,158 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:37,658 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:37,660 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:37,660 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:37,663 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:38,164 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:38,166 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:38,166 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:38,169 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:38,670 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:38,671 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:38,672 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:38,676 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:39,177 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:39,178 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:39,179 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:39,181 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:39,682 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:39,684 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:39,685 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:39,687 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:40,188 - modules.window_manager - WARNING - ⚠️ 在8秒内未找到添加朋友窗口
2025-07-30 10:54:40,188 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未在指定时间内出现
2025-07-30 10:54:40,189 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未出现，但点击操作已执行
2025-07-30 10:54:40,189 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 10:54:40,190 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 10:54:41,190 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 10:54:41,192 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:54:41,192 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:54:41,194 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:54:41,194 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:54:41,196 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 10:54:41,197 - modules.main_interface - WARNING - ⚠️ 未找到添加朋友窗口
2025-07-30 10:54:41,197 - modules.main_interface - WARNING - ⚠️ [主界面操作] 添加朋友窗口验证失败，但继续执行
2025-07-30 10:54:41,198 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 10:54:41,198 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 10:54:41,198 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 10:54:41,199 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 10:54:41,199 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 10:54:41,200 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 10:54:41,200 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 10:54:41,200 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 10:54:41,200 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 10:54:41,201 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 10:54:41,201 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 10:54:41,203 - modules.friend_request_window - INFO -    📝 备注信息: '014325110135-孟庆军-2025-07-30 18:53:44'
2025-07-30 10:54:41,704 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 10:54:41,705 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:54:41,705 - modules.wechat_auto_add_simple - INFO - ✅ 19104446711 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 10:54:41,705 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 19104446711
2025-07-30 10:54:41,707 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:54:45,384 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2921
2025-07-30 10:54:45,385 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13783597105 (赵甲琪)
2025-07-30 10:54:45,385 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
